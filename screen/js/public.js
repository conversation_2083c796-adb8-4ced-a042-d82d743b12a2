// JavaScript Document
var jumpTo = function( jumpUrl){
    window.location.href = jumpUrl;
    return false;
}
var uniAjax = function( url , data){
    var rsJson = '';
    $.ajax({
        url:url,
        data:data,
        type:'POST',
        dataType:'json',
        async:false,
        success:function( data){
            rsJson = data;
        },
        error:function(data){
            if(data.responseText){
                rsJson = JSON.parse(data.responseText);
            }else{
                rsJson = {};
            }

        }
    });
    return rsJson;
};
var formatDate = function (nowtime) {
    var now = new Date(nowtime);
    var year=now.getFullYear();
    var month=now.getMonth()+1;
    var date=now.getDate();
    var hour=now.getHours();
    var minute=now.getMinutes();
    var second=now.getSeconds();
    return year+"-"+month+"-"+date+" "+hour+":"+minute+":"+second;
}
var uniIsnull = function( par){
    if( par == '' || typeof(par) == 'undefined' || par == null)
        return true;
    return false;
}
var showError = function( errMsg , jumpUrl){
    alert( errMsg);
    if( !uniIsnull( jumpUrl))
        window.location = jumpUrl;
}

var showSuccess = function( msg , jumpUrl){
    alert( msg);
    if( !uniIsnull( jumpUrl))
        window.location = jumpUrl;
}
var baseUrl = function () {
  var url = window.location.href;
  url = url.toLowerCase();
  if (url.indexOf('wmsbe.test.yunkesys') > -1) {
    return 'http://wmsbeapi.test.yunkesys.net/api/';
  } else if (url.indexOf('wmsbe.dev.yunkesys') > -1) {
    return 'http://wmsbeapi.dev.yunkesys.com/api/';
  }
  return 'https://wmsbeapi.yunkesys.com/api/';
}
