<!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<meta http-equiv="author" content="EDT BE" />
<meta http-equiv="copyright" content="EDT BE" />
<meta http-equiv="pragma" content="no-cache" />
<meta name="apple-mobile-web-app-capable" content="yes" />
<meta name="apple-touch-fullscreen" content="yes">
<meta name="apple-mobile-web-app-status-bar-style" content="black" />
<meta name="MobileOptimized" content="800">
<meta name="format-detection" content="telephone=no"/>
<meta name="viewport" id="viewport" content="width=device-width, minimum-scale=1, maximum-scale=1, initial-scale=1.0,user-scalable=no" />
<title>EDT BE</title>
<link rel="stylesheet" href="css/css.css?id=1">
<script src="js/jquery.js"></script>
<script src="js/vue.js"></script>
<script src="js/axios.min.js"></script>
	<script src="js/public.js?ie=2"></script>
	<script src="js/lang.js"></script>
<script type="text/javascript">
    var phoneWidth = parseInt(window.screen.width);
    var phoneScale = phoneWidth / 750;
    var ua = navigator.userAgent;
    if (/Android (\d+\.\d+)/.test(ua)) {
        var version = parseFloat(RegExp.$1);
        if (version > 2.3) {
            document
                    .write('<meta name="viewport" content="width=750, minimum-scale = '+phoneScale+', maximum-scale = '+phoneScale+', target-densitydpi=device-dpi">');
        } else {
            document
                    .write('<meta name="viewport" content="width=750, target-densitydpi=device-dpi">');
        }
    } else {
        document
                .write('<meta name="viewport" content="width=750, user-scalable=no, target-densitydpi=device-dpi">');
    }
</script>

</head>
<body class="bg_white">
	<div id="app" v-cloak>
		<div class="lang_div">
			<select v-model="language" @change="changeLang">
				<option value="EN">EN</option>
				<option value="FR">FR</option>
				<option value="ANG">ANG</option>
				<option value="DE">DE</option>
				<option value="PL">PL</option>
				<option value="SLO">SLO</option>
				<option value="RUS">RUS</option>
				<option value="HU">HU</option>
        <option value="CZ">CZ</option>
        <option value="BG">BG</option>
        <option value="RO">RO</option>
        <option value="LT">LT</option>
        <option value="ES">ES</option>
        <option value="SK">SK</option>
			</select>
		</div>
		<div :class="radio_class" style="width:690px; margin:0 auto; border-bottom:2px solid #EAEAEA; padding-top:20px;height: 72px;padding-bottom: 10px;">
			<div class="radio left_radio" @click="changeRadio('Van')">
			</div>
			<div class="radio right_radio" @click="changeRadio('Truck')">
			</div>
		</div>
		<div class="edit_div">
			<div class="tit">{{languageArr.create_date}}</div>
			<div class="edit_inp">
				<input type="text" class="inp" v-model="froms.create_date" placeholder="" />
			</div>
		</div>
		<div class="edit_div">
			<div class="tit">{{languageArr.name}}</div>
			<div class="edit_inp">
				<input type="text" class="inp" v-model="froms.name" placeholder="" />
			</div>
		</div>
		<div class="edit_div">
			<div class="tit">{{languageArr.car_number}}</div>
			<div class="edit_inp">
				<input type="text" class="inp" v-model="froms.car_number" placeholder="" />
			</div>
		</div>
		<div class="edit_div" v-if="plate_show">
			<div class="tit">{{languageArr.trailer_plate}}</div>
			<div class="edit_inp">
				<input type="text" class="inp" v-model="froms.trailer_plate" placeholder="" />
			</div>
		</div>
		<div class="edit_div">
			<div class="tit">{{languageArr.gsname}}</div>
			<div class="edit_inp">
				<input type="text" class="inp" v-model="froms.gsname" placeholder="" />
			</div>
		</div>
		<div class="edit_div">
			<div class="tit">{{languageArr.tel}}</div>
			<div class="edit_inp">
				<input type="text" class="inp" v-model="froms.tel" placeholder="" />
			</div>
		</div>
		<div class="footer_btn"  @click="submits()">{{languageArr.submit}}</div>
	</div>
	<script>
    console.log(window.location.href);

		Vue.config.devtools = true;
		var defalutData = {
			create_date:"",
			name:"",
			car_number: "",
			gsname: "",
			tel: "",
			type: "Van",
			sort: 1,
			trailer_plate: ""
		};
		var vm = new Vue({
			el: '#app',
			data: {
				froms: Object.assign({}, defalutData),
				radio_class: "radio_div_van",
				plate_show: false,
				language:"EN",
				languageArr:{
					create_date:"DATE",
					name:"Driver Name",
					car_number: "Truck Plate",
					trailer_plate: "Trailer Plate",
					gsname: "Company Name",
					tel: "Telephone",
					submit: "Submit"
				},
				languageArrError:{
					name:"Driver Name Is Required",
					car_number: "TRUCK PLATE Is Required",
					gsname: "Company Name Is Required",
					tel: "Telephone Is Required"
				}
			},
			methods: {
				init: function (_this) {
					this.froms = Object.assign({}, defalutData);
					this.froms.create_date = this.initDate();
				},
				changeLang: function () {
					this.languageArr = lang[this.language]['languageArr'];
					this.languageArrError = lang[this.language]['languageArrError'];
				},
				changeRadio: function (str){
					this.froms.type = str;
					if(this.froms.type=='Van'){
						this.froms.trailer_plate = '';
						this.plate_show = false;
						this.radio_class = 'radio_div_van'
					}else{
						this.plate_show = true;
						this.radio_class = 'radio_div_truck'
					}
				},
				initDate: function (){
					var date = new Date();
					var year = date.getFullYear();
					var month = date.getMonth() + 1;
					var day = date.getDate();
					month = (month > 9) ? month : ("0" + month);
					day = (day < 10) ? ("0" + day) : day;
					return year + "-" + month + "-" + day;
				},
				submits: function () {
					if(this.froms.name==''){
						showError( this.languageArrError.name);
						return false;
					}
					if(this.froms.car_number==''){
						showError( this.languageArrError.car_number);
						return false;
					}
					if(this.froms.gsname==''){
						showError( this.languageArrError.gsname);
						return false;
					}
					if(this.froms.tel==''){
						showError( this.languageArrError.tel);
						return false;
					}

					var _this = this;
					var requesUrl =  baseUrl()+"pickup/save";
					rsJson = uniAjax(requesUrl, this.froms);
					if( rsJson.status == 'success'){
						showSuccess("Success");
						_this.init();
					}else{
						showError("Error")
					}
				}
			},
			mounted: function () {
				var _this = this;
				this.init(_this);
			}
		})
	</script>
</body>
</html>
