<!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<meta http-equiv="author" content="EDT BE" />
<meta http-equiv="copyright" content="EDT BE" />
<title>EDT BE</title>
<link rel="stylesheet" href="css/css_pc.css?t=5">
<script src="js/jquery.js"></script>
<script src="js/vue.js"></script>
<script src="js/axios.min.js"></script>
<script src="js/public.js"></script>
  <style>
    body div{
      max-width: 100%;
    }
  </style>
</head>
<body class="dp_bg">
<div id="app" v-cloak>
	<div class="dp_main_div">
		<div class="dp_lists">
			<table vspace="0" cellpadding="0" cellspacing="0" width="100%">
				<tr class="t_header">
					<td width="245px">Dock Name</td>
					<td width="245px">Truck Plate</td>
					<td width="245px">Trailer Plate</td>
					<td width="245px" style="text-align: left">Driver Name</td>
				</tr>
				<tbody class="t_body">
					<tr v-for="(item, index) in lists" >
						<td class="tr_css">{{item.name}}</td>
						<td class="tr_css">{{item.car_number}}</td>
						<td class="tr_css">{{item.trailer_plate}}</td>
						<td class="tr_css" style="text-align: left">{{item.sjname}}</td>
					</tr>
				</tbody>
			</table>
		</div>
	</div>
	<div class="ge_main_div">
		<div class="ge_lists">
			<table vspace="0" cellpadding="0" cellspacing="0" width="100%">
				<tr class="t_header">
					<td width="245px">Dock Name</td>
					<td width="245px">Truck Plate</td>
					<td width="245px">Trailer Plate</td>
					<td width="245px" style="text-align: left">Driver Name</td>
				</tr>
				<tbody class="t_body">
				<tr v-for="(item, index) in lists2" >
					<td class="tr_css">{{item.name}}</td>
					<td class="tr_css">{{item.car_number}}</td>
					<td class="tr_css">{{item.trailer_plate}}</td>
					<td class="tr_css" style="text-align: left">{{item.sjname}}</td>
				</tr>
				</tbody>
			</table>
		</div>
	</div>
</div>
	<script>
		Vue.config.devtools = true;
		var vm = new Vue({
			el: '#app',
			data: {
				lists: [],
				lists_2: [],
				uptime: false,
				getList:false,
				showPage:2,
				lists2:[],
				lists2_2:[],
				page:1
			},
			methods: {
				init: function (_this) {
					var _this = this;
					_this.getlist();
					setInterval(() => {
						this.uptime = false;
						this.getList = true;
						$(".t_body").fadeOut(500,function (){
							if(_this.getList){
								_this.getList = false;
								_this.getlist();
							}
						});
					}, 10000);
				},
				getlist: function () {
					var _this = this;
					if(_this.showPage==1){
						_this.lists = _this.lists_2;
						_this.lists2 = _this.lists2_2;
						$(".t_body").fadeIn(500,function (){
							_this.uptime = true;
						});
						_this.showPage = 2;
					}else{
						var requesUrl =  baseUrl()+"pickup/showIndex";
						rsJson = uniAjax(requesUrl, []);
						if(rsJson.status=="success"){
							_this.showPage=1;
							_this.lists = rsJson.result.plat1;
							_this.lists_2 = rsJson.result.plat2;
							_this.lists2 = rsJson.result.gate1;
							_this.lists2_2 = rsJson.result.gate2;
							$(".t_body").fadeIn(500,function (){
								_this.uptime = true;
							});
						}
					}
				}
			},
			mounted: function () {
				var _this = this;
				this.init(_this);
			}
		})
	</script>
</body>
</html>
