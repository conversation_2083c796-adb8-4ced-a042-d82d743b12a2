@charset "utf-8";

body {
	font-family: '微软雅黑 Bold', '微软雅黑 Regular', '微软雅黑';
	width:100%;
	height:100%	;
    background: #f9f9f9;
	margin: 0px;
	padding: 0px 0px 100px 0px;
}
.bg_white{
	background:#FFFFFF;
}
.no_pad_b{
	padding-bottom:0;
}
ul,dl,dd,h1,h2,h3,h4,h5,h6,form,p { 
	padding:0; 
	margin:0;
}
a{
	outline:none;
}
p {
	margin: 0px;
	padding: 0px;
}
textarea {
	margin: 0px;
	padding: 0px;
}
input {
	margin: 0px;
	padding: 0px;
}

input::-webkit-input-placeholder { 
/* WebKit browsers */ 
color: #FFFFFF; 
} 
input:-moz-placeholder { 
/* Mozilla Firefox 4 to 18 */ 
color: #FFFFFF; 
} 
input::-moz-placeholder { 
/* Mozilla Firefox 19+ */ 
color: #FFFFFF; 
} 
input:-ms-input-placeholder { 
/* Internet Explorer 10+ */ 
color: #FFFFFF; 
}
form {
	margin: 0px;
	padding: 0px;
}
h1,h2,h3,h4,h5,h6{
	margin: 0px;
	padding: 0px;
}
ul,li,div {
	margin: 0px;
	padding: 0px;
}
img {
	border:none;
	padding: 0;
	margin: 0;
}
ul li {
	list-style-type: none;
}
a {
	text-decoration: none;
}
button{margin: 0;padding: 0;border: none;}

.lang_div{
	width:690px;
	margin:0 auto;
	padding-top:20px;
	padding-bottom:30px;
}
.lang_div select{
	height:50px;
	font-size:32px;
	padding-left:0px;
	border:none;
	border-radius:0px;
	background:none;
	color: #666666;
	width: 100px;
	float: right;
}
.lang_div select option{
	font-size:18px;
	line-height: 30px;
}

.edit_div{
	width:690px;
	margin:0 auto;
	border-bottom:2px solid #EAEAEA;
	padding-top:20px;
	padding-bottom:30px;
}
.radio_div_truck .radio{
	width: 340px;
	height: 72px;
	float: left;
	background-image: url("../images/van.png");
	background-repeat: no-repeat;
	background-position: center bottom;
}
.radio_div_truck .radio:last-child{
	float: right;
	background-image: url("../images/truck_s.png");
}
.radio_div_van .radio{
	width: 340px;
	height: 72px;
	float: left;
	background-image: url("../images/van_s.png");
	background-repeat: no-repeat;
	background-position: center bottom;
}
.radio_div_van .radio:last-child{
	float: right;
	background-image: url("../images/truck.png");
}


.edit_div .tit{
	font-size:28px;
	color:#666666;
	line-height:50px;
}
.edit_div .tit .tit_r{
	float:right;
}

.edit_div .tit .tit_r span{
	font-size:28px;
	color:#333333;
	line-height:50px;
}
.edit_div .tit .tit_r label{
	line-height:50px;
	padding:0 20px;
	cursor:pointer;
	margin-right:10px;
}
.edit_div .edit_inp{
	height:50px;
}
.edit_div .edit_inp .inp{
	width:690px;
	height:50px;
	border:none;
	outline:none;
	display:block;
	line-height:50px;
	font-size:32px;
	color:#666666;
}
.edit_div .edit_inp .inp_m{
	width:490px;
	height:50px;
	border:none;
	outline:none;
	display:block;
	line-height:50px;
	font-size:32px;
	color:#666666;
	float:left;
}
.code_btn{
	height:50px;
	width:170px;
	text-align:center;
	line-height:50px;
	background:#39A0FF;
	outline:none;
	border-radius:8px;
	display:block;
	overflow:hidden;
	font-size:24px;
	color:#FFFFFF;
	font-weight:400;
	cursor:pointer;
	float:right;
}
.unit_lab{
	line-height:50px;
	font-size:32px;
	color:#BBBBBB;
	float:right;
}
.edit_div .edit_inp input::-webkit-input-placeholder { 
/* WebKit browsers */ 
color: #666666; 
} 
.edit_div .edit_inp input:-moz-placeholder { 
/* Mozilla Firefox 4 to 18 */ 
color: #666666; 
} 
.edit_div .edit_inp input::-moz-placeholder { 
/* Mozilla Firefox 19+ */ 
color: #666666; 
} 
.edit_div .edit_inp input:-ms-input-placeholder { 
/* Internet Explorer 10+ */ 
color: #666666; 
}
.edit_div .select_div{
	padding:0px;
	height:50px;
}
.edit_div .select_div select{
	height:50px;
	font-size:32px;
	padding-left:0px;
	border:none;
	border-radius:0px;
	background:none;
	color: #666666; 
}
.edit_div .select_div .select_m{
	width:100%;
}

.clear{
	display:block;
	clear:both;
}
/*底部菜单样式*/
.footer_btn{
	margin:50px auto 0 auto;
	height:98px;
	width:700px;
	z-index:999;
	background:#4a97ff;
	text-align:center;
	line-height:98px;
	font-size:32px;
	color:#FFFFFF;
	cursor:pointer;
}
.pc_bg{
	background:#F4F8F9;
}
.main_div{
	width:1824px;
	height:984px;
	margin:48px auto 0 auto;
	background:#FFFFFF;
	border-radius: 16px;
}
.lists{

}