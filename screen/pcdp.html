<!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<meta http-equiv="author" content="EDT BE" />
<meta http-equiv="copyright" content="EDT BE" />
<title>EDT BE</title>
<link rel="stylesheet" href="css/css_pc.css?t=30">
<script src="js/jquery.js"></script>
<script src="js/vue.js"></script>
<script src="js/axios.min.js"></script>
<script src="js/public.js"></script>
  <style>
    body div{
      max-width: 100%;
    }
    body{overflow-y: hidden;}
  </style>
</head>
<body class="pc_bg">
	<div class="main_div" id="app" v-cloak>
		<div class="lists">
			<table vspace="0" cellpadding="0" cellspacing="0" width="100%">
				<tr class="t_header">
					<td width="245px">Dock Name</td>
					<td width="265px">Truck Plate</td>
					<td width="265px">Trailer Plate</td>
					<td width="145px">Timimg</td>
          <td width="580px">Remark</td>
          <td width="205px">CMR</td>
				</tr>
				<tbody class="t_body">
					<tr v-for="(item, index) in lists" >
						<td class="tr_css">{{item.name}}</td>
						<td class="tr_css">{{item.car_number}}</td>
						<td class="tr_css">{{item.trailer_plate}}</td>
						<td :class="classTime(item.last_time)">{{formatTime(item.last_time)}}</td>
            <td class="tr_css" v-html="item.remark"></td>
            <td class="tr_css" v-html="item.cmr"></td>
					</tr>
				</tbody>
			</table>
		</div>
	</div>
	<script>
		Vue.config.devtools = true;
		var vm = new Vue({
			el: '#app',
			data: {
				lists: [],
				list2:[],
				pageCount:1,
				showPage:1,
				uptime: false,
				getList:false
			},
			methods: {
				init: function (_this) {
					var _this = this;
					_this.getlist();
					setInterval(() => {
						this.uptime = false;
						this.getList = true;
						$(".t_body").fadeOut(500,function (){
							if(_this.getList){
								_this.getList = false;
								_this.getlist();
							}
						});
					}, 10000);
					setInterval(() => {
						_this.updateTime();
					}, 1000);
				},
				updateTime: function (){
					for(let i =0;i<this.lists.length;i++){
						this.lists[i].last_time = this.lists[i].last_time-1;
					}
					for(let i =0;i<this.list2.length;i++){
						this.list2[i].last_time = this.list2[i].last_time-1;
					}
				},
				classTime: function (time){
					if(time<0){
						return "tr_css minus"
					}else{
						return "tr_css plus"
					}
				},
				formatTime: function (time){
					var top = '';
					if(time<0){
						top = '-';
						time = time*(-1);
					}
					var m = parseInt(time/60);
					var s = time - m*60;
					if(m<10){
						m = "0"+m
					}
					if(s<10){
						s = "0"+s
					}
					return top+m+":"+s;
				},
				getlist: function () {
					var _this = this;
					if(_this.showPage==_this.pageCount){
						var requesUrl =  baseUrl()+"pickup/showIndexAddress";
						rsJson = uniAjax(requesUrl, []);
						if(rsJson.status=="success"){
							_this.showPage=1;
							_this.pageCount = rsJson.result.pageCount;
							_this.lists = rsJson.result.plat1;
							_this.list2 = rsJson.result.plat2;
							$(".t_body").fadeIn(500,function (){
								_this.uptime = true;
							});
						}
					}else{
						_this.showPage = 2;
						_this.lists = _this.list2;
						$(".t_body").fadeIn(500,function (){
							_this.uptime = true;
						});
					}
				}
			},
			mounted: function () {
				var _this = this;
				this.init(_this);
			}
		})
	</script>
</body>
</html>
