stages:
    - build-dev
    - build-test
    - build-release

releaseEnvJob:
  stage: build-release
  tags:
    - wmsbe-frontend-prd
  only:
    - release
  script:
    - cd /data/docker/nginx/html/wmsbe.yunkesys.com
    - git pull
    - docker run --rm -v $(pwd):$(pwd) -w $(pwd) node:14 npm install
    - docker run --rm -v $(pwd):$(pwd) -w $(pwd) node:14 npm run build:release
    - cp -rf ./screen ./dist


testEnvJob:
    stage: build-test
    tags:
        - wmsbe-frontend-test
    only:
        - test
    script:
        - cd /data/docker/nginx/html/wmsbe.test.yunkesys.net
        - git pull
        - docker run --rm -v $(pwd):$(pwd) -w $(pwd) node:14 npm install
        - docker run --rm -v $(pwd):$(pwd) -w $(pwd) node:14 npm run build:test
        - cp -rf ./screen ./dist

devEnvJob:
  stage: build-dev
  tags:
    - be-wms-front-dev
  only:
    - dev
  script:
    - cd /data/docker/nginx/html/wmsbe.dev.yunkesys.com
    - git pull
    - source ~/.bashrc
    - set_proxy
    - npm build
    - npm run build:dev
    - cp -rf ./screen ./dist
