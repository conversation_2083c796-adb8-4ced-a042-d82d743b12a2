const CompressionWebpackPlugin = require('compression-webpack-plugin');
const { transformElementScss } = require('ele-admin/lib/utils/dynamic-theme');

module.exports = {
  productionSourceMap: false,
  transpileDependencies: ['element-ui', 'ele-admin', 'vue-i18n'],
  lintOnSave: false,
  devServer: {
    proxy: {
      '/admin': {
        // target: 'http://wms.dev.yunkesys.com/api/',
        target: 'http://wmsbeapi.dev.yunkesys.com/api/',
        // target: 'http://127.0.0.1:8000/api/',
        // changeOrigin: true,  //是否跨域
        pathRewrite: {
          '^/admin': ''
        }
      }
    }
  },
  chainWebpack(config) {
    config.plugins.delete('prefetch');
    if (process.env.NODE_ENV !== 'development') {
      // 对超过10kb的文件gzip压缩
      config.plugin('compressionPlugin').use(
        new CompressionWebpackPlugin({
          test: /\.(js|css|html)$/,
          threshold: 10240
        })
      );
    }
  },
  css: {
    loaderOptions: {
      sass: {
        sassOptions: {
          outputStyle: 'expanded',
          importer: transformElementScss()
        }
      }
    }
  }
};
