{"name": "ele-admin-template", "version": "1.8.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "serve:test": "vue-cli-service serve --mode test", "serve:fr": "vue-cli-service serve --mode fr", "build": "vue-cli-service build", "build:dev": "vue-cli-service build --mode dev", "build:test": "vue-cli-service build --mode teser", "build:release": "vue-cli-service build --mode release", "build:fr": "vue-cli-service build --mode fr", "lint": "vue-cli-service lint", "clean:lib": "rimraf node_modules"}, "dependencies": {"@amap/amap-jsapi-loader": "^1.0.1", "@ant-design/colors": "^6.0.0", "@bytemd/plugin-gfm": "^1.11.0", "@bytemd/vue": "^1.11.0", "@tinymce/tinymce-vue": "^3.2.8", "@vue/composition-api": "^1.4.9", "ali-oss": "^6.17.1", "axios": "^0.26.0", "core-js": "^3.21.1", "countup.js": "^2.0.8", "cropperjs": "^1.5.12", "echarts": "^5.3.0", "echarts-wordcloud": "^2.0.0", "ele-admin": "^1.9.3", "element-ui": "^2.15.7", "github-markdown-css": "^5.1.0", "nprogress": "^0.2.0", "qrcodejs2": "^0.0.2", "tinymce": "^5.10.3", "vue": "^2.6.14", "vue-clipboard2": "^0.3.3", "vue-countup-v2": "^4.0.0", "vue-echarts": "^6.0.2", "vue-i18n": "^8.27.0", "vue-router": "^3.5.3", "vuedraggable": "^2.24.3", "vuex": "^3.6.2", "xgplayer-vue": "^1.1.5", "xlsx": "^0.18.2"}, "devDependencies": {"@babel/core": "^7.17.5", "@babel/eslint-parser": "^7.17.0", "@vue/cli-plugin-babel": "^5.0.1", "@vue/cli-plugin-eslint": "^5.0.1", "@vue/cli-plugin-router": "^5.0.1", "@vue/cli-plugin-vuex": "^5.0.1", "@vue/cli-service": "^5.0.4", "compression-webpack-plugin": "^6.1.1", "eslint": "8.22.0", "eslint-config-prettier": "^8.4.0", "eslint-plugin-prettier": "^4.0.0", "eslint-plugin-vue": "^8.5.0", "prettier": "^2.5.1", "sass": "^1.49.9", "sass-loader": "^12.6.0", "vue-eslint-parser": "^8.3.0", "vue-template-compiler": "^2.6.14", "webpack": "^5.0.0"}}