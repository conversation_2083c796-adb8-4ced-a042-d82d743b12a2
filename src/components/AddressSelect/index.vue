<!-- 角色选择下拉框 -->
<template>
  <el-select
    clearable
    filterable
    :value="value"
    class="ele-block"
    :placeholder="this.$t('basics.pleaseChoose')"
    @input="updateValue"
    @change="change"
  >
    <el-option
      v-for="{ code, id, addr_name } in data"
      :key="id"
      :value="code"
      :label="addr_name"
    />
  </el-select>
</template>

<script>
  import { lists } from '@/api/basics/address';

  export default {
    name: 'RoleSelect',
    props: {
      // 选中的数据(v-modal)
      value: String,
      searchType: Number,
      // 提示信息
      placeholder: {
        type: String,
        default: ''
      }
    },
    data() {
      return {
        data: []
      };
    },
    async created() {
      /* 获取角色数据 */
      let data = {
        type: this.searchType,
        page: 1,
        status: 2,
        num: 10000
      };
      let a = await lists(data);
      // console.log(a.list)
      this.data = a.list;
    },
    methods: {
      updateValue(value) {
        this.$emit('input', value);
      },
      click(value) {
        console.log(value);
      },
      change(value) {
        this.$emit('addressClick', value);
      }
    }
  };
</script>
