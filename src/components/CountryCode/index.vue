<!-- 角色选择下拉框 -->
<template>
  <el-select
    clearable
    filterable
    :value="value"
    class="ele-block"
    :placeholder="this.$t('basics.pleaseChoose')"
    @input="updateValue"
  >
    <el-option
      v-for="{ iso_code_2,country_id } in data"
      :key="country_id"
      :value="iso_code_2"
      :label="iso_code_2"
    />
  </el-select>
</template>

<script>
  import { countryCode } from '@/api/common';

  export default {
    name: 'RoleSelect',
    props: {
      // 选中的数据(v-modal)
      value: String,
      // 提示信息
      placeholder: {
        type: String,
        default: ''
      }
    },
    data() {
      return {
        data: []
      };
    },
    async created() {
      /* 获取角色数据 */
      let a = await countryCode()
      this.data = a.result
    },
    methods: {
      updateValue(value) {
        this.$emit('input', value);
      },
      click(value) {
        console.log(value);
      }
    }
  };
</script>
