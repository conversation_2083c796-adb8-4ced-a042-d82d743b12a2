<!-- 角色选择下拉框 -->
<template>
  <el-select
    clearable
    :value="value"
    :disabled="disabled()"
    class="ele-block"
    :placeholder="placeholder"
    @input="updateValue"
  >
    <el-option
      v-for="item in data"
      :key="item.id"
      :value="item.id"
      :label="item.name"
    />
  </el-select>
</template>

<script>
  import { pageBrand } from '@/api/commodity/brand';

  export default {
    name: 'select-brand',
    props: {
      // 选中的数据(v-modal)
      value: String,
      userId: {
        // eslint-disable-next-line vue/require-prop-type-constructor
        type: Number | String
      },
      // 提示信息
      placeholder: {
        type: String,
        default: '请选择品牌'
      }
    },
    data() {
      return {
        data: []
      };
    },
    created() {
      /* 获取品牌数据 */
      this.loadData();
    },
    methods: {
      loadData() {
        pageBrand({
          cur_page: 1,
          per_page: 10000000000,
          user_id: this.userId
        })
          .then((list) => {
            this.data = list.list;
          })
          .catch((e) => {
            this.$message.error(e);
          });
      },
      disabled() {
        return !(
          (this.$hasRole('admin') && this.userId) ||
          !this.$hasRole('admin')
        );
      },
      updateValue(value) {
        this.$emit('input', value);
      },
      click(value) {
        console.log(value);
      }
    },
    watch: {
      userId: function () {
        //情况数据
        this.loadData();
        this.$emit('input', null);
      }
    }
  };
</script>
