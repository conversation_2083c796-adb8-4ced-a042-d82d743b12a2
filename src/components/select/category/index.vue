<!-- 角色选择下拉框 -->
<template>
  <ele-tree-select
    clearable
    :value="val"
    :data="data"
    :disabled="disabled()"
    label-key="name"
    value-key="id"
    de="id"
    default-expand-all
    @input="updateValue"
    :placeholder="placeholder"
  />
</template>

<script>
  import { pageGoodeCategory } from '@/api/commodity/goods-category';
  export default {
    name: 'select-category',
    props: {
      // 选中的数据(v-modal)
      value: String,
      // 提示信息
      placeholder: {
        type: String,
        default: '请选择分类'
      },
      userId: {
        // eslint-disable-next-line vue/require-prop-type-constructor
        type: Number | String
      }
    },
    data() {
      return {
        data: [],
        val: null
      };
    },
    created() {
      this.loadData();
    },
    methods: {
      disabled() {
        return !(
          (this.$hasRole('admin') && this.userId) ||
          !this.$hasRole('admin')
        );
      },
      loadData() {
        pageGoodeCategory({
          user_id: this.userId
        })
          .then((list) => {
            this.data = this.$util.toTreeData({
              data: list,
              idField: 'id',
              parentIdField: 'parent_id'
            });
            //回调数据
            this.$emit('click', {
              data: list
            });
            this.$nextTick(() => {
              this.val = this.value;
            });
          })
          .catch((e) => {
            this.$message.error(e);
          });
      },
      hasRole() {
        return this.$hasRole('admin');
      },
      updateValue(value) {
        this.$emit('input', value);
      },
      click(value) {
        console.log(value);
      }
    },
    watch: {
      value() {
        this.val = this.value;
      },
      userId: function () {
        //情况数据
        this.loadData();
        this.$emit('input', null);
      }
    }
  };
</script>
