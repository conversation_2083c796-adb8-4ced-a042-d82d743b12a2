<!-- 角色选择下拉框 -->
<template>
  <el-select
    clearable
    :value="value"
    class="ele-block"
    :placeholder="placeholder"
    @input="updateValue"
  >
    <el-option
      v-for="{ id, nickname } in data"
      :key="parseInt(id)"
      :value="parseInt(id)"
      :label="nickname"
    />
  </el-select>
</template>

<script>
  import { ownerList } from '@/api/common';

  export default {
    name: 'user-select',
    props: {
      // 选中的数据(v-modal)
      // eslint-disable-next-line vue/require-prop-type-constructor
      value: Number | String,
      // 提示信息
      placeholder: {
        type: String,
        default: '请选择'
      }
    },
    data() {
      return {
        data: []
      };
    },
    async created() {
      /* 获取角色数据 */
      let a = await ownerList();
      console.log(a.result)
      this.data = a.result
    },
    methods: {
      updateValue(value) {
        this.$emit('input', value);
      },
      click(value) {
        console.log(value);
      }
    }
  };
</script>
