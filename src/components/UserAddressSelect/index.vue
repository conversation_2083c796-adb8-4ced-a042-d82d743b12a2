<!-- 角色选择下拉框 -->
<template>
  <el-select
    clearable
    filterable
    :value="value"
    class="ele-block"
    :placeholder="this.$t('basics.pleaseChoose')"
    @input="updateValue"
  >
    <el-option
      v-for="{ id, user_addresses } in data"
      :key="id"
      :value="user_addresses"
      :label="user_addresses"
    />
  </el-select>
</template>

<script>
  import { lists } from '@/api/basics/useraddress';

  export default {
    name: 'UserAddressSelect',
    props: {
      // 选中的数据(v-modal)
      value: String,
      searchType: Number,
      // 提示信息
      placeholder: {
        type: String,
        default: ''
      }
    },
    data() {
      return {
        data: []
      };
    },
    async created() {
      /* 获取角色数据 */
      let data = {
        page: 1,
        num: 10000
      };
      let a = await lists(data);
      // console.log(a.list)
      this.data = a.list;
    },
    methods: {
      updateValue(value) {
        this.$emit('input', value);
      },
      click(value) {
        console.log(value);
      }
    }
  };
</script>
