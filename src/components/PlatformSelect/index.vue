<!-- 角色选择下拉框 -->
<template>
  <el-select
    clearable
    :value="value"
    class="ele-block"
    :placeholder="this.$t('basics.pleaseChoose')"
    @input="updateValue"
    filterable
  >
    <el-option
      v-for="{ name, id } in data"
      :key="id"
      :value="id"
      :label="name"
    />
    <el-option
      :key="nomalValue"
      :value="nomalValue"
      :label="this.$t('platform.pickup.platform_nomal')"
    />
  </el-select>
</template>

<script>
  import { lists } from '@/api/platform/platform';

  export default {
    name: 'RoleSelect',
    props: {
      // 选中的数据(v-modal)
      value: Number,
      // 提示信息
      placeholder: {
        type: String,
        default: ''
      }
    },
    data() {
      return {
        data: [],
        nomalValue: 0
      };
    },
    async created() {
      let a = await lists({ page: 1, num: 10000 });
      this.data = a.list;
    },
    methods: {
      updateValue(value) {
        if (value === '') {
          this.$emit('input', null);
        } else {
          this.$emit('input', value);
        }
      },
      click(value) {
        console.log(value);
      }
    }
  };
</script>
