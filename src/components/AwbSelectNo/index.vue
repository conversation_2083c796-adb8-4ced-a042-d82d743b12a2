<!-- 角色选择下拉框 -->
<template>
  <el-select
    clearable
    :value="value"
    class="ele-block"
    :placeholder="this.$t('basics.pleaseChoose')"
    @input="updateValue"
    filterable
    remote
    reserve-keyword
    :remote-method="remoteMethod"
  >
    <el-option
      v-for="{ bol_no,id } in data"
      :key="id"
      :value="bol_no"
      :label="bol_no"
    />
  </el-select>
</template>

<script>
  import { noPagelists } from '@/api/order/awb';

  export default {
    name: 'RoleSelect',
    props: {
      // 选中的数据(v-modal)
      value: String,
      // 提示信息
      placeholder: {
        type: String,
        default: ''
      }
    },
    data() {
      return {
        data: []
      };
    },
    async created() {
      let a = await noPagelists({page:1,num:1000})
      this.data = a
    },
    methods: {
      remoteMethod(query) {
        if (query !== '') {
          this.loading = true;
          noPagelists({bol_no:query,page:1,num:1000}).then(res=>{
            this.loading = false;
            this.data = res
          })
        } else {
          this.options = [];
        }
      },
      updateValue(value) {
        this.$emit('input', value);
      },
      click(value) {
        console.log(value);
      }
    }
  };
</script>
