<!-- 角色选择下拉框 -->
<template>
  <el-select
    clearable
    :value="value"
    class="ele-block"
    :placeholder="this.$t('basics.pleaseChoose')"
    @input="updateValue"
    filterable
  >
    <el-option
      v-for="{ air_no, id } in data"
      :key="id"
      :value="air_no"
      :label="air_no"
    />
  </el-select>
</template>

<script>
  import { airlist } from '@/api/list/carlist';

  export default {
    name: 'RoleSelect',
    props: {
      // 选中的数据(v-modal)
      value: String,
      address_code: String,
      // 提示信息
      placeholder: {
        type: String,
        default: ''
      }
    },
    data() {
      return {
        data: []
      };
    },
    async created() {
      let a = await airlist({
        page: 1,
        num: 10000,
        status: 1,
        address_code: this.address_code
      });
      this.data = a.list;
    },
    methods: {
      updateValue(value) {
        this.$emit('input', value);
      },
      click(value) {
        console.log(value);
      }
    }
  };
</script>
