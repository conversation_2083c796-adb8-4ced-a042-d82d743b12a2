<!-- 角色选择下拉框 -->
<template>
  <el-select
    clearable
    filterable
    :value="value"
    class="ele-block"
    :placeholder="this.$t('basics.pleaseChoose')"
    @input="updateValue"
  >
    <el-option
      v-for="{ name, id } in data"
      :key="id"
      :value="name"
      :label="name"
    />
  </el-select>
</template>

<script>
  import { lists } from '@/api/basics/bussiness';

  export default {
    name: 'RoleSelect',
    props: {
      // 选中的数据(v-modal)
      value: String,
      // 提示信息
      placeholder: {
        type: String,
        default: ''
      }
    },
    data() {
      return {
        data: []
      };
    },
    async created() {
      let a = await lists({ page: 1, num: 10000 });
      this.data = a.list;
    },
    methods: {
      updateValue(value) {
        this.$emit('input', value);
      },
      click(value) {
        console.log(value);
      }
    }
  };
</script>
