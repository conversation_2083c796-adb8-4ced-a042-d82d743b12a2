<template>
  <ele-image-upload
    v-model="images"
    :limit="limit"
    :disabled="disabled"
    :before-upload="onBeforeUpload"
    :drag="true"
    @upload="onUpload"
  />
</template>

<script>
  import EleImageUpload from 'ele-admin/es/ele-image-upload';
  import { getAliYunUpdateRpc } from '@/api/common';
  import { OSS_UPLOAD_CONFIG } from '@/config/setting';
  import { uuid } from 'ele-admin';
  const OSS = require('ali-oss');
  export default {
    name: 'upload-image',
    props: {
      // 选中的数据(v-modal)
      value: {
        type: Array,
        default: function () {
          return [];
        }
      },
      // 选中的数据(v-modal)
      disabled: {
        type: Boolean,
        default: false
      },
      limit: {
        type: Number,
        default: 10
      },
      prelude: {
        type: String,
        default: 'back_end'
      }
    },
    async mounted() {
      const aliYunUpdateRpc = await getAliYunUpdateRpc();
      this.client = new OSS({
        ...aliYunUpdateRpc,
        refreshSTSToken: async () => {
          const info = await getAliYunUpdateRpc();
          return {
            accessKeyId: info.accessKeyId,
            accessKeySecret: info.accessKeySecret,
            stsToken: info.stsToken
          };
        },
        // 刷新临时访问凭证的时间间隔，单位为毫秒。
        refreshSTSTokenInterval: 300000
      });
    },
    components: { EleImageUpload },
    data() {
      return {
        // 已上传数据
        images: this.value,
        client: null
      };
    },
    methods: {
      /* 上传事件 */
      async onUpload(item) {
        item.status = 'uploading';
        item.progress = 0;
        try {
          const name = `${this.prelude}/${this.$util.toDateString(
            new Date(),
            'yyyyMMdd'
          )}/${uuid()}.${item.name.split('.').pop()}`;
          const res = await this.client.multipartUpload(name, item.file, {
            ...OSS_UPLOAD_CONFIG,
            progress: (p) => {
              item.progress = p * 100;
            }
          });
          if (res.res.requestUrls[0]) {
            item.status = 'done';
            item.url = this.client.options.host + name;
            this.$emit('input', this.images);
          } else {
            this.$message.error('上传失败, 服务器繁忙');
          }
        } catch (err) {
          console.log(err);
          item.status = 'exception';
          this.$message.error('上传失败, 服务器繁忙');
        }
      },
      /* 上传前钩子 */
      onBeforeUpload(file) {
        if (!file.type.startsWith('image')) {
          this.$message.error('只能选择图片');
          return false;
        }
        if (file.size / 1024 / 1024 > 12) {
          this.$message.error('大小不能超过 12MB');
          return false;
        }
      },
      /* 验证器验证是否全部成功 */
      verification() {
        if (
          this.images.filter((item) => {
            return item.status !== 'done';
          }).length !== 0
        ) {
          this.$message.error('请处理完未上传成功的图片！！');
          return false;
        }
        return this.images;
      }
    },
    watch: {
      value(visible) {
        this.images = visible;
      }
    }
  };
</script>
