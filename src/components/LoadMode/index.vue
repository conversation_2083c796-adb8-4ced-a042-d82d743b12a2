<!-- 角色选择下拉框 -->
<template>
  <el-select
    clearable
    filterable
    :value="value"
    class="ele-block"
    :placeholder="this.$t('basics.pleaseChoose')"
    @input="updateValue"
  >
    <el-option
      v-for="{ name,id } in data"
      :key="id"
      :value="id"
      :label="name"
    />
  </el-select>
</template>

<script>
  import { lists } from '@/api/basics/loadmode';

  export default {
    name: 'RoleSelect',
    props: {
      // 选中的数据(v-modal)
      value: String,
      // 提示信息
      placeholder: {
        type: String,
        default: ''
      }
    },
    data() {
      return {
        data: []
      };
    },
    async created() {
      /* 获取角色数据 */
      let a = await lists({page:1,num:10000})
      this.data = a.result
    },
    methods: {
      updateValue(value) {
        this.$emit('input', value);
      },
      click(value) {
        console.log(value);
      }
    }
  };
</script>
