<!-- 编辑弹窗 -->
<template>
  <ele-modal
    width="720px"
    :visible="visible"
    :close-on-click-modal="false"
    custom-class="ele-dialog-form"
    :title="this.$t('route.storages._name')"
    @update:visible="updateVisible">
    <el-form ref="form" :model="form" :rules="rules" label-width="150px" @submit.native.prevent>
      <el-row :gutter="15">
        <el-col :sm="24">
          <el-form-item :label="this.$t('storages.storage_name')+':'" prop="storage_name">
            <el-input clearable :maxlength="255" v-model="form.storage_name" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div slot="footer">
      <el-button @click="updateVisible(false)">
        {{ $t("basics.cancel") }}
      </el-button>
      <el-button type="primary" :loading="loading" @click="save">
        {{ $t("basics.save") }}
      </el-button>
    </div>
  </ele-modal>
</template>

<script>
// import UserAddressSelect from '@/components/UserAddressSelect';
import { create, update } from "@/api/storages/list";

const DEFAULT_FORM = {
  storage_id: "",
  storage_name: ""
};

export default {
  name: "StoragesEdit",
  // components: { UserAddressSelect },
  props: {
    // 弹窗是否打开
    visible: Boolean,
    // 修改回显的数据
    data: Object
  },
  data() {
    return {
      // 表单数据
      form: { ...DEFAULT_FORM },
      // 表单验证规则
      rules: {
        storage_name: [
          {
            required: true,
            message: this.$t("basics.pleaseInput"),
            trigger: "blur"
          }
        ]
      },
      // 提交状态
      loading: false,
      // 是否是修改
      isUpdate: false
    };
  },
  created() {
  },
  methods: {
    /* 保存编辑 */
    save() {
      this.$refs["form"].validate((valid) => {
        if (!valid) {
          return false;
        }
        this.loading = true;
        if (this.isUpdate) {
          update(this.form)
            .then(res => {
              this.loading = false;
              console.log(res);
              this.$message.success(this.$t('basics.success'));
              this.updateVisible(false);
              this.$emit('done');
            })
            .catch(err => {
              this.loading = false;
              console.log(err);
              this.$message.error(err);
            });
        } else {
          create(this.form)
            .then(res => {
              this.loading = false;
              console.log(res);
              this.$message.success(this.$t('basics.success'));
              this.updateVisible(false);
              this.$emit('done');
            })
            .catch(err => {
              this.loading = false;
              console.log(err);
              this.$message.error(err);
            });
        }
      });
    },
    /* 更新visible */
    updateVisible(value) {
      this.$emit("update:visible", value);
    },
    /* 角色选择回调*/
    roleSelectCallback(value) {
      this.form.role_type = value.type;
    }
  },
  watch: {
    async visible(visible) {
      console.log(visible);
      if (visible) {
        let data = this.data;
        console.log(data);
        if (this.data) {
          this.$util.assignObject(this.form, {
            ...data
          });
          this.isUpdate = true;
        } else {
          this.isUpdate = false;
        }
        console.log(this.isUpdate);
      } else {
        this.$refs["form"].clearValidate();
        this.form = { ...DEFAULT_FORM };

        console.log("close", this.form, DEFAULT_FORM);
      }
    }
  }
};
</script>
