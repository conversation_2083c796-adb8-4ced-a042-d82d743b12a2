<template>
  <div class="ele-body">
    <el-card shadow="never">
      <!-- 搜索表单 -->
      <outbound-fba-search @search="reload" />
      <!-- 数据表格 -->
      <ele-pro-table
        ref="table"
        :initLoad="false"
        :columns="columns"
        :datasource="datasource"
        :selection.sync="selection">
        <template slot="toolbar">
          <el-button
            v-permission="'storages:create'"
            size="small"
            type="primary"
            icon="el-icon-plus"
            class="ele-btn-icon"
            @click="addStorages()">{{ $t("storages.add_storage") }}
          </el-button>
        </template>

        <template slot="action" slot-scope="{ row }">
          <el-link
            v-permission="'storages:update'"
            @click="editStorages(row)"
            type="primary"
            icon="el-icon-edit"
            :underline="false">{{ $t("storages.edit_storage") }}
          </el-link>
          <el-popconfirm
            class="ele-action"
            v-permission="'storages:delete'"
            :title="$t('storages.del_storage_tips')"
            @confirm="deleteStorages(row)">
            <el-link
              type="danger"
              slot="reference"
              icon="el-icon-delete"
              :underline="false">{{ $t("storages.del_storage") }}
            </el-link>
          </el-popconfirm>
        </template>
      </ele-pro-table>
    </el-card>
    <storages-edit :visible.sync="showEdit" :data="current" @done="reload" />
  </div>
</template>

<script>
import { list, deleteStorages } from "@/api/storages/list";
import OutboundFbaSearch from "./components/outbound-fba-search";
import StoragesEdit from "./components/edit";
// import AddEdit from "./components/edit";
// import { utils, writeFile } from "xlsx";

export default {
  name: "SystemUser",
  components: {
    StoragesEdit,
    OutboundFbaSearch
    // AddEdit
    // UserSearch,
    // UserEdit,
    // UserImport,
    // UserChangePassword
  },
  data() {
    return {
      // 表格列配置
      columnsData: [
        {
          columnKey: "index",
          type: "index",
          width: 45,
          showOverflowTooltip: true,
          fixed: "left"
        },
        {
          prop: "storage_name",
          label: this.$t("storages.storage_name"),
          showOverflowTooltip: true,
          minWidth: 350
        },
        {
          prop: "created_at",
          label: this.$t("basics.createTime"),
          showOverflowTooltip: true,
          width: 200
        },
        {
          prop: "updated_at",
          label: this.$t("basics.updateTime"),
          showOverflowTooltip: true,
          width: 200
        },
        {
          columnKey: "action",
          label: this.$t("basics.action"),
          width: 270,
          align: "center",
          resizable: false,
          slot: "action",
          hideInSetting: true
        }
      ],
      // 表格选中数据
      selection: [],
      // 当前编辑数据
      current: null,
      // 是否显示编辑弹窗
      showEdit: false,
      // 是否显示导入弹窗
      showImport: false,
      // 是否显修改密码
      userId: null,
      showChangePassword: false,
      lastWhere: {},
      pageNum: 20000,
      pageAll: 1,
      loading: null,
      loading_vue: null
    };
  },
  activated() {
    this.reload();
  },
  computed: {
    // 通知标题
    columns() {
      return this.columnsData.filter((i) => {
        return (
          (i.noRole ? !this.$hasRole(i.noRole) : true) &&
          (i.role ? this.$hasRole(i.role) : true)
        );
      });
    }
  },
  methods: {
    /* 表格数据源 */
    datasource({ page, limit, where, order }) {
      return list({
        ...where,
        ...order,
        page: page,
        num: limit
        // inbound_id: this.$route.query.inbound_id
      });
    },
    /* 刷新表格 */
    reload(where) {
      this.lastWhere = where;
      this.$refs.table.reload({ page: 1, where: where });
    },
    /* 打开导入弹窗 */
    openImport() {
      this.showImport = true;
    },
    addStorages() {
      this.current = null;
      this.showEdit = true;
    },
    editStorages(row) {
      console.log(row);
      this.current = {
        storage_id: row.id,
        storage_name: row.storage_name
      };
      this.showEdit = true;
    },
    deleteStorages(row) {
      this.loading_vue = this.$loading({ lock: true });
      deleteStorages({ storage_id: row.id })
        .then(res => {
          this.loading_vue.close();
          // console.log(res);
          this.reload();
        })
        .catch(err => {
          this.loading_vue.close();
          // console.log(err);
          this.$message.error(err);
        })
    }
  }
};
</script>
