<template>
  <div class="ele-body">
    <el-card shadow="never">
      <!-- 搜索表单 -->
      <outbound-fba-search @search="reload" />
      <!-- 数据表格 -->
      <ele-pro-table
        ref="table"
        :initLoad="false"
        :columns="columns"
        :datasource="datasource"
        :selection.sync="selection">
        <template slot="toolbar">
          <el-button
            size="small"
            icon="el-icon-download"
            class="ele-btn-icon"
            @click="exportXls(1, [])"
            v-permission="'box:export'">
            {{ $t("basics.export") }}
          </el-button>
        </template>

        <template slot="action" slot-scope="{ row }">
          <el-popconfirm
            v-show="row.status < 4"
            v-permission="'box:edit'"
            class="ele-action"
            :title="$t('awb.box.tips.t1')"
            @confirm="editAdd(row)">
            <el-link type="danger" slot="reference" :underline="false">
              {{ $t("awb.box.editAdd") }}
            </el-link>
          </el-popconfirm>
        </template>

        <template slot="address" slot-scope="{ row }">
          <p v-for="item in row.address.split(',')">{{ item }}</p>
        </template>
      </ele-pro-table>
      <add-edit
        :visible.sync="showEdit"
        :data="current"
        @done="reload"
      ></add-edit>
    </el-card>
  </div>
</template>

<script>
import { inventory } from "@/api/storages/list";
import OutboundFbaSearch from "./components/outbound-fba-search";
import AddEdit from "./components/edit";
import { utils, writeFile } from "xlsx";

export default {
  name: "SystemUser",
  components: {
    OutboundFbaSearch,
    AddEdit
    // UserSearch,
    // UserEdit,
    // UserImport,
    // UserChangePassword
  },
  data() {
    return {
      // 表格列配置
      columnsData: [
        /* {
          width: 45,
          type: "selection",
          columnKey: "selection",
          // noRole: 'sonOwner', //什么角色下不显示
          align: "center"
        }, */
        {
          columnKey: 'index',
          type: 'index',
          width: 45,
          // role: 'sonOwner', //什么角色下不显示
          showOverflowTooltip: true,
          fixed: 'left'
        },
        {
          prop: "storage_name",
          label: this.$t("storages.storage_name"),
          showOverflowTooltip: true,
          minWidth: 150
        },
        {
          prop: "bol_no",
          label: this.$t("awb.noa.awbno"),
          showOverflowTooltip: true,
          minWidth: 150
        },
        {
          prop: "address",
          label: this.$t("basicsMenu.address.name"),
          showOverflowTooltip: true,
          minWidth: 110,
          slot: "address"
        },
        {
          prop: "user_name",
          label: this.$t("awb.awb.col9"),
          showOverflowTooltip: true,
          minWidth: 110
        },
        {
          prop: "box_num",
          label: this.$t("car.boxNum"),
          showOverflowTooltip: true,
          minWidth: 110
        },
        {
          prop: "tray_num",
          label: this.$t("order.awbExp.col24"),
          showOverflowTooltip: true,
          minWidth: 110
        },
        {
          prop: "created_at",
          label: this.$t("storages.launch_time"),
          showOverflowTooltip: true,
          minWidth: 110
        },
        /* {
          noRole: "sonOwner", //什么角色下不显示
          columnKey: "action",
          label: this.$t("basics.action"),
          width: 140,
          align: "center",
          resizable: false,
          slot: "action",
          hideInSetting: true,
          fixed: "right"
        } */
      ],
      // 表格选中数据
      selection: [],
      // 当前编辑数据
      current: null,
      // 是否显示编辑弹窗
      showEdit: false,
      // 是否显示导入弹窗
      showImport: false,
      // 是否显修改密码
      userId: null,
      showChangePassword: false,
      lastWhere: {},
      pageNum: 20000,
      pageAll: 1,
      loading: null
    };
  },
  activated() {
    this.reload();
  },
  computed: {
    // 通知标题
    columns() {
      return this.columnsData.filter((i) => {
        return (
          (i.noRole ? !this.$hasRole(i.noRole) : true) &&
          (i.role ? this.$hasRole(i.role) : true)
        );
      });
    }
  },
  methods: {
    /* 表格数据源 */
    datasource({ page, limit, where, order }) {
      return inventory({
        ...where,
        ...order,
        page: page,
        num: limit
        // inbound_id: this.$route.query.inbound_id
      });
    },
    /* 刷新表格 */
    reload(where) {
      this.lastWhere = where;
      this.$refs.table.reload({ page: 1, where: where });
    },
    /* 打开编辑弹窗 */
    editAdd(row) {
      this.current = {
        addresses_users: row.user_address_code,
        box_id_arr: [row.id]
      };
      this.showEdit = true;
    },
    /* 打开导入弹窗 */
    openImport() {
      this.showImport = true;
    },
    exportXls(page_no = 1, data = []) {
      let _this = this, array = data;
      // let roleUser = this.$hasRole("sonOwner");
      if (page_no == 1) {
        array.push([
          this.$t("storages.storage_name"),
          this.$t("awb.noa.awbno"),
          this.$t("basicsMenu.address.name"),
          this.$t("awb.awb.col9"),
          this.$t("car.boxNum"),
          this.$t("order.awbExp.col24"),
          this.$t("storages.launch_time")
        ]);
      }
      _this.loading = this.$loading({ lock: true });
      inventory({
        ...this.lastWhere,
        page: page_no,
        num: this.pageNum
      }).then((res) => {
        _this.pageAll = Math.ceil(res.count / this.pageNum);
        res.list.forEach((d) => {
          array.push([
            d.storage_name,
            d.bol_no,
            d.address,
            d.user_name,
            d.box_num,
            d.tray_num,
            d.created_at
          ]);
        });

        if (_this.pageAll <= 1 || page_no == _this.pageAll) {
          _this.loading.close();
          const sheetName = "Sheet1";
          const workbook = {
            SheetNames: [sheetName],
            Sheets: {}
          };
          workbook.Sheets[sheetName] = utils.aoa_to_sheet(array);
          writeFile(workbook, "Inventory.xlsx");
        } else {
          _this.exportXls(parseInt(page_no) + 1, array);
        }
      });
    }
  }
};
</script>
