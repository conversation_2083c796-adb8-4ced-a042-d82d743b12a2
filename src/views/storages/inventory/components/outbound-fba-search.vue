<template>
  <el-card shadow="never" body-style="padding: 22px 22px 7px 22px;">
    <!-- 搜索表单 -->
    <el-form
      label-width="145px"
      class="ele-form-search"
      @keyup.enter.native="search"
      @submit.native.prevent>
      <el-row :gutter="15">
        <el-col :lg="8" :md="12">
          <el-form-item :label="this.$t('storages.storage_name') + ':'">
            <el-input
              clearable
              show-word-limit
              v-model="where.storage_name"
              :placeholder="this.$t('basics.pleaseInput')"
            />
          </el-form-item>
        </el-col>
        <el-col :lg="8" :md="12">
          <el-form-item :label="this.$t('awb.noa.awbno') + ':'" prop="bol_no">
            <el-input
              v-model="where.bol_no"
              :placeholder="this.$t('basics.pleaseInput')"
            />
          </el-form-item>
        </el-col>
        <el-col :lg="8" :md="12">
          <el-form-item :label="`${$t('car.addressOut')}:`">
            <address-select v-model="where.address_code" />
          </el-form-item>
        </el-col>
        <el-col :lg="8" :md="12">
          <!--<el-form-item :label="this.$t('userAddress.user_addresses') + ':'">
            <user-address-select v-model="where.user_address_code" />
          </el-form-item>-->
          <el-form-item :label="`${$t('awb.awb.col9')}:`" prop="bol_no">
            <user-select v-model="where.user_id" @callback="roleSelectCallback" />
          </el-form-item>
        </el-col>
        <!--<el-col :lg="8" :md="12">
          <el-form-item :label="this.$t('basics.createTime') + ':'" prop="created_at">
            <el-date-picker
              v-model="where.created_at"
              type="datetimerange"
              value-format="yyyy-MM-dd HH:mm:ss"
              :range-separator="this.$t('basics.to')"
              class="ele-fluid"
              :start-placeholder="this.$t('basics.beginTime')"
              :end-placeholder="this.$t('basics.endTime')"></el-date-picker>
          </el-form-item>
        </el-col>-->
        <el-col :lg="8" :md="12" v-permission="'awb:nouserSearch'">
          <el-form-item :label="$t('storages.launch_time') + ':'" prop="created_at">
            <el-date-picker
              v-model="where.created_at"
              type="datetimerange"
              value-format="yyyy-MM-dd HH:mm:ss"
              :range-separator="this.$t('basics.to')"
              class="ele-fluid"
              :start-placeholder="this.$t('basics.beginTime')"
              :end-placeholder="this.$t('basics.endTime')"></el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :lg="8" :md="12">
          <div class="ele-form-actions">
            <el-button
              type="primary"
              icon="el-icon-search"
              class="ele-btn-icon"
              @click="search">{{ $t("basics.query") }}</el-button>
            <el-button @click="reset">{{ $t("basics.reset") }}</el-button>
          </div>
        </el-col>
      </el-row>
    </el-form>
  </el-card>
</template>

<script>
import AddressSelect from "@/components/AddressSelect";
import UserAddressSelect from "@/components/UserAddressSelect";
import UserSelect from "@/views/order/awb/components/user-select.vue";
// 默认表单数据

const DEFAULT_WHERE = {
  bol_no: "",
  box_no: "",
  track_no: "",
  package_reference: "",
  address_code: "",
  status: null,
  clearance: null,
  user_address_code: null
};

export default {
  name: "outbound-fba-search",
  components: { UserSelect, UserAddressSelect, AddressSelect },
  data() {
    return {
      // 表单数据
      where: { ...DEFAULT_WHERE },
      // 搜索表单是否展开
      searchExpand: false,
      goodsCategoryList: [],
      category_id: ""
    };
  },
  methods: {
    /* 搜索 */
    search() {
      //解决搜索 也搜索下级的数据
      this.$emit("search", this.where);
    },
    /* 重置搜索 */
    reset() {
      this.where = { ...DEFAULT_WHERE };
      this.search();
    },
    /* 搜索展开/收起 */
    toggleExpand() {
      this.searchExpand = !this.searchExpand;
      this.$emit("expand-change", this.searchExpand);
    },
    /* 搜索展开/收起 */
    goodsCategoryFilterListClick(e) {
      this.goodsCategoryList = e.data;
    },
    roleSelectCallback() {},
  }
};
</script>
