<template>
  <div class="ele-body ele-body-card">
    <template>
      <el-card shadow="never" body-style="padding: 22px 22px 7px 22px;">
        <!-- 搜索表单 -->
        <el-form
          label-width="130px"
          class="ele-form-search"
          @keyup.enter.native="search"
          @submit.native.prevent
        >
          <el-row :gutter="15">
            <el-col :lg="8" :md="12">
              <el-form-item :label="`${$t('awb.awb.col9')}:`" prop="user_id">
                <user-select v-model="where.user_id" />
              </el-form-item>
            </el-col>
            <el-col :lg="8" :md="12">
              <el-form-item :label="`${$t('car.addressOut')}:`">
                <address-select v-model="where.address_code" />
              </el-form-item>
            </el-col>
            <el-col :lg="8" :md="12">
              <el-form-item :label="this.$t('awb.awb.port') + ':'" prop="lgg">
                <port-select v-model="where.lgg" />
              </el-form-item>
            </el-col>
            <el-col :lg="8" :md="12">
              <el-form-item :label="$t('awb.awb.col21') + ':'" prop="ata_time">
                <el-date-picker
                  v-model="where.ata_time"
                  type="datetimerange"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  :range-separator="$t('basics.to')"
                  class="ele-fluid"
                  :start-placeholder="$t('basics.beginTime')"
                  :end-placeholder="$t('basics.endTime')"
                  :picker-options="pickerOptions"
                >
                </el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :lg="8" :md="12">
              <div class="ele-form-actions">
                <el-button
                  type="primary"
                  icon="el-icon-search"
                  class="ele-btn-icon"
                  @click="search"
                >
                  {{ $t('basics.query') }}
                </el-button>
                <el-button @click="reset">{{ $t('basics.reset') }}</el-button>
              </div>
            </el-col>
          </el-row>
        </el-form>
      </el-card>
    </template>
    <sale-card :data="nowWhere" ref="saleCardRef" />
  </div>
</template>

<script>
  import SaleCard from './components/sale-card.vue';
  import UserSelect from './components/user-select.vue';
  import AddressSelect from '@/components/AddressSelect';
  import PortSelect from '@/components/PortSelect';

  const DEFAULT_WHERE = {
    address_code: '',
    lgg: '',
    ata_time: null
  };
  export default {
    name: 'DashboardAnalysis',
    components: {
      UserSelect,
      AddressSelect,
      PortSelect,
      SaleCard
    },
    computed: {
      // 是否开启响应式布局
      styleResponsive() {
        return this.$store.state.theme.styleResponsive;
      }
    },
    data() {
      return {
        where: { ...DEFAULT_WHERE },
        nowWhere: {},
        pickerOptions: {
          onPick: ({ maxDate, minDate }) => {
            this.selectedMinDate = minDate;
            if (maxDate) {
              this.selectedMinDate = null;
            }
          },
          disabledDate: (time) => {
            if (!this.selectedMinDate) {
              return false;
            }

            // 计算最大允许日期（当前选择日期 + 1个月）
            const maxAllowedDate = new Date(this.selectedMinDate);
            maxAllowedDate.setMonth(maxAllowedDate.getMonth() + 1);

            // 计算最小允许日期（当前选择日期 - 1个月）
            const minAllowedDate = new Date(this.selectedMinDate);
            minAllowedDate.setMonth(minAllowedDate.getMonth() - 1);

            // 禁用超过1个月范围的日期
            return (
              time.getTime() > maxAllowedDate.getTime() ||
              time.getTime() < minAllowedDate.getTime()
            );
          }
        }
      };
    },
    methods: {
      /* 重置搜索 */
      reset() {
        this.where = { ...DEFAULT_WHERE };
      },
      /* 搜索 */
      search() {
        this.$refs.saleCardRef.getSaleroomData(this.where);
        //解决搜索 也搜索下级的数据
        // this.nowWhere = this.where;
      }
    }
  };
</script>
