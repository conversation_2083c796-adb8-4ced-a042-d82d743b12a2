<template>
  <el-card shadow="never" body-style="padding: 0;">
    <el-row :gutter="15" style="background-color: #f0f2f5">
      <el-col v-bind="{ span: 12 }">
        <el-card class="analysis-chart-card" shadow="never">
          <template v-slot:header>
            <div class="ele-cell">
              <div class="ele-cell-content">{{
                $t('validity.validityTypes.s1')
              }}</div>
            </div>
          </template>
          <div class="analysis-chart-card-num ele-text-heading">
            <span
              >{{ $t('validity.ninetyInTime') }}{{ this.ninetyInTime }}hrs</span
            >
          </div>
        </el-card>
      </el-col>
      <el-col v-bind="{ span: 12 }">
        <el-card class="analysis-chart-card" shadow="never">
          <template v-slot:header>
            <div class="ele-cell">
              <div class="ele-cell-content">{{
                $t('validity.validityTypes.s2')
              }}</div>
            </div>
          </template>
          <div class="analysis-chart-card-num ele-text-heading">
            <span>
              {{ $t('validity.ninetyOutTime') }}{{ this.ninetyOutTime }}hrs
            </span>
          </div>
        </el-card>
      </el-col>
    </el-row>
    <div class="ele-cell demo-monitor-tool">
      <div class="ele-cell-content">
        <el-tabs
          v-model="saleSearch.type"
          class="demo-monitor-tabs"
          @tab-click="onSaleTypeChange"
        >
          <el-tab-pane
            :label="this.$t('validity.validityParcels')"
            name="saleroom"
          />
          <el-tab-pane
            :label="this.$t('validity.validityParcelsTrend')"
            name="broken"
          />
        </el-tabs>
      </div>
    </div>
    <el-divider />
    <el-row>
      <el-col v-bind="styleResponsive ? { lg: 24, md: 16 } : { span: 24 }">
        <div class="demo-monitor-title">
          <span v-if="saleSearch.type === 'saleroom'">
            {{ this.$t('validity.validityParcels') }}
            {{ this.$t('validity.validityParcelsUnit') }}
          </span>
          <span v-else>
            {{ this.$t('validity.validityParcelsTrend') }}
            {{ this.$t('validity.validityParcelsTrendUnit') }}
          </span>
        </div>
        <v-chart
          ref="saleChart"
          style="height: 485px"
          :option="saleChartOption"
          @click="handleChartClick"
        />
      </el-col>
      <parcel-lists
        :visible.sync="displayShowParcel"
        :data="nowSearch"
        ref="parcelListRef"
      />
    </el-row>
    <el-divider />
    <div style="height: 15px; width: 100%; background-color: #f0f2f5"></div>
    <el-row>
      <ele-pro-table
        ref="tablelog"
        :columns="columns"
        :datasource="datasource"
        :toolkit="[]"
      >
        <template slot="toolbar">
          <el-row :gutter="15">
            <el-col :lg="4" :md="12">
              <el-select
                v-model="selectType"
                :placeholder="this.$t('validity.pleaseTypeChoose')"
                clearable
                class="ele-fluid"
                @change="changHandle"
              >
                <el-option
                  :label="this.$t('validity.validityTypes.s1')"
                  :value="2"
                />
                <el-option
                  :label="this.$t('validity.validityTypes.s2')"
                  :value="3"
                />
              </el-select>
            </el-col>
            <el-col :lg="4" :md="12">
              <el-select
                v-model="selectArea"
                :placeholder="this.$t('validity.pleaseAreaChoose')"
                clearable
                class="ele-fluid"
                @change="changHandle"
              >
                <el-option
                  v-if="this.inCounts.length == 7"
                  :label="this.$t('validity.validityAreas.s0')"
                  :value="0"
                />
                <el-option
                  :label="this.$t('validity.validityAreas.s1')"
                  :value="1"
                />
                <el-option
                  :label="this.$t('validity.validityAreas.s2')"
                  :value="2"
                />
                <el-option
                  :label="this.$t('validity.validityAreas.s3')"
                  :value="3"
                />
                <el-option
                  :label="this.$t('validity.validityAreas.s4')"
                  :value="4"
                />
                <el-option
                  :label="this.$t('validity.validityAreas.s5')"
                  :value="5"
                />
                <el-option
                  :label="this.$t('validity.validityAreas.s6')"
                  :value="6"
                />
              </el-select>
            </el-col>
          </el-row>
        </template>
        <template slot="toolkit">
          <el-button
            size="small"
            type="success"
            icon="el-icon-download"
            class="ele-btn-icon"
            @click="exportParcel"
          >
            {{ $t('basics.export') }}
          </el-button>
        </template>
      </ele-pro-table>
    </el-row>
  </el-card>
</template>

<script>
  import { use } from 'echarts/core';
  import { CanvasRenderer } from 'echarts/renderers';
  import { BarChart, LineChart } from 'echarts/charts';
  import {
    GridComponent,
    TooltipComponent,
    LegendComponent
  } from 'echarts/components';
  import VChart from 'vue-echarts';
  import { echartsMixin } from '@/utils/echarts-mixin';
  import {
    getValidityHourList,
    validityHourParcelList,
    validityHourParcelListExcel,
    validityLineList
  } from '@/api/order/validity';
  import ParcelLists from './components/parcels';

  use([
    CanvasRenderer,
    BarChart,
    LineChart,
    GridComponent,
    TooltipComponent,
    LegendComponent
  ]);

  export default {
    components: { VChart, ParcelLists },
    mixins: [echartsMixin(['saleChart'])],
    props: {
      // 查询的数据的数据
      data: Object
    },
    data() {
      return {
        // 销售量搜索参数
        saleSearch: {
          type: 'saleroom',
          dateType: 0,
          datetime: ''
        },
        nowSearch: {},
        // 销售量趋势数据
        inCounts: [],
        // 访问量趋势数据
        outCounts: [],
        inPercentage: [],
        outPercentage: [],
        ninetyInTime: 0,
        ninetyOutTime: 0,
        inAllCount: 0,
        outAllCount: 0,
        isFirst: true,
        inBrokens: [],
        outBrokens: [],
        areaBrokens: [],
        xAxisDataSelect: [],
        displayShowParcel: false,
        selectType: '',
        selectArea: '',
        // 销售额柱状图配置
        saleChartOption: {},
        columns: [
          {
            columnKey: 'index',
            type: 'index',
            width: 45,
            showOverflowTooltip: true,
            fixed: 'left'
          },
          {
            prop: 'bol_no',
            label: this.$t('order.awbExp.col3'),
            showOverflowTooltip: true,
            minWidth: 110
          },
          {
            prop: 'track_no',
            label: this.$t('awb.package.no'),
            showOverflowTooltip: true,
            minWidth: 110
          },
          {
            prop: 'addr_name',
            label: this.$t('car.addressOut'),
            showOverflowTooltip: true,
            minWidth: 110
          },
          {
            prop: 'user_id',
            label: this.$t('awb.awb.col9'),
            showOverflowTooltip: true,
            minWidth: 110
          },
          {
            prop: 'lgg',
            label: this.$t('order.awbExp.col11'),
            showOverflowTooltip: true,
            minWidth: 110
          },
          {
            prop: 'clearance',
            label: this.$t('awb.awb.clearStatus'),
            showOverflowTooltip: true,
            minWidth: 70,
            formatter: (row, column, cellValue) => {
              // return cellValue;
              return this.$t(`basics.statusWords.s${cellValue}`);
            }
          },
          {
            prop: 'stock_in_time',
            label: this.$t('awb.box.stock_in_time'),
            showOverflowTooltip: true,
            minWidth: 110
          },
          {
            prop: 'stock_out_time',
            label: this.$t('awb.box.stock_out_time'),
            showOverflowTooltip: true,
            minWidth: 110
          },
          {
            prop: 'ata_time',
            label: this.$t('awb.awb.col21'),
            showOverflowTooltip: true,
            minWidth: 110
          },
          {
            prop: 'stock_in_validity',
            label: this.$t('validity.validityTypes.s1'),
            showOverflowTooltip: true,
            minWidth: 110
          },
          {
            prop: 'stock_out_validity',
            label: this.$t('validity.validityTypes.s2'),
            showOverflowTooltip: true,
            minWidth: 110
          }
        ]
      };
    },
    computed: {
      // 是否开启响应式布局
      styleResponsive() {
        return this.$store.state.theme.styleResponsive;
      }
    },
    created() {
      this.getSaleroomData(this.data);
    },
    methods: {
      /* 获取销售量数据 */
      getSaleroomData(search) {
        if (typeof search.ata_time === 'undefined') {
          return;
        }
        if (!Array.isArray(search.ata_time)) {
          this.$message.error(
            this.$t('validity.validityAtaTimeEmptyError') + ''
          );
          return;
        }
        this.saleSearch.type = 'saleroom';
        this.selectType = '';
        this.selectArea = '';
        this.nowSearch.allCount = 0;
        this.reload();
        this.isFirst = true;
        const loading = this.$loading({ lock: true });
        getValidityHourList(search)
          .then((data) => {
            this.nowSearch = search;
            this.inCounts = data.inCounts;
            this.outCounts = data.outCounts;
            this.inPercentage = data.inPercentage;
            this.outPercentage = data.outPercentage;
            this.ninetyInTime = data.ninetyInTime;
            this.ninetyOutTime = data.ninetyOutTime;
            this.inAllCount = data.inAllCounts;
            this.outAllCount = data.outAllCounts;
            this.onSaleTypeChange();
            loading.close();
          })
          .catch((e) => {
            loading.close();
            this.$message.error(e);
          });
      },
      changHandle() {
        if (this.selectArea === '' || this.selectType === '') {
          return;
        }
        if (this.inCounts.length === 0) {
          return;
        }
        this.nowSearch.type = this.selectType;
        this.nowSearch.dataIndex = this.selectArea;
        if (this.selectType == 2) {
          this.nowSearch.allCount = this.inCounts[this.selectArea];
        } else if (this.selectType == 3) {
          this.nowSearch.allCount = this.outCounts[this.selectArea];
        } else {
          this.nowSearch.allCount = 0;
        }
        this.reload();
      },
      handleChartClick(event) {
        // 检查是否点击了数据系列
        if (
          event &&
          event.componentType === 'series' &&
          this.saleSearch.type === 'saleroom'
        ) {
          // 获取数据点的名称和值
          let dataIndex = event.dataIndex;
          if (this.inCounts.length == 6) {
            dataIndex = dataIndex + 1;
          }
          this.nowSearch.allCount = event.data; //包裹数量
          this.nowSearch.type = event.seriesIndex == 0 ? 2 : 3; //2查询入库、3查询出库
          this.selectType = event.seriesIndex == 0 ? 2 : 3; //2查询入库、3查询出库
          this.nowSearch.dataIndex = dataIndex;
          this.selectArea = dataIndex;
          this.reload();
        }
      },
      datasource({ page, limit, where, order }) {
        if (typeof where.allCount === 'undefined') {
          return [];
        }
        if (typeof where.type === 'undefined') {
          return [];
        }
        if (typeof where.dataIndex === 'undefined') {
          return [];
        }
        if (where.allCount === 0) {
          return [];
        }
        return validityHourParcelList({
          ...where,
          ...order,
          page: page,
          num: limit
        });
      },
      reload() {
        console.log(this.nowSearch);
        this.$nextTick(() => {
          this.$refs.tablelog.reload({
            page: 1,
            where: this.nowSearch
          });
        });
      },
      exportParcel() {
        if (typeof this.nowSearch.type === 'undefined') {
          this.$message.error(this.$t('validity.exportXlsError') + '');
          return;
        }
        if (typeof this.nowSearch.dataIndex === 'undefined') {
          this.$message.error(this.$t('validity.exportXlsError') + '');
          return;
        }
        if (typeof this.nowSearch.allCount === 'undefined') {
          this.$message.error(this.$t('validity.exportXlsError') + '');
          return;
        }
        validityHourParcelListExcel(this.nowSearch)
          .then((data) => {
            this.$message.success(this.$t('validity.parcelExcelMessage') + '');
          })
          .catch((e) => {
            if (typeof e.message === 'undefined') {
              this.$message.error(e);
            } else {
              this.$message.error(e.message);
            }
          });
      },
      /* 销售量tab选择改变事件 */
      onSaleTypeChange() {
        let data1 = [];
        let data2 = [];
        let label = { show: true, position: 'top' };
        let label1 = { show: true, position: 'top' };
        let label2 = { show: true, position: 'top' };
        let xAxisData = [
          this.$t('validity.validityAreas.s0'),
          this.$t('validity.validityAreas.s1'),
          this.$t('validity.validityAreas.s2'),
          this.$t('validity.validityAreas.s3'),
          this.$t('validity.validityAreas.s4'),
          this.$t('validity.validityAreas.s5'),
          this.$t('validity.validityAreas.s6')
        ];
        if (this.saleSearch.type === 'saleroom') {
          // for (let i = 0; i < this.inCounts.length; i++) {
          //   data1.push({
          //     value: this.inCounts[i]
          //   });
          // }
          //
          //
          data1 = this.inCounts;
          data2 = this.outCounts;
          if (this.inCounts.length == 6) {
            xAxisData = [
              this.$t('validity.validityAreas.s1'),
              this.$t('validity.validityAreas.s2'),
              this.$t('validity.validityAreas.s3'),
              this.$t('validity.validityAreas.s4'),
              this.$t('validity.validityAreas.s5'),
              this.$t('validity.validityAreas.s6')
            ];
          }
          let inAllCount = this.inAllCount;
          let outAllcount = this.outAllCount;
          label1 = {
            show: true,
            position: 'top',
            formatter: function (params) {
              return (
                params.value +
                '\n(' +
                ((params.value / inAllCount) * 100).toFixed(1) +
                '%)'
              );
            }
          };
          label2 = {
            show: true,
            position: 'top',
            formatter: function (params) {
              return (
                params.value +
                '\n(' +
                ((params.value / outAllcount) * 100).toFixed(1) +
                '%)'
              );
            }
          };
          this.xAxisDataSelect = xAxisData;
        } else {
          if (this.isFirst) {
            if (typeof this.nowSearch.ata_time === 'undefined') {
              return;
            }
            if (!Array.isArray(this.nowSearch.ata_time)) {
              return;
            }
            const loading = this.$loading({ lock: true });
            validityLineList(this.nowSearch)
              .then((data) => {
                this.area = data.area;
                this.inBrokens = data.in;
                this.outBrokens = data.out;
                loading.close();
                this.isFirst = false;
                this.onSaleTypeChange();
              })
              .catch((e) => {
                this.isFirst = false;
                loading.close();
                this.$message.error(e);
              });
            return;
          } else {
            xAxisData = [];
            data1 = this.inBrokens;
            data2 = this.outBrokens;
            label = {
              show: true,
              formatter: (params) => params.value + 'hrs'
            };
            for (var i = 1; i <= data1.length; i++) {
              xAxisData.push(this.$t('validity.validityWeeks.s' + i));
            }
          }
        }
        var emphasisStyle = {
          itemStyle: {
            shadowBlur: 10,
            shadowColor: 'rgba(0,0,0,0.3)'
          }
        };
        if (this.saleSearch.type === 'saleroom') {
          this.saleChartOption = {
            orient: 'vertical',
            legend: {
              data: [
                this.$t('validity.validityTypes.s1'),
                this.$t('validity.validityTypes.s2')
              ],
              top: 'top'
            },
            tooltip: {},
            xAxis: {
              data: xAxisData,
              name: 'X Axis',
              axisLine: { onZero: true },
              splitLine: { show: false },
              splitArea: { show: false }
            },
            yAxis: {},
            grid: {
              bottom: 100
            },
            series: [
              {
                name: this.$t('validity.validityTypes.s1'),
                type: 'bar',
                stack: 'one',
                emphasis: emphasisStyle,
                label: label1,
                data: data1
              },
              {
                name: this.$t('validity.validityTypes.s2'),
                type: 'bar',
                stack: 'two',
                emphasis: emphasisStyle,
                label: label2,
                data: data2
              }
            ]
          };
        } else {
          this.saleChartOption = {
            legend: {
              data: [
                this.$t('validity.validityTypes.s1'),
                this.$t('validity.validityTypes.s2')
              ]
            },
            xAxis: {
              data: xAxisData,
              name: 'X Axis',
              type: 'category',
              boundaryGap: false
            },
            yAxis: {},
            grid: {
              bottom: 100
            },
            series: [
              {
                name: this.$t('validity.validityTypes.s1'),
                type: 'line',
                label: label,
                data: data1
              },
              {
                name: this.$t('validity.validityTypes.s2'),
                type: 'line',
                label: label,
                data: data2
              }
            ]
          };
        }
      }
    }
  };
</script>

<style lang="scss" scoped>
  /* 销售额、访问量工具栏 */
  .demo-monitor-tool {
    padding: 0 20px;

    .demo-monitor-tabs {
      height: 51px;
    }

    :deep(.el-tabs__item) {
      height: 51px;
      line-height: 51px;
      font-size: 15px;
    }

    :deep(.el-tabs__nav-wrap:after) {
      display: none;
    }
  }

  /* 小标题 */
  .demo-monitor-title {
    padding: 0 20px;
    margin: 15px 0 5px 0;
  }

  /* 排名 item */
  .demo-monitor-rank-item {
    padding: 0 20px;
    line-height: 20px;
    margin-top: 18px;
  }
  .analysis-chart-card-num {
    font-size: 30px;
  }

  .analysis-chart-card-content {
    height: 40px;
    box-sizing: border-box;
    margin-bottom: 12px;
  }

  .analysis-chart-card-text {
    padding-top: 12px;
  }
</style>
