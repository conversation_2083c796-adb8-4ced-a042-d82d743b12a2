<!-- 编辑弹窗 -->
<template>
  <ele-pro-table ref="tablelog" :columns="columns" :datasource="datasource">
    <template slot="toolbar">
      <el-button
        size="small"
        type="success"
        icon="el-icon-download"
        class="ele-btn-icon"
        @click="exportParcel"
      >
        {{ $t('basics.export') }}
      </el-button>
    </template>
  </ele-pro-table>
</template>

<script>
  import {
    validityHourParcelList,
    validityHourParcelListExcel
  } from '@/api/order/validity';

  const DEFAULT_FORM = {
    allCount: 0
  };

  export default {
    name: 'EditTime',
    components: {},
    props: {
      // 弹窗是否打开
      visible: Boolean,
      // 修改回显的数据
      data: Object
    },
    data() {
      return {
        // 表单数据
        form: { ...DEFAULT_FORM },
        columns: [
          {
            columnKey: 'index',
            type: 'index',
            width: 45,
            showOverflowTooltip: true,
            fixed: 'left'
          },
          {
            prop: 'bol_no',
            label: this.$t('order.awbExp.col3'),
            showOverflowTooltip: true,
            minWidth: 110
          },
          {
            prop: 'track_no',
            label: this.$t('awb.package.no'),
            showOverflowTooltip: true,
            minWidth: 110
          },
          {
            prop: 'addr_name',
            label: this.$t('car.addressOut'),
            showOverflowTooltip: true,
            minWidth: 110
          },
          {
            prop: 'user_id',
            label: this.$t('awb.awb.col9'),
            showOverflowTooltip: true,
            minWidth: 110
          },
          {
            prop: 'lgg',
            label: this.$t('order.awbExp.col11'),
            showOverflowTooltip: true,
            minWidth: 110
          },
          {
            prop: 'clearance',
            label: this.$t('awb.awb.clearStatus'),
            showOverflowTooltip: true,
            minWidth: 70,
            formatter: (row, column, cellValue) => {
              // return cellValue;
              return this.$t(`basics.statusWords.s${cellValue}`);
            }
          },
          {
            prop: 'stock_in_time',
            label: this.$t('awb.box.stock_in_time'),
            showOverflowTooltip: true,
            minWidth: 110
          },
          {
            prop: 'stock_out_time',
            label: this.$t('awb.box.stock_out_time'),
            showOverflowTooltip: true,
            minWidth: 110
          },
          {
            prop: 'ata_time',
            label: this.$t('awb.awb.times.time3'),
            showOverflowTooltip: true,
            minWidth: 110
          },
          {
            prop: 'stock_in_validity',
            label: this.$t('awb.awb.times.time3'),
            showOverflowTooltip: true,
            minWidth: 110
          },
          {
            prop: 'stock_out_validity',
            label: this.$t('awb.awb.times.time3'),
            showOverflowTooltip: true,
            minWidth: 110
          }
        ],
        // 提交状态
        loading: false,
        // 是否是修改
        isUpdate: false
      };
    },
    methods: {
      datasource({ page, limit, where, order }) {
        if (typeof where.allCount === 'undefined') {
          return [];
        }
        return validityHourParcelList({
          ...where,
          ...order,
          page: page,
          num: limit
        });
      },
      exportParcel() {
        validityHourParcelListExcel(this.data)
          .then((data) => {
            this.$message.success(this.$t('validity.parcelExcelMessage'));
          })
          .catch((e) => {
            this.$message.error(e.message);
          });
      },
      reload(data) {
        this.form = data;
        // console.log(data);
        // console.log(this.data);
        this.$nextTick(() => {
          this.$refs.tablelog.reload();
        });
      },
      /* 更新visible */
      updateVisible(value) {
        this.$emit('update:visible', value);
      },
      /* 角色选择回调*/
      roleSelectCallback(value) {
        this.form.role_type = value.type;
      }
    },
    watch: {
      async visible(visible) {
        if (visible) {
          let data = this.data;
          if (this.data) {
            this.$util.assignObject(this.form, {
              ...data
            });
            this.isUpdate = true;
          } else {
            this.isUpdate = false;
          }
          this.reload();
        } else {
          // this.$refs['form'].clearValidate();
          this.form = { ...DEFAULT_FORM };
        }
      }
    }
  };
</script>
