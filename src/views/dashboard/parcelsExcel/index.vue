<template>
  <div class="ele-body">
    <el-card shadow="never">
      <!-- 数据表格 -->
      <ele-pro-table
        ref="table"
        :initLoad="false"
        :columns="columns"
        :datasource="datasource"
        :selection.sync="selection"
      >
        <!-- 操作列 -->
        <template slot="action" slot-scope="{ row }">
          <el-link
            v-if="row.status === 2"
            @click="info(row)"
            type="primary"
            icon="el-icon-download"
            :underline="false"
          >
            {{$t('validity.downloadZip')}}
          </el-link>
          <el-popconfirm
            class="ele-action"
            :title="$t('basics.confirmDel')"
            @confirm="cancel(row)"
          >
            <el-link type="danger" slot="reference" icon="el-icon-delete" :underline="false">
              {{$t('basics.delete')}}
            </el-link>
          </el-popconfirm>
        </template>
      </ele-pro-table>
    </el-card>
  </div>
</template>

<script>
  import { cancel, lists, info } from '@/api/order/validity';

  export default {
    name: 'SystemUser',
    components: {},
    data() {
      return {
        // 表格列配置
        columns: [
          {
            columnKey: 'index',
            type: 'index',
            width: 45,
            showOverflowTooltip: true,
            fixed: 'left'
          },
          {
            prop: 'status',
            label: this.$t('basics.status'),
            showOverflowTooltip: true,
            minWidth: 110,
            formatter: (row, column, cellValue) => {
              return [
                this.$t('validity.status.s1'),
                this.$t('validity.status.s2')
              ][cellValue - 1];
            }
          },
          {
            prop: 'created_at',
            label: this.$t('basics.createTime'),
            showOverflowTooltip: true,
            minWidth: 110
          },
          {
            prop: 'updated_at',
            label: this.$t('basics.updateTime'),
            showOverflowTooltip: true,
            minWidth: 110
          },
          {
            columnKey: 'action',
            label: this.$t('basics.action'),
            width: 220,
            align: 'center',
            resizable: false,
            slot: 'action',
            hideInSetting: true
          }
        ],
        // 表格选中数据
        selection: [],
        // 当前编辑数据
        current: null,
        // 是否显示编辑弹窗
        showEdit: false,
        // 是否显示导入弹窗
        showImport: false,
        // 是否显修改密码
        userId: null,
        showChangePassword: false
      };
    },
    created() {
      //或者登陆不显示货主名字其他登录显示名称
      if (
        this.$store.state.user.info.role_type === '2' ||
        this.$store.state.user.info.role_type === '3'
      ) {
        const index = this.columns.findIndex(function (key) {
          return key.prop === 'username';
        });
        this.columns.splice(index, index + 1);
      }
    },
    activated() {
      this.reload();
    },
    methods: {
      /* 表格数据源 */
      datasource({ page, limit, where, order }) {
        return lists({
          ...where,
          ...order,
          page: page,
          num: limit
        });
      },
      /* 刷新表格 */
      reload(where) {
        this.$refs.table.reload({ page: 1, where: where });
      },
      info(row) {
        const loading = this.$loading({ lock: true });
        info(row.id)
          .then((data) => {
            loading.close();
            this.downloadZip(data.result.zip_path);
          })
          .catch((e) => {
            loading.close();
            this.$message.error(e);
          });
      },
      downloadZip(url, name = '') {
        let a = document.createElement('a'), //创建a标签
          e = document.createEvent('MouseEvents'); //创建鼠标事件对象
        e.initEvent('click', false, false); //初始化事件对象
        a.href = url; //设置下载地址
        a.target = '_blank';
        a.download = name; //设置下载文件名
        a.dispatchEvent(e); //给指定的元素，执行事件click事件
      },
      /* 删除 */
      cancel(row) {
        const loading = this.$loading({ lock: true });
        cancel(row.id)
          .then((msg) => {
            loading.close();
            this.$message.success(this.$t('basics.success'));
            this.reload();
          })
          .catch((e) => {
            loading.close();
            this.$message.error(e);
          });
      }
    }
  };
</script>
