<template>
  <el-card shadow="never" body-style="padding: 0;">
    <div class="ele-cell demo-monitor-tool">
      <div class="ele-cell-content">
        <el-tabs
          v-model="saleSearch.type"
          class="demo-monitor-tabs"
          @tab-click="onSaleTypeChange"
        >
          <el-tab-pane
            :label="this.$t('validity.validityLine.s0')"
            name="piechart"
          />
          <el-tab-pane
            :label="this.$t('validity.validityLine.s1')"
            name="saleroom"
          />
          <el-tab-pane
            :label="this.$t('validity.validityLine.s2')"
            name="broken"
          />
        </el-tabs>
      </div>
    </div>
    <el-divider />
    <el-row>
      <el-col v-bind="styleResponsive ? { lg: 24, md: 16 } : { span: 24 }">
        <div class="demo-monitor-title">
          <span v-if="saleSearch.type === 'piechart'">
            <el-row :gutter="15" style="height: 36px">
              <el-col :lg="4" :md="12">
                <el-select
                  v-model="showoptionStr"
                  :placeholder="this.$t('validity.pleaseAreaChoose')"
                  class="ele-fluid"
                  style="width: 150px"
                  @change="onSaleTypeChange"
                >
                  <el-option
                    :label="this.$t('validity.validityBols.cmr_out_hour')"
                    value="cmrOutHourOption"
                  />
                  <el-option
                    :label="this.$t('validity.validityBols.plan_hour')"
                    value="planHourOption"
                  />
                  <el-option
                    :label="this.$t('validity.validityBols.noa_pmc_hour')"
                    value="noaPmcHourOption"
                  />
                  <el-option
                    :label="this.$t('validity.validityBols.noa_loose_hour')"
                    value="noaLooseHourOption"
                  />
                  <el-option
                    :label="this.$t('validity.validityBols.cmr_pmc_hour')"
                    value="cmrPmcHourOption"
                  />
                  <el-option
                    :label="this.$t('validity.validityBols.cmr_loose_hour')"
                    value="cmrLooseHourOption"
                  />
                  <el-option
                    :label="this.$t('validity.validityBols.cmr_in_hour')"
                    value="cmrInHourOption"
                  />
                  <el-option
                    :label="this.$t('validity.validityBols.edt_in_hour')"
                    value="edtInHourOption"
                  />
                  <el-option
                    :label="this.$t('validity.validityBols.customs_hour')"
                    value="customsHourOption"
                  />
                  <el-option
                    :label="this.$t('validity.validityBols.station_hour')"
                    value="stationHourOption"
                  />
                  <el-option
                    :label="this.$t('validity.validityBols.box_hour')"
                    value="boxHourOption"
                  />
                </el-select>
              </el-col>
            </el-row>
          </span>
          <span v-else-if="saleSearch.type === 'saleroom'">
            <el-row :gutter="15" style="height: 36px">
              <el-col :lg="4" :md="12">
                {{ this.$t('validity.validityLine.s1') }}
              </el-col>
            </el-row>
          </span>
          <span v-else>
            <el-row :gutter="15" style="height: 36px">
              <el-col :lg="4" :md="12">
                {{ this.$t('validity.validityLine.s2') }}
              </el-col>
            </el-row>
          </span>
        </div>
        <v-chart
          ref="saleChart"
          style="height: 485px"
          :option="saleChartOption"
          @click="handleChartClick"
        />
      </el-col>
    </el-row>
    <div style="height: 15px; width: 100%; background-color: #f0f2f5"></div>
    <div class="ele-cell demo-monitor-tool">
      <div class="ele-cell-content">
        <el-tabs
          v-model="listSearch"
          class="demo-monitor-tabs"
          @tab-click="onListChange"
        >
          <el-tab-pane
            :label="this.$t('validity.validityLineList.s1')"
            name="addressList"
          />
          <el-tab-pane
            :label="this.$t('validity.validityLineList.s2')"
            name="bolList"
          />
        </el-tabs>
      </div>
    </div>
    <el-divider />
    <el-row v-if="listSearch === 'addressList'">
      <ele-pro-table
        ref="addressList"
        :columns="columnsData"
        :datasource="datasourceAddress"
        :selection.sync="selection"
      >
        <!-- 表头工具栏 -->
        <template slot="toolbar">
          <el-button
            size="small"
            icon="el-icon-plus"
            class="ele-btn-icon"
            @click="batchEditReason"
            v-permission="'bols:editReason'"
          >
            {{ $t('validity.reason_setting') }}
          </el-button>
        </template>
        <template slot="cmr_out_hour" slot-scope="{ row }">
          <span
            style="color: var(--color-primary)"
            v-if="row.cmr_out_hour > setting.cmr_out_hour"
            >{{ row.cmr_out_hour }}</span
          >
          <span v-else>{{ row.cmr_out_hour }}</span>
        </template>
        <template slot="plan_hour" slot-scope="{ row }">
          <span
            style="color: var(--color-primary)"
            v-if="row.plan_hour > setting.plan_hour"
            >{{ row.plan_hour }}</span
          >
          <span v-else>{{ row.plan_hour }}</span>
        </template>
        <template slot="noa_pmc_hour" slot-scope="{ row }">
          <span
            style="color: var(--color-primary)"
            v-if="row.noa_pmc_hour > setting.noa_pmc_hour"
            >{{ row.noa_pmc_hour }}</span
          >
          <span v-else>{{ row.noa_pmc_hour }}</span>
        </template>
        <template slot="noa_loose_hour" slot-scope="{ row }">
          <span
            style="color: var(--color-primary)"
            v-if="row.noa_loose_hour > setting.noa_loose_hour"
            >{{ row.noa_loose_hour }}</span
          >
          <span v-else>{{ row.noa_loose_hour }}</span>
        </template>
        <template slot="cmr_pmc_hour" slot-scope="{ row }">
          <span
            style="color: var(--color-primary)"
            v-if="row.cmr_pmc_hour > setting.cmr_pmc_hour"
            >{{ row.cmr_pmc_hour }}</span
          >
          <span v-else>{{ row.cmr_pmc_hour }}</span>
        </template>
        <template slot="cmr_loose_hour" slot-scope="{ row }">
          <span
            style="color: var(--color-primary)"
            v-if="row.cmr_loose_hour > setting.cmr_loose_hour"
            >{{ row.cmr_loose_hour }}</span
          >
          <span v-else>{{ row.cmr_loose_hour }}</span>
        </template>
        <template slot="cmr_in_hour" slot-scope="{ row }">
          <span
            style="color: var(--color-primary)"
            v-if="row.cmr_in_hour > setting.cmr_in_hour"
            >{{ row.cmr_in_hour }}</span
          >
          <span v-else>{{ row.cmr_in_hour }}</span>
        </template>
        <template slot="edt_in_hour" slot-scope="{ row }">
          <span
            style="color: var(--color-primary)"
            v-if="row.edt_in_hour > setting.edt_in_hour"
            >{{ row.edt_in_hour }}</span
          >
          <span v-else>{{ row.edt_in_hour }}</span>
        </template>
        <template slot="customs_hour" slot-scope="{ row }">
          <span
            style="color: var(--color-primary)"
            v-if="row.customs_hour > setting.customs_hour"
            >{{ row.customs_hour }}</span
          >
          <span v-else>{{ row.customs_hour }}</span>
        </template>
        <template slot="box_hour" slot-scope="{ row }">
          <span
            style="color: var(--color-primary)"
            v-if="row.box_hour > setting.box_hour"
            >{{ row.box_hour }}</span
          >
          <span v-else>{{ row.box_hour }}</span>
        </template>
        <template slot="station_hour" slot-scope="{ row }">
          <span
            style="color: var(--color-primary)"
            v-if="row.station_hour > setting.station_hour"
            >{{ row.station_hour }}</span
          >
          <span v-else>{{ row.station_hour }}</span>
        </template>
        <template slot="action" slot-scope="{ row }">
          <el-link
            @click="editReason(row)"
            type="primary"
            icon="el-icon-tickets"
            v-permission="'bols:editReason'"
            :underline="false"
          >
            {{ $t('validity.reason_setting') }}
          </el-link>
          <el-link
            @click="detailawb(row)"
            type="primary"
            icon="el-icon-tickets"
            :underline="false"
          >
            {{ $t('basics.detail') }}
          </el-link>
        </template>
      </ele-pro-table>
      <detail-awb :visible.sync="showDetailAwb" :data="current" />
      <edit-reason :visible.sync="showEdit" :data="current" />
    </el-row>
    <el-row v-if="listSearch === 'bolList'">
      <ele-pro-table
        ref="tablelog"
        :columns="columns"
        :datasource="datasource"
        :toolkit="[]"
      >
        <template slot="toolbar">
          <el-row :gutter="15">
            <el-col :lg="4" :md="12">
              <el-select
                v-model="optionStr"
                :placeholder="this.$t('validity.pleaseAreaChoose')"
                clearable
                class="ele-fluid"
                @change="changHandle"
              >
                <el-option
                  :label="this.$t('validity.validityBols.cmr_out_hour')"
                  value="cmrOutHourOption"
                />
                <el-option
                  :label="this.$t('validity.validityBols.plan_hour')"
                  value="planHourOption"
                />
                <el-option
                  :label="this.$t('validity.validityBols.noa_pmc_hour')"
                  value="noaPmcHourOption"
                />
                <el-option
                  :label="this.$t('validity.validityBols.noa_loose_hour')"
                  value="noaLooseHourOption"
                />
                <el-option
                  :label="this.$t('validity.validityBols.cmr_pmc_hour')"
                  value="cmrPmcHourOption"
                />
                <el-option
                  :label="this.$t('validity.validityBols.cmr_loose_hour')"
                  value="cmrLooseHourOption"
                />
                <el-option
                  :label="this.$t('validity.validityBols.cmr_in_hour')"
                  value="cmrInHourOption"
                />
                <el-option
                  :label="this.$t('validity.validityBols.edt_in_hour')"
                  value="edtInHourOption"
                />
                <el-option
                  :label="this.$t('validity.validityBols.customs_hour')"
                  value="customsHourOption"
                />
                <el-option
                  :label="this.$t('validity.validityBols.station_hour')"
                  value="stationHourOption"
                />
                <el-option
                  :label="this.$t('validity.validityBols.box_hour')"
                  value="boxHourOption"
                />
              </el-select>
            </el-col>
            <el-col :lg="4" :md="12">
              <el-select
                v-model="statusId"
                :placeholder="this.$t('validity.pleaseTypeChoose')"
                clearable
                class="ele-fluid"
                @change="changHandle"
              >
                <el-option
                  :label="this.$t('validity.validityStatus.s0')"
                  :value="0"
                />
                <el-option
                  :label="this.$t('validity.validityStatus.s1')"
                  :value="1"
                />
                <el-option
                  :label="this.$t('validity.validityStatus.s2')"
                  :value="2"
                />
              </el-select>
            </el-col>
          </el-row>
        </template>
        <template slot="toolkit">
          <el-button
            size="small"
            type="success"
            icon="el-icon-download"
            class="ele-btn-icon"
            @click="exportBols"
          >
            {{ $t('basics.export') }}
          </el-button>
        </template>
      </ele-pro-table>
    </el-row>
  </el-card>
</template>

<script>
  import { use } from 'echarts/core';
  import { CanvasRenderer } from 'echarts/renderers';
  import { BarChart, LineChart, PieChart } from 'echarts/charts';
  import {
    GridComponent,
    TooltipComponent,
    LegendComponent
  } from 'echarts/components';
  import VChart from 'vue-echarts';
  import { echartsMixin } from '@/utils/echarts-mixin';
  import {
    validityBol,
    validityBolList,
    validityBolListExp,
    getValidityBolHour,
    validityAddressLists
  } from '@/api/order/validity';
  import { getProcess } from '@/api/views';
  import { utils, writeFile } from 'xlsx';
  import DetailAwb from './components/detail';
  import EditReason from './components/edit';

  use([
    CanvasRenderer,
    BarChart,
    LineChart,
    PieChart,
    GridComponent,
    TooltipComponent,
    LegendComponent
  ]);

  export default {
    components: {
      DetailAwb,
      EditReason,
      VChart
    },
    mixins: [
      echartsMixin([
        'cmrOutHourChart',
        'planHourChart',
        'noaPmcHourChart',
        'noaLooseHourChart',
        'cmPmcHourChart',
        'cmrLooseHourChart',
        'cmrInHourChart',
        'edtInHourChart',
        'customsHourChart',
        'stationHourChart',
        'boxHourChart'
      ])
    ],
    props: {
      // 查询的数据的数据
      data: Object
    },
    data() {
      return {
        saleSearch: {
          type: 'piechart',
          dateType: 0,
          datetime: ''
        },
        listSearch: 'addressList',
        saleChartOption: {},
        xAxisData: [],
        seriesDataS1: {
          cmr_out_hour: [],
          noa_pmc_hour: [],
          plan_hour: [],
          noa_loose_hour: [],
          cmr_loose_hour: [],
          cmr_in_hour: [],
          customs_hour: [],
          station_hour: [],
          edt_in_hour: [],
          box_hour: []
        },
        seriesDataS2: [],
        isFirst: true,
        bolData: [],
        nowSearch: [],
        bolIds: [],
        optionStr: null,
        showoptionStr: 'cmrOutHourOption',
        statusId: null,
        cmrOutHourOption: {},
        planHourOption: {},
        noaPmcHourOption: {},
        noaLooseHourOption: {},
        cmrPmcHourOption: {},
        cmrLooseHourOption: {},
        cmrInHourOption: {},
        edtInHourOption: {},
        customsHourOption: {},
        stationHourOption: {},
        boxHourOption: {},
        columns: [
          {
            columnKey: 'index',
            type: 'index',
            width: 45,
            showOverflowTooltip: true,
            fixed: 'left'
          },
          {
            prop: 'bol_no',
            label: this.$t('order.awbExp.col3'),
            showOverflowTooltip: true,
            minWidth: 110
          },
          {
            prop: 'created_at',
            label: this.$t('basics.createTime'),
            showOverflowTooltip: true,
            minWidth: 110
          },
          {
            prop: 'bol_flight',
            label: this.$t('awb.awb.col21'),
            showOverflowTooltip: true,
            minWidth: 110
          },
          {
            prop: 'bol_noa',
            label: this.$t('awb.noa.time'),
            showOverflowTooltip: true,
            minWidth: 110
          },
          {
            prop: 'bol_customs',
            label: this.$t('order.awbExp.col17'),
            showOverflowTooltip: true,
            minWidth: 110
          },
          {
            prop: 'bol_cmr_in',
            label: this.$t('validity.validityBols.bol_cmr_in'),
            showOverflowTooltip: true,
            minWidth: 110
          },
          {
            prop: 'scan_in',
            label: this.$t('awb.box.stock_in_time'),
            showOverflowTooltip: true,
            minWidth: 110
          },
          {
            prop: 'bol_cmr_out',
            label: this.$t('validity.validityBols.bol_cmr_out'),
            showOverflowTooltip: true,
            minWidth: 110
          },
          {
            prop: 'bol_warehouse',
            label: this.$t('basicsMenu.address.station'),
            showOverflowTooltip: true,
            minWidth: 110
          },
          {
            prop: 'bol_transport_type',
            label: this.$t('basicsMenu.loadmode.loadmode'),
            showOverflowTooltip: true,
            minWidth: 110
          }
        ],
        columnsData: [
          {
            width: 60,
            type: 'selection',
            columnKey: 'selection',
            align: 'center',
            showOverflowTooltip: true,
            fixed: 'left'
          },
          //提单号
          {
            prop: 'bol_no',
            label: this.$t('awb.noa.awbno'),
            showOverflowTooltip: true,
            minWidth: 200
          },
          //派送地址
          {
            prop: 'addr_name',
            label: this.$t('car.addressOut'), //地址名称
            showOverflowTooltip: true,
            minWidth: 200
          },
          //创建时间
          {
            prop: 'created_at',
            label: this.$t('validity.validityBols.created_at'),
            showOverflowTooltip: true,
            minWidth: 160
          },
          //实际落地时间
          {
            prop: 'bol_flight',
            label: this.$t('validity.validityBols.bol_flight'),
            showOverflowTooltip: true,
            minWidth: 160
          },
          {
            // NOA时间(PMC)
            prop: 'bol_pmc_noa',
            label: this.$t('validity.validityBols.bol_pmc_noa'),
            showOverflowTooltip: true,
            minWidth: 160
          },
          {
            // NOA时间(LOOSE)
            prop: 'bol_loose_noa',
            label: this.$t('validity.validityBols.bol_loose_noa'),
            showOverflowTooltip: true,
            minWidth: 160
          },
          {
            // NOA时间(LOOSE)
            prop: 'bol_customs',
            label: this.$t('validity.validityBols.bol_customs'),
            showOverflowTooltip: true,
            minWidth: 160
          },
          {
            // 到达货站时间
            prop: 'bol_cmr_station_in',
            label: this.$t('validity.validityBols.bol_cmr_station_in'),
            showOverflowTooltip: true,
            minWidth: 160
          },
          {
            // 到达货站时间(PMC)
            prop: 'bol_station_in_pmc',
            label: this.$t('validity.validityBols.bol_station_in_pmc'),
            showOverflowTooltip: true,
            show: false,
            minWidth: 160
          },
          {
            // 到达货站时间(LOOSE)
            prop: 'bol_station_in_loose',
            label: this.$t('validity.validityBols.bol_station_in_loose'),
            showOverflowTooltip: true,
            show: false,
            minWidth: 160
          },
          {
            // 离开货站时间
            prop: 'bol_cmr_station_out',
            label: this.$t('validity.validityBols.bol_cmr_station_out'),
            showOverflowTooltip: true,
            minWidth: 160
          },
          {
            // 离开货站时间
            prop: 'bol_cmr_station_out_edt',
            label: this.$t('validity.validityBols.bol_cmr_station_out_edt'),
            showOverflowTooltip: true,
            show: false,
            minWidth: 160
          },
          {
            // 到达仓库时间
            prop: 'bol_cmr_in',
            label: this.$t('validity.validityBols.bol_cmr_in'),
            showOverflowTooltip: true,
            minWidth: 160
          },
          {
            // 离开仓库时间
            prop: 'bol_cmr_out',
            label: this.$t('validity.validityBols.bol_cmr_out'),
            showOverflowTooltip: true,
            minWidth: 160
          },
          {
            // 出仓时间 扫描最后一箱时间
            prop: 'box_in_last_time',
            label: this.$t('validity.validityBols.box_in_last_time'),
            showOverflowTooltip: true,
            minWidth: 160
          },
          {
            // 出仓时间 扫描最后一箱时间
            prop: 'box_out_last_time',
            label: this.$t('validity.validityBols.box_out_last_time'),
            showOverflowTooltip: true,
            minWidth: 160
          },
          {
            // 箱子入库时间
            prop: 'bol_box_in',
            label: this.$t('validity.validityBols.bol_box_in'),
            showOverflowTooltip: true,
            show: false,
            minWidth: 160
          },
          {
            // 箱子出库时间
            prop: 'bol_box_out',
            label: this.$t('validity.validityBols.bol_box_out'),
            showOverflowTooltip: true,
            show: false,
            minWidth: 160
          },
          {
            // 出库时效
            prop: 'cmr_out_hour',
            label: this.$t('validity.validityBols.cmr_out_hour'),
            showOverflowTooltip: true,
            minWidth: 160,
            slot: 'cmr_out_hour'
          },
          {
            // 预报段时效
            prop: 'plan_hour',
            label: this.$t('validity.validityBols.plan_hour'),
            showOverflowTooltip: true,
            minWidth: 160,
            slot: 'plan_hour'
          },
          {
            // NOA段时效(PMC)
            prop: 'noa_pmc_hour',
            label: this.$t('validity.validityBols.noa_pmc_hour'),
            showOverflowTooltip: true,
            minWidth: 160,
            slot: 'noa_pmc_hour'
          },
          {
            // NOA段时效(LOOSE)
            prop: 'noa_loose_hour',
            label: this.$t('validity.validityBols.noa_loose_hour'),
            showOverflowTooltip: true,
            minWidth: 160,
            slot: 'noa_loose_hour'
          },
          {
            // 提货调度段时效(NOA)
            prop: 'cmr_pmc_hour',
            label: this.$t('validity.validityBols.cmr_pmc_hour'),
            showOverflowTooltip: true,
            minWidth: 160,
            slot: 'cmr_pmc_hour'
          },
          {
            // 提货调度段时效(LOOSE)
            prop: 'cmr_loose_hour',
            label: this.$t('validity.validityBols.cmr_loose_hour'),
            showOverflowTooltip: true,
            minWidth: 160,
            slot: 'cmr_loose_hour'
          },
          {
            // 货站装车段时效
            prop: 'cmr_in_hour',
            label: this.$t('validity.validityBols.cmr_in_hour'),
            showOverflowTooltip: true,
            minWidth: 160,
            slot: 'cmr_in_hour'
          },
          {
            // 货站到EDT段时效
            prop: 'edt_in_hour',
            label: this.$t('validity.validityBols.edt_in_hour'),
            showOverflowTooltip: true,
            minWidth: 160,
            slot: 'edt_in_hour'
          },
          {
            // 出库调整段时效
            prop: 'box_hour',
            label: this.$t('validity.validityBols.box_hour'),
            showOverflowTooltip: true,
            minWidth: 160,
            slot: 'box_hour'
          },
          {
            // 清关段时效
            prop: 'customs_hour',
            label: this.$t('validity.validityBols.customs_hour'),
            showOverflowTooltip: true,
            minWidth: 160,
            slot: 'customs_hour'
          },
          {
            // 库操段时效
            prop: 'station_hour',
            label: this.$t('validity.validityBols.station_hour'),
            showOverflowTooltip: true,
            minWidth: 160,
            slot: 'station_hour'
          },
          {
            columnKey: 'action',
            label: this.$t('basics.action'),
            width: 220,
            align: 'center',
            resizable: false,
            slot: 'action',
            hideInSetting: true,
            fixed: 'right'
          }
        ],
        // 表格选中数据
        selection: [],
        current: null,
        currentRow: null,
        // 是否显示编辑弹窗
        showEdit: false,
        setting: {},
        showDetail: false,
        showDetailAwb: false,
        loading: false
      };
    },
    computed: {
      // 是否开启响应式布局
      styleResponsive() {
        return this.$store.state.theme.styleResponsive;
      }
    },
    created() {
      this.getSaleroomData(this.data);
    },
    async mounted() {
      const config = await getProcess();
      this.setting = config.content;
    },
    methods: {
      /* 获取销售量数据 */
      getSaleroomData(search) {
        if (search.isSearch !== true) {
          return;
        }
        this.optionStr = null;
        this.bolIds = [];
        this.reload();
        this.isFirst = true;
        const loading = this.$loading({ lock: true });
        validityBol(search)
          .then((data) => {
            this.bolData = data.result;
            this.initPieChart();
            loading.close();
          })
          .catch((e) => {
            loading.close();
            this.$message.error(e);
          });
        this.xAxisData = [];
        this.seriesDataS2 = [];
        this.seriesDataS1 = {
          cmr_out_hour: [],
          noa_pmc_hour: [],
          plan_hour: [],
          noa_loose_hour: [],
          cmr_loose_hour: [],
          cmr_in_hour: [],
          customs_hour: [],
          station_hour: [],
          edt_in_hour: [],
          box_hour: []
        };
        if (typeof search.type !== 'undefined') {
          this.getSaleroomDataLine(search);
        }
        this.reloadList(search);
      },
      /* 获取销售量数据 */
      getSaleroomDataLine(search) {
        if (typeof search.type === 'undefined') {
          if (this.isFirst !== true) {
            // this.$message.error(this.$t('validity.pleaseAreaChoose') + '');
          } else {
            this.isFirst = false;
          }
          return;
        }
        if (search.type > 4 || search.type < 1) {
          // this.$message.error(this.$t('validity.pleaseAreaChoose') + '');
          return;
        }
        // const loading = this.$loading({ lock: true });
        getValidityBolHour(search)
          .then((data) => {
            let arrList = data.result.arrList;
            const leng = arrList.length;
            let data2 = [];
            for (let i = 0; i < arrList.length; i++) {
              data2.push(0);
            }
            data.result.reasonList.forEach((d) => {
              this.seriesDataS2[d.id] = {
                name: d.reason,
                data: [...data2]
              };
            });
            for (let i = 0; i < leng; i++) {
              if (search.type == 1) {
                this.xAxisData.push(
                  this.$t('validity.validityPeriodBegin') +
                    (i + 1) +
                    this.$t('validity.validityPeriod.s1')
                );
              } else if (search.type == 2) {
                this.xAxisData.push(
                  this.$t('validity.validityPeriodBegin') +
                    (i + 1) +
                    this.$t('validity.validityPeriod.s2')
                );
              } else if (search.type == 3) {
                this.xAxisData.push(
                  this.$t('validity.validityPeriodBegin') +
                    (i + 1) +
                    this.$t('validity.validityPeriod.s3')
                );
              } else if (search.type == 4) {
                this.xAxisData.push(
                  this.$t('validity.validityPeriodBegin') +
                    (i + 1) +
                    this.$t('validity.validityPeriod.s4')
                );
              }
              Object.entries(arrList[i].bolProcess).forEach(([key, value]) => {
                if (this.seriesDataS1[key]) {
                  this.seriesDataS1[key].push(value.percent ?? 0); // 加默认值以防 undefined
                }
              });
              Object.entries(arrList[i].reasonProcess).forEach(
                ([key, value]) => {
                  if (
                    this.seriesDataS2[key] &&
                    this.seriesDataS2[key]['data']
                  ) {
                    this.seriesDataS2[key]['data'][i] = value;
                  }
                }
              );
            }
            this.onSaleTypeChange();
            // loading.close();
          })
          .catch((e) => {
            // loading.close();
            this.$message.error(e);
          });
      },
      changHandle() {
        if (this.optionStr == null || this.optionStr === '') {
          this.bolIds = [];
          this.reload();
          return;
        }
        if (this.statusId == null || this.statusId === '') {
          this.bolIds = [];
          this.reload();
          return;
        }
        let data = [];
        switch (this.optionStr) {
          case 'cmrOutHourOption':
            data = this.bolData.cmr_out_hour;
            break;
          case 'planHourOption':
            data = this.bolData.cmr_out_hour;
            break;
          case 'noaPmcHourOption':
            data = this.bolData.noa_pmc_hour;
            break;
          case 'noaLooseHourOption':
            data = this.bolData.noa_loose_hour;
            break;
          case 'cmrPmcHourOption':
            data = this.bolData.cmr_pmc_hour;
            break;
          case 'cmrLooseHourOption':
            data = this.bolData.cmr_loose_hour;
            break;
          case 'cmrInHourOption':
            data = this.bolData.cmr_in_hour;
            break;
          case 'edtInHourOption':
            data = this.bolData.edt_in_hour;
            break;
          case 'customsHourOption':
            data = this.bolData.customs_hour;
            break;
          case 'stationHourOption':
            data = this.bolData.station_hour;
            break;
          case 'boxHourOption':
            data = this.bolData.box_hour;
            break;
        }
        if (typeof data === 'undefined') {
          this.bolIds = [];
          this.reload();
          return;
        }
        switch (this.statusId) {
          case 0:
            this.bolIds = data.bol_0;
            break;
          case 1:
            this.bolIds = data.bol_1;
            break;
          case 2:
            this.bolIds = data.bol_2;
            break;
        }
        this.reload();
      },
      handleChartClick(event) {
        if (this.saleSearch.type === 'piechart') {
          // 检查是否点击了数据系列
          // 获取数据点的名称和值
          // let dataIndex = event.dataIndex;
          let data = event.data; //包裹数量
          // let seriesIndex = event.seriesIndex;
          this.bolIds = Object.values(data.bol);
          this.optionStr = data.optionStr;
          this.statusId = data.statusId;
          this.reload();
        }
      },
      datasource({ page, limit, where, order }) {
        if (typeof where === 'undefined') {
          return [];
        }
        if (typeof where === 'object' && Object.values(where).length === 0) {
          return [];
        }
        if (where.length === 0) {
          return [];
        }
        return validityBolList({
          ...order,
          bolIds: where,
          page: page,
          num: limit,
          isAll: false
        });
      },
      /* 表格数据源 */
      datasourceAddress({ page, limit, where, order }) {
        return validityAddressLists({
          ...where,
          ...order,
          page: page,
          num: limit
        });
      },
      reload() {
        this.$nextTick(() => {
          if (typeof this.$refs.tablelog === 'undefined') {
            return;
          }
          this.$refs.tablelog.reload({
            page: 1,
            where: this.bolIds
          });
        });
      },
      reloadList(where) {
        this.$nextTick(() => {
          if (typeof this.$refs.addressList === 'undefined') {
            return;
          }
          this.$refs.addressList.reload({
            page: 1,
            where: where
          });
        });
      },
      detailawb(row) {
        //查看
        this.current = {
          ...row
        };
        this.showDetailAwb = true;
      },
      batchEditReason() {
        this.current = {
          reason_key_arr: this.selection.map((i) => ({
            bol_id: i.bol_id,
            address_code: i.address_code
          }))
        };
        this.showEdit = true;
      },
      editReason(row) {
        this.current = {
          reason_key_arr: [
            { bol_id: row.bol_id, address_code: row.address_code }
          ]
        };
        this.showEdit = true;
      },
      exportBols() {
        if (typeof this.bolIds === 'undefined') {
          this.$message.error(this.$t('validity.exportXlsError') + '');
          return;
        }
        if (
          typeof this.bolIds === 'object' &&
          Object.values(this.bolIds).length === 0
        ) {
          this.$message.error(this.$t('validity.exportXlsError') + '');
          return;
        }
        if (this.bolIds.length === 0) {
          this.$message.error(this.$t('validity.exportXlsError') + '');
          return;
        }
        this.loading = this.$loading({ lock: true });
        validityBolListExp({ bolIds: this.bolIds, isAll: true })
          .then((data) => {
            this.initXlsx(data.result);
          })
          .catch((e) => {
            this.loading.close();
            if (typeof e.message === 'undefined') {
              this.$message.error(e);
            } else {
              this.$message.error(e.message);
            }
          });
      },
      initXlsx(data) {
        const array = [];
        array.push([
          this.$t('order.awbExp.col3'),
          this.$t('basics.createTime'),
          this.$t('awb.awb.col21'),
          this.$t('awb.noa.time'),
          this.$t('order.awbExp.col17'),
          this.$t('validity.validityBols.bol_cmr_in'),
          this.$t('awb.box.stock_in_time'),
          this.$t('validity.validityBols.bol_cmr_out'),
          this.$t('basicsMenu.address.station'),
          this.$t('basicsMenu.loadmode.loadmode')
        ]);
        data.forEach((d) => {
          array.push([
            d.bol_no,
            d.created_at,
            d.bol_flight,
            d.bol_noa,
            d.bol_customs,
            d.bol_cmr_in,
            d.scan_in,
            d.bol_cmr_out,
            d.bol_warehouse,
            d.bol_transport_type
          ]);
        });
        this.loading.close();
        const sheetName = 'Sheet1';
        const workbook = {
          SheetNames: [sheetName],
          Sheets: {}
        };
        workbook.Sheets[sheetName] = utils.aoa_to_sheet(array);
        writeFile(workbook, 'Bol.xlsx');
      },
      onListChange() {
        console.log('dd');
      },
      /* 销售量tab选择改变事件 */
      onSaleTypeChange() {
        if (this.saleSearch.type === 'piechart') {
          this.initPieChart();
          return;
        }
        let xAxisData = this.xAxisData;
        let series = [];
        let legend = [];
        var emphasisStyle = {
          itemStyle: {
            shadowBlur: 10,
            shadowColor: 'rgba(0,0,0,0.3)'
          }
        };
        // let label = { show: true, position: 'top' };
        let label = {
          show: true,
          position: 'top',
          formatter: (params) => params.value + '%'
        };
        if (this.saleSearch.type === 'saleroom') {
          console.log(this.seriesDataS1);
          Object.entries(this.seriesDataS1).forEach(([key, value]) => {
            let name = '';
            switch (key) {
              case 'cmr_out_hour':
                name = this.$t('validity.validityBols.cmr_out_hour') + '';
                break;
              case 'plan_hour':
                name = this.$t('validity.validityBols.plan_hour') + '';
                break;
              case 'noa_pmc_hour':
                name = this.$t('validity.validityBols.noa_pmc_hour') + '';
                break;
              case 'noa_loose_hour':
                name = this.$t('validity.validityBols.noa_loose_hour') + '';
                break;
              case 'cmr_pmc_hour':
                name = this.$t('validity.validityBols.cmr_pmc_hour') + '';
                break;
              case 'cmr_loose_hour':
                name = this.$t('validity.validityBols.cmr_loose_hour') + '';
                break;
              case 'cmr_in_hour':
                name = this.$t('validity.validityBols.cmr_in_hour') + '';
                break;
              case 'edt_in_hour':
                name = this.$t('validity.validityBols.edt_in_hour') + '';
                break;
              case 'box_hour':
                name = this.$t('validity.validityBols.box_hour') + '';
                break;
              case 'customs_hour':
                name = this.$t('validity.validityBols.customs_hour') + '';
                break;
              case 'station_hour':
                name = this.$t('validity.validityBols.station_hour') + '';
                break;
            }
            legend.push(name + '');
            console.log(legend.length);
            series.push({
              name: name,
              type: 'line',
              emphasis: emphasisStyle,
              label: label,
              data: value
            });
          });
        } else {
          this.seriesDataS2.forEach((d) => {
            legend.push(d.name);
            series.push({
              name: d.name,
              type: 'line',
              emphasis: emphasisStyle,
              label: label,
              data: d.data
            });
          });
        }
        this.saleChartOption = {
          legend: {
            data: legend
          },
          xAxis: {
            data: xAxisData,
            name: 'X Axis',
            type: 'category',
            boundaryGap: false
          },
          yAxis: {},
          grid: {
            bottom: 100
          },
          series: series
        };
      },
      initPieChart() {
        if (this.saleSearch.type !== 'piechart') {
          return;
        }
        let name = '';
        let data = [];
        let option = this.showoptionStr;
        switch (this.showoptionStr) {
          case 'cmrOutHourOption':
            data = this.bolData.cmr_out_hour;
            name = this.$t('validity.validityBols.cmr_out_hour') + '';
            break;
          case 'planHourOption':
            data = this.bolData.plan_hour;
            name = this.$t('validity.validityBols.plan_hour') + '';
            break;
          case 'noaPmcHourOption':
            data = this.bolData.noa_pmc_hour;
            name = this.$t('validity.validityBols.noa_pmc_hour') + '';
            break;
          case 'noaLooseHourOption':
            data = this.bolData.noa_loose_hour;
            name = this.$t('validity.validityBols.noa_loose_hour') + '';
            break;
          case 'cmrPmcHourOption':
            data = this.bolData.cmr_pmc_hour;
            name = this.$t('validity.validityBols.cmr_pmc_hour') + '';
            break;
          case 'cmrLooseHourOption':
            data = this.bolData.cmr_loose_hour;
            name = this.$t('validity.validityBols.cmr_loose_hour') + '';
            break;
          case 'cmrInHourOption':
            data = this.bolData.cmr_in_hour;
            name = this.$t('validity.validityBols.cmr_in_hour') + '';
            break;
          case 'edtInHourOption':
            data = this.bolData.edt_in_hour;
            name = this.$t('validity.validityBols.edt_in_hour') + '';
            break;
          case 'boxHourOption':
            data = this.bolData.box_hour;
            name = this.$t('validity.validityBols.box_hour') + '';
            break;
          case 'customsHourOption':
            data = this.bolData.customs_hour;
            name = this.$t('validity.validityBols.customs_hour') + '';
            break;
          case 'stationHourOption':
            data = this.bolData.station_hour;
            name = this.$t('validity.validityBols.station_hour') + '';
            break;
        }
        const bol_0 = data.bol_0;
        const bol_1 = data.bol_1;
        const bol_2 = data.bol_2;
        let optionData = [];
        optionData.push({
          value: Object.values(bol_0).length,
          name: this.$t('validity.validityStatus.s0'),
          bol: bol_0,
          optionStr: option,
          statusId: 0
        });
        optionData.push({
          value: Object.values(bol_1).length,
          name: this.$t('validity.validityStatus.s1'),
          bol: bol_1,
          optionStr: option,
          statusId: 1
        });
        optionData.push({
          value: Object.values(bol_2).length,
          name: this.$t('validity.validityStatus.s2'),
          bol: bol_2,
          optionStr: option,
          statusId: 2
        });
        this.saleChartOption = {
          tooltip: {
            trigger: 'item',
            formatter: '{a} <br/>{b}: {c} ({d}%)'
          },
          legend: {
            type: 'scroll',
            orient: 'vertical',
            right: 10,
            top: 20,
            bottom: 20,
            data: [
              this.$t('validity.validityStatus.s0'),
              this.$t('validity.validityStatus.s1'),
              this.$t('validity.validityStatus.s2')
            ]
          },
          series: [
            {
              name: name,
              type: 'pie',
              radius: ['40%', '70%'],
              avoidLabelOverlap: false,
              label: {
                show: true,
                position: 'outside',
                formatter: '{b}: {c} ({d}%)'
              },
              labelLine: {
                show: true
              },
              data: optionData
            }
          ]
        };
      }
    }
  };
</script>

<style lang="scss" scoped>
  /* 销售额、访问量工具栏 */
  .demo-monitor-tool {
    padding: 0 20px;

    .demo-monitor-tabs {
      height: 51px;
    }

    :deep(.el-tabs__item) {
      height: 51px;
      line-height: 51px;
      font-size: 15px;
    }

    :deep(.el-tabs__nav-wrap:after) {
      display: none;
    }
  }

  /* 小标题 */
  .demo-monitor-title {
    padding: 0 20px;
    margin: 15px 0 5px 0;
  }

  /* 排名 item */
  .demo-monitor-rank-item {
    padding: 0 20px;
    line-height: 20px;
    margin-top: 18px;
  }
  .analysis-chart-card-num {
    font-size: 30px;
  }

  .analysis-chart-card-content {
    height: 40px;
    box-sizing: border-box;
    margin-bottom: 12px;
  }

  .analysis-chart-card-text {
    padding-top: 12px;
  }
</style>
