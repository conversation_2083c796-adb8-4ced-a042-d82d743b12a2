<template>
  <div class="ele-body ele-body-card">
    <template>
      <el-card shadow="never" body-style="padding: 22px 22px 7px 22px;">
        <!-- 搜索表单 -->
        <el-form
          label-width="130px"
          class="ele-form-search"
          @keyup.enter.native="search"
          @submit.native.prevent
        >
          <el-row :gutter="15">
            <el-col :lg="8" :md="12">
              <el-form-item :label="`${$t('awb.awb.col9')}:`" prop="user_id">
                <user-select v-model="where.user_id" />
              </el-form-item>
            </el-col>
            <el-col :lg="8" :md="12">
              <el-form-item :label="`${$t('car.addressOut')}:`">
                <address-select v-model="where.address_code" />
              </el-form-item>
            </el-col>
            <el-col :lg="8" :md="12">
              <el-form-item :label="$t('awb.awb.col21') + ':'" prop="ata_time">
                <el-date-picker
                  v-model="where.ata_time"
                  type="datetimerange"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  :range-separator="$t('basics.to')"
                  class="ele-fluid"
                  :start-placeholder="$t('basics.beginTime')"
                  :end-placeholder="$t('basics.endTime')"
                >
                </el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :lg="8" :md="12">
              <el-form-item :label="`${$t('validity.validityParcelsTrend')}:`">
                <el-select
                  v-model="where.type"
                  :placeholder="this.$t('basics.pleaseChoose')"
                  clearable
                  class="ele-fluid"
                >
                  <el-option
                    :label="this.$t('validity.validityPeriod.s1')"
                    :value="1"
                  />
                  <el-option
                    :label="this.$t('validity.validityPeriod.s2')"
                    :value="2"
                  />
                  <el-option
                    :label="this.$t('validity.validityPeriod.s3')"
                    :value="3"
                  />
                  <el-option
                    :label="this.$t('validity.validityPeriod.s4')"
                    :value="4"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :lg="8" :md="12">
              <div class="ele-form-actions">
                <el-button
                  type="primary"
                  icon="el-icon-search"
                  class="ele-btn-icon"
                  @click="search"
                >
                  {{ $t('basics.query') }}
                </el-button>
                <el-button @click="reset">{{ $t('basics.reset') }}</el-button>
              </div>
            </el-col>
          </el-row>
        </el-form>
      </el-card>
    </template>
    <sale-card :data="nowWhere" ref="saleCardRef" />
  </div>
</template>

<script>
  import SaleCard from './components/sale-card.vue';
  import UserSelect from './components/user-select.vue';
  import AddressSelect from '@/components/AddressSelect';

  const DEFAULT_WHERE = {
    address_code: '',
    ata_time: null,
    isSearch: false
  };
  export default {
    name: 'DashboardAnalysis',
    components: {
      UserSelect,
      AddressSelect,
      SaleCard
    },
    computed: {
      // 是否开启响应式布局
      styleResponsive() {
        return this.$store.state.theme.styleResponsive;
      }
    },
    data() {
      return {
        where: { ...DEFAULT_WHERE },
        nowWhere: {}
      };
    },
    methods: {
      /* 重置搜索 */
      reset() {
        this.where = { ...DEFAULT_WHERE };
      },
      /* 搜索 */
      search() {
        this.where.isSearch = true;
        this.$refs.saleCardRef.getSaleroomData(this.where);
        //解决搜索 也搜索下级的数据
        // this.nowWhere = this.where;
      }
    }
  };
</script>
