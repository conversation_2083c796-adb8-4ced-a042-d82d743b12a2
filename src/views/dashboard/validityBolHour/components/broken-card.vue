<template>
  <el-card shadow="never" body-style="padding: 0;">
    <div class="ele-cell demo-monitor-tool">
      <div class="ele-cell-content">
        <el-tabs
          v-model="saleSearch.type"
          class="demo-monitor-tabs"
          @tab-click="onSaleTypeChange"
        >
          <el-tab-pane label="时效" name="saleroom" />
        </el-tabs>
      </div>
      <!--
      <div :class="['ele-inline-block', { 'hidden-xs-only': styleResponsive }]">
        <el-radio-group v-model="saleSearch.dateType" size="small">
          <el-radio-button :label="0">今天</el-radio-button>
          <el-radio-button :label="1">本周</el-radio-button>
          <el-radio-button :label="2">本月</el-radio-button>
          <el-radio-button :label="3">本年</el-radio-button>
        </el-radio-group>
      </div>-->
    </div>
    <el-divider />
    <el-row>
      <el-col v-bind="styleResponsive ? { lg: 24, md: 16 } : { span: 24 }">
        <div class="demo-monitor-title">
          <span>时效</span>
        </div>
        <v-chart
          ref="saleChart"
          style="height: 485px"
          :option="saleChartOption"
        />
      </el-col>
    </el-row>
  </el-card>
</template>

<script>
  import { use } from 'echarts/core';
  import { CanvasRenderer } from 'echarts/renderers';
  import { LineChart } from 'echarts/charts';
  import {
    GridComponent,
    TooltipComponent,
    LegendComponent
  } from 'echarts/components';
  import VChart from 'vue-echarts';
  import { echartsMixin } from '@/utils/echarts-mixin';
  import { validityLineList } from '@/api/order/validity';

  use([
    CanvasRenderer,
    LineChart,
    GridComponent,
    TooltipComponent,
    LegendComponent
  ]);

  export default {
    components: { VChart },
    mixins: [echartsMixin(['saleChart'])],
    props: {
      // 查询的数据的数据
      data: Object
    },
    data() {
      return {
        // 销售量搜索参数
        saleSearch: {
          type: 'saleroom',
          dateType: 0,
          datetime: ''
        },
        area: [],
        // 销售量趋势数据
        inCounts: [],
        // 访问量趋势数据
        outCounts: [],
        // 门店排名数据
        xAxisData: [
          '第一周',
          '第二周',
          '第三周',
          '第四周',
          '第五周',
          '第六周',
          '第七周'
        ],
        // 销售额柱状图配置
        saleChartOption: {}
      };
    },
    computed: {
      // 是否开启响应式布局
      styleResponsive() {
        return this.$store.state.theme.styleResponsive;
      }
    },
    created() {
      this.getSaleroomData(this.data);
    },
    methods: {
      /* 获取销售量数据 */
      getSaleroomData(search) {
        if (typeof search.ata_time === 'undefined') {
          return;
        }
        if (!Array.isArray(search.ata_time)) {
          this.$message.error('请选择实际落地时间');
          return;
        }
        const loading = this.$loading({ lock: true });
        validityLineList(search)
          .then((data) => {
            this.area = data.area;
            this.inCounts = data.in;
            this.outCounts = data.out;
            loading.close();
            this.onSaleTypeChange();
          })
          .catch((e) => {
            loading.close();
            this.$message.error(e);
          });
      },

      /* 销售量tab选择改变事件 */
      onSaleTypeChange() {
        let data1 = [];
        let data2 = [];
        let xAxisData = [];
        data1 = this.inCounts;
        data2 = this.outCounts;
        for (var i = 1; i <= data1.length; i++) {
          xAxisData.push('第' + i + '周');
        }
        this.saleChartOption = {
          legend: {
            data: ['入库', '出库'],
            right: '5%'
          },
          tooltip: {},
          xAxis: {
            data: xAxisData,
            name: 'X Axis',
            type: 'category',
            boundaryGap: false
          },
          yAxis: {},
          grid: {
            bottom: 100
          },
          series: [
            {
              name: '入库',
              type: 'line',
              stack: 'Total',
              data: data1
            },
            {
              name: '出库',
              type: 'line',
              stack: 'Total',
              data: data2
            }
          ]
        };
      }
    }
  };
</script>

<style lang="scss" scoped>
  /* 销售额、访问量工具栏 */
  .demo-monitor-tool {
    padding: 0 20px;

    .demo-monitor-tabs {
      height: 51px;
    }

    :deep(.el-tabs__item) {
      height: 51px;
      line-height: 51px;
      font-size: 15px;
    }

    :deep(.el-tabs__nav-wrap:after) {
      display: none;
    }
  }

  /* 小标题 */
  .demo-monitor-title {
    padding: 0 20px;
    margin: 15px 0 5px 0;
  }

  /* 排名 item */
  .demo-monitor-rank-item {
    padding: 0 20px;
    line-height: 20px;
    margin-top: 18px;
  }
</style>
