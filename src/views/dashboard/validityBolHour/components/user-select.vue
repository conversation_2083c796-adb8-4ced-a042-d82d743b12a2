<!-- 角色选择下拉框 -->
<template>
  <el-select
    clearable
    :value="value"
    class="ele-block"
    :placeholder="placeholder"
    :disabled="disabled"
    @input="updateValue"
  >
    <el-option
      v-for="item in data"
      :key="item.id"
      :value="item.id"
      :label="item.nickname"
    />
  </el-select>
</template>

<script>
  import { nopageUsers } from '@/api/system/user';

  export default {
    name: 'RoleSelect',
    props: {
      // 选中的数据(v-modal)
      value: Number,
      disabled:Boolean,
      // 提示信息
      placeholder: {
        type: String,
        default: ''
      }
    },
    data() {
      return {
        data: []
      };
    },
    created() {
      /* 获取角色数据 */
      nopageUsers({
        page: 1,
        num: 10000,
        role_id:3
      })
        .then((list) => {
          this.data = list
        })
        .catch((e) => {
          this.$message.error(e);
        });
    },
    methods: {
      updateValue(value) {
        this.$emit(
          'callback',
          this.data.find((item) => item.id === value)
        );
        this.$emit('input', value);
      },
      click(value) {
        console.log(value);
      }
    }
  };
</script>
