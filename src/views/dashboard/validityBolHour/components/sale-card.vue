<template>
  <el-card shadow="never" body-style="padding: 0;">
    <div class="ele-cell demo-monitor-tool">
      <div class="ele-cell-content">
        <el-tabs
          v-model="saleSearch.type"
          class="demo-monitor-tabs"
          @tab-click="onSaleTypeChange"
        >
          <el-tab-pane
            :label="this.$t('validity.validityLine.s1')"
            name="saleroom"
          />
          <el-tab-pane
            :label="this.$t('validity.validityLine.s2')"
            name="broken"
          />
        </el-tabs>
      </div>
    </div>
    <el-divider />
    <el-row>
      <el-col v-bind="styleResponsive ? { lg: 24, md: 16 } : { span: 24 }">
        <div class="demo-monitor-title">
          <span v-if="saleSearch.type === 'saleroom'">
            {{ this.$t('validity.validityLine.s1') }}
          </span>
          <span v-else>
            {{ this.$t('validity.validityLine.s2') }}
          </span>
        </div>
        <v-chart
          ref="saleChart"
          style="height: 485px"
          :option="saleChartOption"
        />
      </el-col>
    </el-row>
  </el-card>
</template>

<script>
  import { use } from 'echarts/core';
  import { CanvasRenderer } from 'echarts/renderers';
  import { BarChart, LineChart } from 'echarts/charts';
  import {
    GridComponent,
    TooltipComponent,
    LegendComponent
  } from 'echarts/components';
  import VChart from 'vue-echarts';
  import { echartsMixin } from '@/utils/echarts-mixin';
  import { getValidityBolHour } from '@/api/order/validity';

  use([
    CanvasRenderer,
    BarChart,
    LineChart,
    GridComponent,
    TooltipComponent,
    LegendComponent
  ]);

  export default {
    components: { VChart },
    mixins: [echartsMixin(['saleChart'])],
    props: {
      // 查询的数据的数据
      data: Object
    },
    data() {
      return {
        // 销售量搜索参数
        saleSearch: {
          type: 'saleroom',
          dateType: 0,
          datetime: ''
        },
        nowSearch: {},
        // 销售量趋势数据
        inCounts: [],
        // 访问量趋势数据
        outCounts: [],
        inPercentage: [],
        outPercentage: [],
        ninetyInTime: 0,
        ninetyOutTime: 0,
        inAllCount: 0,
        outAllCount: 0,
        isFirst: true,
        inBrokens: [],
        outBrokens: [],
        areaBrokens: [],
        xAxisDataSelect: [],
        displayShowParcel: false,
        selectType: '',
        selectArea: '',
        // 销售额柱状图配置
        saleChartOption: {},
        xAxisData: [],
        seriesDataS1: {
          cmr_out_hour: [],
          noa_pmc_hour: [],
          plan_hour: [],
          noa_loose_hour: [],
          cmr_loose_hour: [],
          cmr_in_hour: [],
          customs_hour: [],
          station_hour: [],
          edt_in_hour: [],
          box_hour: []
        },
        seriesDataS2: []
      };
    },
    computed: {
      // 是否开启响应式布局
      styleResponsive() {
        return this.$store.state.theme.styleResponsive;
      }
    },
    created() {
      this.getSaleroomData(this.data);
    },
    methods: {
      /* 获取销售量数据 */
      getSaleroomData(search) {
        if (typeof search.type === 'undefined') {
          if (this.isFirst !== true) {
            this.$message.error(this.$t('validity.pleaseAreaChoose') + '');
          } else {
            this.isFirst = false;
          }
          return;
        }
        if (search.type > 4 || search.type < 1) {
          this.$message.error(this.$t('validity.pleaseAreaChoose') + '');
          return;
        }
        const loading = this.$loading({ lock: true });
        getValidityBolHour(search)
          .then((data) => {
            this.xAxisData = [];
            this.seriesDataS2 = [];
            this.seriesDataS1 = {
              cmr_out_hour: [],
              noa_pmc_hour: [],
              plan_hour: [],
              noa_loose_hour: [],
              cmr_loose_hour: [],
              cmr_in_hour: [],
              customs_hour: [],
              station_hour: [],
              edt_in_hour: [],
              box_hour: []
            };
            let arrList = data.result.arrList;
            const leng = arrList.length;
            let data2 = [];
            for (let i = 0; i < arrList.length; i++) {
              data2.push(0);
            }
            data.result.reasonList.forEach((d) => {
              this.seriesDataS2[d.id] = {
                name: d.reason,
                data: [...data2]
              };
            });
            for (let i = 0; i < leng; i++) {
              if (search.type == 1) {
                this.xAxisData.push(
                  this.$t('validity.validityPeriodBegin') +
                    (i + 1) +
                    this.$t('validity.validityPeriod.s1')
                );
              } else if (search.type == 2) {
                this.xAxisData.push(
                  this.$t('validity.validityPeriodBegin') +
                    (i + 1) +
                    this.$t('validity.validityPeriod.s2')
                );
              } else if (search.type == 3) {
                this.xAxisData.push(
                  this.$t('validity.validityPeriodBegin') +
                    (i + 1) +
                    this.$t('validity.validityPeriod.s3')
                );
              } else if (search.type == 4) {
                this.xAxisData.push(
                  this.$t('validity.validityPeriodBegin') +
                    (i + 1) +
                    this.$t('validity.validityPeriod.s4')
                );
              }
              Object.entries(arrList[i].bolProcess).forEach(([key, value]) => {
                if (this.seriesDataS1[key]) {
                  this.seriesDataS1[key].push(value.percent ?? 0); // 加默认值以防 undefined
                }
              });
              Object.entries(arrList[i].reasonProcess).forEach(
                ([key, value]) => {
                  if (
                    this.seriesDataS2[key] &&
                    this.seriesDataS2[key]['data']
                  ) {
                    this.seriesDataS2[key]['data'][i] = value;
                  }
                }
              );
            }
            this.onSaleTypeChange();
            loading.close();
          })
          .catch((e) => {
            loading.close();
            this.$message.error(e);
          });
      },
      /* 销售量tab选择改变事件 */
      onSaleTypeChange() {
        let xAxisData = this.xAxisData;
        let series = [];
        let legend = [];
        var emphasisStyle = {
          itemStyle: {
            shadowBlur: 10,
            shadowColor: 'rgba(0,0,0,0.3)'
          }
        };
        // let label = { show: true, position: 'top' };
        let label = {
          show: true,
          position: 'top',
          formatter: (params) => params.value + '%'
        };
        if (this.saleSearch.type === 'saleroom') {
          console.log(this.seriesDataS1);
          Object.entries(this.seriesDataS1).forEach(([key, value]) => {
            let name = '';
            switch (key) {
              case 'cmr_out_hour':
                name = this.$t('validity.validityBols.cmr_out_hour') + '';
                break;
              case 'plan_hour':
                name = this.$t('validity.validityBols.plan_hour') + '';
                break;
              case 'noa_pmc_hour':
                name = this.$t('validity.validityBols.noa_pmc_hour') + '';
                break;
              case 'noa_loose_hour':
                name = this.$t('validity.validityBols.noa_loose_hour') + '';
                break;
              case 'cmr_pmc_hour':
                name = this.$t('validity.validityBols.cmr_pmc_hour') + '';
                break;
              case 'cmr_loose_hour':
                name = this.$t('validity.validityBols.cmr_loose_hour') + '';
                break;
              case 'cmr_in_hour':
                name = this.$t('validity.validityBols.cmr_in_hour') + '';
                break;
              case 'edt_in_hour':
                name = this.$t('validity.validityBols.edt_in_hour') + '';
                break;
              case 'box_hour':
                name = this.$t('validity.validityBols.box_hour') + '';
                break;
              case 'customs_hour':
                name = this.$t('validity.validityBols.customs_hour') + '';
                break;
              case 'station_hour':
                name = this.$t('validity.validityBols.station_hour') + '';
                break;
            }
            legend.push(name + '');
            console.log(legend.length);
            series.push({
              name: name,
              type: 'line',
              emphasis: emphasisStyle,
              label: label,
              data: value
            });
          });
        } else {
          this.seriesDataS2.forEach((d) => {
            legend.push(d.name);
            series.push({
              name: d.name,
              type: 'line',
              emphasis: emphasisStyle,
              label: label,
              data: d.data
            });
          });
        }
        this.saleChartOption = {
          legend: {
            data: legend
          },
          xAxis: {
            data: xAxisData,
            name: 'X Axis',
            type: 'category',
            boundaryGap: false
          },
          yAxis: {},
          grid: {
            bottom: 100
          },
          series: series
        };
      }
    }
  };
</script>

<style lang="scss" scoped>
  /* 销售额、访问量工具栏 */
  .demo-monitor-tool {
    padding: 0 20px;

    .demo-monitor-tabs {
      height: 51px;
    }

    :deep(.el-tabs__item) {
      height: 51px;
      line-height: 51px;
      font-size: 15px;
    }

    :deep(.el-tabs__nav-wrap:after) {
      display: none;
    }
  }

  /* 小标题 */
  .demo-monitor-title {
    padding: 0 20px;
    margin: 15px 0 5px 0;
  }

  /* 排名 item */
  .demo-monitor-rank-item {
    padding: 0 20px;
    line-height: 20px;
    margin-top: 18px;
  }
  .analysis-chart-card-num {
    font-size: 30px;
  }

  .analysis-chart-card-content {
    height: 40px;
    box-sizing: border-box;
    margin-bottom: 12px;
  }

  .analysis-chart-card-text {
    padding-top: 12px;
  }
</style>
