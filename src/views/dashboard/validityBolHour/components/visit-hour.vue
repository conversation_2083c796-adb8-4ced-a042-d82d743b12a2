<template>
  <el-card
    shadow="never"
    header="最近1小时访问情况"
    body-style="padding: 14px 5px 0 0;"
  >
    <v-chart
      ref="visitHourChart"
      style="height: 323px"
      :option="visitHourChartOption"
    />
  </el-card>
</template>

<script>
  import { use } from 'echarts/core';
  import { CanvasRenderer } from 'echarts/renderers';
  import { LineChart } from 'echarts/charts';
  import {
    GridComponent,
    TooltipComponent,
    LegendComponent
  } from 'echarts/components';
  import VChart from 'vue-echarts';
  import { getVisitHourList } from '@/api/dashboard/analysis';
  import { echartsMixin } from '@/utils/echarts-mixin';

  use([
    CanvasRenderer,
    LineChart,
    GridComponent,
    TooltipComponent,
    LegendComponent
  ]);

  export default {
    components: { VChart },
    mixins: [echartsMixin(['visitHourChart'])],
    data() {
      return {
        // 最近1小时访问情况折线图配置
        visitHourChartOption: {}
      };
    },
    created() {
      this.getVisitHourData();
    },
    methods: {
      /* 获取最近1小时访问情况数据 */
      getVisitHourData() {
        getVisitHourList()
          .then((data) => {
            data = JSON.parse(
              '[{"time":"16:00","visits":1.5,"views":4.5,"count":3.5},{"time":"16:00","visits":39,"views":169,"count":30},{"time":"16:00","visits":152,"views":400,"count":39},{"time":"16:15","visits":94,"views":285,"count":39},{"time":"16:20","visits":102,"views":316,"count":36},{"time":"16:25","visits":86,"views":148,"count":25},{"time":"16:30","visits":39,"views":150,"count":35},{"time":"16:35","visits":38,"views":234,"count":353},{"time":"16:40","visits":95,"views":158,"count":35},{"time":"16:45","visits":30,"views":100,"count":352},{"time":"16:50","visits":86,"views":266,"count":351}]'
            );

            this.visitHourChartOption = {
              tooltip: {
                trigger: 'axis'
              },
              legend: {
                data: ['浏览量', '访问量', '数量'],
                right: 20
              },
              xAxis: [
                {
                  type: 'category',
                  boundaryGap: false,
                  data: data.map((d) => d.time)
                }
              ],
              yAxis: [
                {
                  type: 'value'
                }
              ],
              series: [
                {
                  name: '浏览量',
                  type: 'line',
                  smooth: true,
                  symbol: 'none',
                  areaStyle: {
                    opacity: 0.5
                  },
                  data: data.map((d) => d.views)
                },
                {
                  name: '访问量',
                  type: 'line',
                  smooth: true,
                  symbol: 'none',
                  areaStyle: {
                    opacity: 0.5
                  },
                  data: data.map((d) => d.visits)
                },
                {
                  name: '数量',
                  type: 'line',
                  smooth: true,
                  symbol: 'none',
                  areaStyle: {
                    opacity: 0.5
                  },
                  data: data.map((d) => d.count)
                }
              ]
            };
          })
          .catch((e) => {
            this.$message.error(e.message);
          });
      }
    }
  };
</script>
