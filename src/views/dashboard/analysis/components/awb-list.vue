<template>
  <el-card shadow="never" body-style="padding: 0;">
    <div class="ele-cell demo-monitor-tool">
      <div class="ele-cell-content">
        <el-tabs v-model="room_type" class="demo-monitor-tabs">
          <el-tab-pane
            :label="this.$t('awb.analysis.awbTitle')"
            name="listRoom"
          />
        </el-tabs>
      </div>
      <div
        :class="['ele-inline-block', { 'hidden-sm-and-down': styleResponsive }]"
        style="width: 260px; margin-left: 10px"
      >
        <el-date-picker
          unlink-panels
          type="daterange"
          value-format="yyyy-MM-dd"
          class="ele-fluid"
          :start-placeholder="$t('basics.beginTime')"
          :end-placeholder="$t('basics.endTime')"
          v-model="awbSearch.date"
          range-separator="至"
          size="small"
          @input="changeTime"
        />
      </div>
    </div>
    <el-divider />
    <el-row>
      <el-col v-bind="styleResponsive ? { lg: 24, md: 16 } : { span: 24 }">
        <ele-pro-table
          ref="table"
          row-key="id"
          :columns="columns"
          :datasource="datasource"
        >
        </ele-pro-table>
      </el-col>
    </el-row>
  </el-card>
</template>

<script>
  import { getAwbDatList } from '@/api/order/awb';
  export default {
    data() {
      return {
        columns: [
          {
            columnKey: 'index',
            type: 'index',
            width: 45,
            align: 'center',
            showOverflowTooltip: true,
            fixed: 'left'
          },
          {
            prop: 'date',
            label: this.$t('awb.analysis.date'),
            showOverflowTooltip: true,
            minWidth: 110
          },
          {
            prop: 'bol',
            label: this.$t('awb.analysis.bol_count'),
            showOverflowTooltip: true,
            minWidth: 110
          },
          {
            prop: 'box',
            label: this.$t('awb.analysis.box_count'),
            showOverflowTooltip: true,
            minWidth: 110
          },
          {
            prop: 'weight',
            label: this.$t('awb.analysis.weight'),
            showOverflowTooltip: true,
            minWidth: 110
          }
        ],
        // 销售量搜索参数
        awbSearch: {
          limit: 10,
          page: 1,
          date: ''
        },
        room_type: 'listRoom',
        showDate: []
      };
    },
    computed: {
      // 是否开启响应式布局
      styleResponsive() {
        return this.$store.state.theme.styleResponsive;
      }
    },
    created() {
      // this.datasource();
    },
    methods: {
      datasource({ page, limit, where, order }) {
        this.awbSearch.limit = limit;
        this.awbSearch.page = page;
        return getAwbDatList(this.awbSearch);
      },
      changeTime() {
        this.$nextTick(() => {
          this.$refs.table.reload({
            page: 1
          });
        });
      }
    }
  };
</script>
<style lang="scss" scoped>
  /* 销售额、访问量工具栏 */
  .demo-monitor-tool {
    padding: 0 20px;

    .demo-monitor-tabs {
      height: 51px;
    }

    :deep(.el-tabs__item) {
      height: 51px;
      line-height: 51px;
      font-size: 15px;
    }

    :deep(.el-tabs__nav-wrap:after) {
      display: none;
    }
  }

  /* 小标题 */
  .demo-monitor-title {
    padding: 0 20px;
    margin: 15px 0 5px 0;
  }

  /* 排名 item */
  .demo-monitor-rank-item {
    padding: 0 20px;
    line-height: 20px;
    margin-top: 18px;
  }
</style>
