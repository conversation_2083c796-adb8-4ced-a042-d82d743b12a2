<template>
  <el-card shadow="never" body-style="padding: 14px 5px 0 0;">
    <div class="ele-cell demo-monitor-tool">
      <div class="ele-cell-content">
        <el-tabs v-model="room_type" class="demo-monitor-tabs">
          <el-tab-pane :label="room_title" name="indexRoom" />
        </el-tabs>
      </div>
      <div
        :class="['ele-inline-block', { 'hidden-xs-only': styleResponsive }]"
        style="margin-left: 10px; margin-bottom: 10px"
        >{{ this.$t('awb.analysis.time_slot') }}
      </div>
      <div
        :class="['ele-inline-block', { 'hidden-xs-only': styleResponsive }]"
        style="margin-left: 10px; margin-bottom: 10px"
      >
        <el-select
          v-model="awbSearch.slot"
          class="ele-fluid"
          @change="changeSlot"
        >
          <el-option :label="'8' + this.$t('awb.analysis.hour')" :value="8" />
          <el-option :label="'7' + this.$t('awb.analysis.hour')" :value="7" />
          <el-option :label="'6' + this.$t('awb.analysis.hour')" :value="6" />
          <el-option :label="'5' + this.$t('awb.analysis.hour')" :value="5" />
          <el-option :label="'4' + this.$t('awb.analysis.hour')" :value="4" />
          <el-option :label="'3' + this.$t('awb.analysis.hour')" :value="3" />
          <el-option :label="'2' + this.$t('awb.analysis.hour')" :value="2" />
          <el-option :label="'1' + this.$t('awb.analysis.hour')" :value="1" />
        </el-select>
      </div>
      <div
        :class="['ele-inline-block', { 'hidden-xs-only': styleResponsive }]"
        style="margin-left: 10px; margin-bottom: 10px"
      >
        <el-radio-group
          v-model="awbSearch.hours"
          size="small"
          @change="changeHour"
        >
          <el-radio-button :label="48"
            >48{{ $t('awb.analysis.hour') }}</el-radio-button
          >
          <el-radio-button :label="32"
            >32{{ $t('awb.analysis.hour') }}</el-radio-button
          >
          <el-radio-button :label="24"
            >24{{ $t('awb.analysis.hour') }}</el-radio-button
          >
          <el-radio-button :label="12"
            >12{{ $t('awb.analysis.hour') }}</el-radio-button
          >
          <el-radio-button :label="8"
            >8{{ $t('awb.analysis.hour') }}</el-radio-button
          >
          <el-radio-button :label="6"
            >6{{ $t('awb.analysis.hour') }}</el-radio-button
          >
        </el-radio-group>
      </div>
    </div>
    <el-divider style="margin-top: 10px" />
    <el-row>
      <el-col v-bind="styleResponsive ? { lg: 24, md: 16 } : { span: 24 }">
        <v-chart
          ref="visitHourChart"
          style="height: 323px"
          :option="visitHourChartOption"
        />
      </el-col>
    </el-row>
  </el-card>
</template>

<script>
  import { use } from 'echarts/core';
  import { CanvasRenderer } from 'echarts/renderers';
  import { LineChart } from 'echarts/charts';
  import {
    GridComponent,
    TooltipComponent,
    LegendComponent
  } from 'echarts/components';
  import VChart from 'vue-echarts';
  import { getVisitHourList } from '@/api/order/awb';
  import { echartsMixin } from '@/utils/echarts-mixin';

  use([
    CanvasRenderer,
    LineChart,
    GridComponent,
    TooltipComponent,
    LegendComponent
  ]);

  export default {
    components: { VChart },
    mixins: [echartsMixin(['visitHourChart'])],
    data() {
      return {
        awbSearch: {
          slot: 8,
          hours: 48
        },
        room_title:
          this.$t('awb.analysis.lastTimeTitle') +
          '48' +
          this.$t('awb.analysis.hour'),
        room_type: 'indexRoom',
        visitHourChartOption: {}
      };
    },
    created() {
      this.getVisitHourData();
    },
    computed: {
      // 是否开启响应式布局
      styleResponsive() {
        return this.$store.state.theme.styleResponsive;
      }
    },
    methods: {
      changeHour() {
        this.room_title =
          this.$t('awb.analysis.lastTimeTitle') +
          this.awbSearch.hours +
          this.$t('awb.analysis.hour');
        this.getVisitHourData();
      },
      changeSlot() {
        this.getVisitHourData();
      },
      /* 获取最近数据 */
      getVisitHourData() {
        getVisitHourList(this.awbSearch)
          .then((data) => {
            this.visitHourChartOption = {
              tooltip: {
                trigger: 'axis'
              },
              legend: {
                data: [
                  this.$t('awb.analysis.bol_count'),
                  this.$t('awb.analysis.box_count'),
                  this.$t('awb.analysis.weight')
                ],
                right: 20
              },
              xAxis: [
                {
                  type: 'category',
                  boundaryGap: false,
                  data: data.map((d) => d.time)
                }
              ],
              yAxis: [
                {
                  type: 'value'
                }
              ],
              series: [
                {
                  name: this.$t('awb.analysis.bol_count'),
                  type: 'line',
                  smooth: true,
                  symbol: 'none',
                  areaStyle: {
                    opacity: 0.5
                  },
                  data: data.map((d) => d.bol)
                },
                {
                  name: this.$t('awb.analysis.box_count'),
                  type: 'line',
                  smooth: true,
                  symbol: 'none',
                  areaStyle: {
                    opacity: 0.5
                  },
                  data: data.map((d) => d.box)
                },
                {
                  name: this.$t('awb.analysis.weight'),
                  type: 'line',
                  smooth: true,
                  symbol: 'none',
                  areaStyle: {
                    opacity: 0.5
                  },
                  data: data.map((d) => d.weight)
                }
              ]
            };
          })
          .catch((e) => {
            this.$message.error(e.message);
          });
      }
    }
  };
</script>
<style lang="scss" scoped>
  /* 销售额、访问量工具栏 */
  .demo-monitor-tool {
    padding: 0 20px;

    .demo-monitor-tabs {
      height: 51px;
    }

    :deep(.el-tabs__item) {
      height: 51px;
      line-height: 51px;
      font-size: 15px;
    }

    :deep(.el-tabs__nav-wrap:after) {
      display: none;
    }
  }

  /* 小标题 */
  .demo-monitor-title {
    padding: 0 20px;
    margin: 15px 0 5px 0;
  }

  /* 排名 item */
  .demo-monitor-rank-item {
    padding: 0 20px;
    line-height: 20px;
    margin-top: 18px;
  }
</style>
