<template>
  <div class="ele-body ele-body-card">
    <awb-card />
    <awb-list />
  </div>
</template>

<script>
  import awbCard from './components/awb-card';
  import awbList from './components/awb-list';
  export default {
    name: 'DashboardAnalysis',
    components: {
      awbCard,
      awbList
    },
    computed: {
      // 是否开启响应式布局
      styleResponsive() {
        return this.$store.state.theme.styleResponsive;
      }
    }
  };
</script>
