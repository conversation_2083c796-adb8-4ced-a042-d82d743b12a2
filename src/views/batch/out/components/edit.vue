<!-- 编辑弹窗 -->
<template>
  <ele-modal
    width="1080px"
    :visible="visible"
    :close-on-click-modal="false"
    custom-class="ele-dialog-form"
    :title="this.$t('basics.detail')"
    @update:visible="updateVisible"
  >
    <el-form ref="form" :model="form" label-width="120px">
      <el-row :gutter="15">
        <el-col :sm="12">
          <el-form-item :label="this.$t('batch.out.name')+':'" prop="batch_no">
            <el-input
              clearable
              :maxlength="50"
              show-word-limit
              v-model="form.batch_no"
              :disabled="true"
            />
          </el-form-item>
        </el-col>
        <el-col :sm="12">
          <el-form-item :label="this.$t('car.address')+':'" prop="address_code">
            <el-input
              clearable
              :maxlength="50"
              show-word-limit
              v-model="form.address_code"
              :disabled="true"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="15">
        <el-col :sm="12">
          <el-form-item :label="this.$t('basics.status')+':'" prop="status">
            <el-select
              v-model="form.status"
              clearable
              :disabled="true"
              class="ele-fluid"
            >
              <el-option :label="this.$t('batch.out.status.s1')" :value="1" />
              <el-option label="等待发货" :value="2" />
              <el-option :label="this.$t('batch.out.status.s2')" :value="3" />
              <el-option :label="this.$t('batch.out.status.s2')" :value="4" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :sm="12">
          <el-form-item :label="this.$t('basics.createTime')+':'" prop="created_at">
            <el-input
              clearable
              :maxlength="50"
              show-word-limit
              v-model="form.created_at"
              :disabled="true"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="15">
        <el-col :sm="12">
          <el-form-item :label="this.$t('batch.out.packagenum')+':'" prop="parcel_count">
            <el-input
              clearable
              :maxlength="50"
              show-word-limit
              v-model="form.parcel_count"
              :disabled="true"
            />
          </el-form-item>
        </el-col>
        <el-col :sm="12">
          <el-form-item :label="this.$t('batch.out.boxnum')+':'" prop="box_count">
            <el-input
              clearable
              :maxlength="50"
              show-word-limit
              v-model="form.box_count"
              :disabled="true"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <!-- <el-row
        v-for="item in form.appoint"
        :gutter="15">
        <el-col :sm="8">
          <el-form-item label="卡车公司:" prop="company">
            <el-input
              clearable
              :maxlength="50"
              show-word-limit
              v-model="item.company"
              :disabled="true"
            />
          </el-form-item>
        </el-col>
        <el-col :sm="8">
          <el-form-item label="车牌:" prop="number">
            <el-input
              clearable
              :maxlength="50"
              show-word-limit
              v-model="item.number"
              :disabled="true"
            />
          </el-form-item>
        </el-col>
        <el-col :sm="8">
          <el-form-item label="约车时间:" prop="coming_time">
            <el-input
              clearable
              :maxlength="50"
              show-word-limit
              v-model="item.coming_time"
              :disabled="true"
            />
          </el-form-item>
        </el-col>
      </el-row> -->
    </el-form>
    <ele-pro-table
      ref="table"
      :initLoad="false"
      :columns="columns"
      :need-page="false"
      :datasource="datasource"
    >
    </ele-pro-table>
    <div style="width:100%;height:20px;"></div>
  </ele-modal>
</template>

<script>
  import { info } from '@/api/list/batch'

  const DEFAULT_FORM = {
    id: null,
    batch_no:"",
    address_code: "",
    created_at:"",
    box_count:null,
    parcel_count:null,
    status:null,
    appoint:[]
  };

  export default {
    name: 'EditAwb',
    components: { },
    props: {
      // 弹窗是否打开
      visible: Boolean,
      // 修改回显的数据
      data: Object
    },
    data() {
      return {
        // 表单数据
        form: { ...DEFAULT_FORM },
        columns: [
          {
            columnKey: 'index',
            type: 'index',
            width: 45,
            showOverflowTooltip: true,
            fixed: 'left'
          },
          {
            prop: 'box_no',
            label: this.$t('awb.box.box'),
            showOverflowTooltip: true,
            minWidth: 110
          },
          {
            prop: 'parcel_count',
            label: this.$t('batch.out.packagenum'),
            showOverflowTooltip: true,
            minWidth: 110
          },
          {
            prop: 'check_count',
            label: this.$t('batch.out.checknum'),
            showOverflowTooltip: true,
            minWidth: 110
          },
        ],
        datasource:[],
        // 提交状态
        loading: false,
        // 是否是修改
        isUpdate: false
      };
    },
    async created() {

    },
    methods: {
      /* 保存编辑 */
      save() {
        this.$refs['form'].validate((valid) => {
          if (!valid) {
            return false;
          }
          this.loading = true;
          const data = {
            ...this.form
          };
          edit(data)
            .then((msg) => {
              this.loading = false;
              this.$message.success(this.$t('basics.success'));
              this.updateVisible(false);
              this.$emit('done');
            })
            .catch((e) => {
              this.loading = false;
              this.$message.error(e);
            });
        });
      },
      /* 更新visible */
      updateVisible(value) {
        this.$emit('update:visible', value);
      },
      /* 角色选择回调*/
      roleSelectCallback(value) {
        this.form.role_type = value.type;
      }
    },
    watch: {
      async visible(visible) {
        if (visible) {
          let a = await info(this.data)
          // this.form = a.result
          // let data = this.data;
          if (this.data) {
            this.$util.assignObject(this.form, {
              ...a.result.batch,
              appoint:a.result.appoint
            });
            this.datasource = a.result.boxes
            console.log(a.result.batch)
            console.log(this.form)
            this.isUpdate = true;
          } else {
            this.isUpdate = false;
          }
        } else {
          this.$refs['form'].clearValidate();
          this.form = { ...DEFAULT_FORM };
        }
      }
    }
  };
</script>
