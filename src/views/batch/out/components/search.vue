<template>
  <el-card shadow="never" body-style="padding: 22px 22px 7px 22px;">
    <!-- 搜索表单 -->
    <el-form
      label-width="80px"
      class="ele-form-search"
      @keyup.enter.native="search"
      @submit.native.prevent
    >
      <el-row :gutter="15">
        <el-col :lg="8" :md="12">
          <el-form-item :label="`${$t('car.address')}:`" prop="address_code">
            <!-- <el-input
              v-model="where.address_code"
              placeholder="请输入地址代码"
            /> -->
            <address-select v-model="where.address_code" />
          </el-form-item>
        </el-col>
        <el-col :lg="8" :md="12">
          <el-form-item :label="`${$t('batch.out.name')}:`">
            <el-input clearable show-word-limit v-model="where.batch_no" />
          </el-form-item>
        </el-col>
        <el-col :lg="8" :md="12">
          <el-form-item :label="`${$t('awb.noa.awbno')}:`">
            <el-input clearable show-word-limit v-model="where.bol_no" />
          </el-form-item>
        </el-col>
        <el-col :lg="8" :md="12">
          <el-form-item :label="`${$t('basics.status')}:`" prop="status">
            <el-select v-model="where.status" clearable class="ele-fluid">
              <el-option :label="this.$t('batch.out.status.s1')" :value="1" />
              <!-- <el-option label="等待发货" :value="2" /> -->
              <el-option :label="this.$t('batch.out.status.s2')" :value="3" />
              <!-- <el-option label="已完成" :value="4" /> -->
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :lg="8" :md="12">
          <el-form-item :label="`${$t('batch.car.user_name')}:`" prop="bol_no">
            <user-select v-model="where.create_user_id" />
          </el-form-item>
        </el-col>

        <el-col :lg="8" :md="12">
          <el-form-item :label="`${$t('batch.car.out_time')}:`" prop="bol_no">
            <el-date-picker
              v-model="where.out_time"
              type="datetimerange"
              value-format="yyyy-MM-dd HH:mm:ss"
              :range-separator="$t('basics.to')"
              class="ele-fluid"
              :start-placeholder="$t('basics.beginTime')"
              :end-placeholder="$t('basics.endTime')"
            />
          </el-form-item>
        </el-col>

        <el-col :lg="8" :md="12">
          <el-form-item :label="`${$t('batch.car.out_name')}:`" prop="bol_no">
            <user-select v-model="where.out_user_id" />
          </el-form-item>
        </el-col>
        <el-col :lg="8" :md="12" v-permission="'awb:nouserSearch'">
          <el-form-item
            :label="$t('storages.storage_name') + ':'"
            prop="storage_name"
          >
            <el-input
              v-model="where.storage_name"
              :placeholder="this.$t('basics.pleaseInput')"
            />
          </el-form-item>
        </el-col>
        <el-col :lg="8" :md="12">
          <div class="ele-form-actions">
            <el-button
              type="primary"
              icon="el-icon-search"
              class="ele-btn-icon"
              @click="search"
            >
              {{ $t('basics.query') }}
            </el-button>
            <el-button @click="reset">{{ $t('basics.reset') }}</el-button>
          </div>
        </el-col>
      </el-row>
    </el-form>
  </el-card>
</template>

<script>
  import AddressSelect from '@/components/AddressSelect';
  import UserSelect from './user-select';
  // 默认表单数据

  const DEFAULT_WHERE = {
    address_code: '',
    batch_no: '',
    create_user_id: '',
    bol_no: '',
    status: null,
    storage_name: '',
    out_time: []
  };

  export default {
    name: 'outbound-fba-search',
    components: { AddressSelect, UserSelect },
    data() {
      return {
        // 表单数据
        where: { ...DEFAULT_WHERE },
        // 搜索表单是否展开
        searchExpand: false,
        goodsCategoryList: [],
        category_id: ''
      };
    },
    methods: {
      /* 搜索 */
      search() {
        //解决搜索 也搜索下级的数据
        this.$emit('search', this.where);
      },
      /* 重置搜索 */
      reset() {
        this.where = { ...DEFAULT_WHERE };
        this.search();
      },
      /* 搜索展开/收起 */
      toggleExpand() {
        this.searchExpand = !this.searchExpand;
        this.$emit('expand-change', this.searchExpand);
      },
      /* 搜索展开/收起 */
      goodsCategoryFilterListClick(e) {
        this.goodsCategoryList = e.data;
      }
    }
  };
</script>
