<template>
  <div class="ele-body">
    <el-card shadow="never">
      <!-- 搜索表单 -->
      <outbound-fba-search @search="reload" />
      <!-- 数据表格 -->
      <ele-pro-table
        ref="table"
        :initLoad="false"
        :columns="columns"
        :datasource="datasource"
        :selection.sync="selection"
        @expand-change="expandChange"
      >
        <template slot="action" slot-scope="{ row }">
          <el-link
            @click="getInfo(row)"
            type="primary"
            icon="el-icon-s-operation"
            :underline="false"
          >
            {{ $t('basics.detail') }}
          </el-link>
          <!-- <el-link
            v-show="row.status == '1'"
            @click="getcar(row)"
            type="primary"
            icon="el-icon-edit"
            :underline="false">
            约车
          </el-link> -->
          <!-- <el-popconfirm
            v-show="row.status === '1'"
            class="ele-action"
            title="确定要删除该车单？"
            @confirm="remove(row)"
          >
            <el-link type="danger" slot="reference" :underline="false">
              取消
            </el-link>
          </el-popconfirm> -->
        </template>
      </ele-pro-table>
    </el-card>
    <show-car :visible.sync="showCar" :data="current" @done="reload" />
    <show-info :visible.sync="showInfo" :data="current" @done="reload" />
  </div>
</template>

<script>
  import { list } from '@/api/list/batch';
  import OutboundFbaSearch from './components/search';
  import ShowCar from './components/car';
  import ShowInfo from './components/edit';

  export default {
    name: 'SystemUser',
    components: {
      OutboundFbaSearch,
      ShowCar,
      ShowInfo
    },
    data() {
      return {
        // 表格列配置
        columns: [
          {
            columnKey: 'index',
            type: 'index',
            width: 45,
            showOverflowTooltip: true,
            fixed: 'left'
          },
          {
            prop: 'batch_no',
            label: this.$t('batch.out.name'),
            showOverflowTooltip: true,
            minWidth: 200
          },
          {
            prop: 'bol_no',
            label: this.$t('awb.noa.awbno'),
            showOverflowTooltip: true,
            minWidth: 200
          },
          {
            prop: 'address',
            label: this.$t('car.address'),
            showOverflowTooltip: true,
            minWidth: 200
          },
          {
            prop: 'box_count',
            label: this.$t('batch.out.boxnum'),
            showOverflowTooltip: true,
            minWidth: 110
          },
          {
            prop: 'parcel_count',
            label: this.$t('batch.out.packagenum'),
            showOverflowTooltip: true,
            minWidth: 80
          },
          {
            prop: 'storage_name',
            label: this.$t('storages.storage_name'),
            showOverflowTooltip: true,
            minWidth: 120
          },
          {
            prop: 'create_user_name',
            label: this.$t('batch.car.user_name'),
            showOverflowTooltip: true,
            minWidth: 80
          },
          {
            prop: 'out_user_name',
            label: this.$t('batch.car.out_name'),
            showOverflowTooltip: true,
            minWidth: 80
          },
          {
            prop: 'status',
            label: this.$t('basics.status'),
            showOverflowTooltip: true,
            minWidth: 110,
            formatter: (row, column, cellValue) => {
              return [
                this.$t('batch.out.status.s1'),
                '等待发货',
                this.$t('batch.out.status.s2'),
                this.$t('batch.out.status.s2')
              ][cellValue - 1];
            }
          },
          {
            prop: 'created_at',
            label: this.$t('basics.createTime'),
            showOverflowTooltip: true,
            minWidth: 110
          },
          {
            prop: 'out_time',
            label: this.$t('batch.car.out_time'),
            showOverflowTooltip: true,
            minWidth: 110
          },
          {
            columnKey: 'action',
            label: this.$t('basics.action'),
            width: 100,
            align: 'center',
            resizable: false,
            slot: 'action',
            hideInSetting: true,
            fixed: 'right'
          }
        ],
        // 表格选中数据
        selection: [],
        // 当前编辑数据
        current: null,
        // 是否显示编辑弹窗
        showCar: false,
        showInfo: false,
        // 是否显示导入弹窗
        showImport: false,
        // 是否显修改密码
        userId: null,
        showChangePassword: false,
        timeWord: {
          time1: '',
          time2: '',
          time3: '',
          time4: ''
        }
      };
    },
    created() {
      //或者登陆不显示货主名字其他登录显示名称
      if (
        this.$store.state.user.info.role_type === '2' ||
        this.$store.state.user.info.role_type === '3'
      ) {
        const index = this.columns.findIndex(function (key) {
          return key.prop === 'username';
        });
        this.columns.splice(index, index + 1);
      }
    },
    activated() {
      this.reload();
    },
    methods: {
      /* 表格数据源 */
      datasource({ page, limit, where, order }) {
        return list({
          ...where,
          ...order,
          page: page,
          num: limit
          // inbound_id: this.$route.query.inbound_id
        });
      },
      /* 刷新表格 */
      reload(where) {
        this.$refs.table.reload({ page: 1, where: where });
      },
      /* 打开编辑弹窗 */
      getcar(row) {
        this.current = row;
        this.showCar = true;
      },
      getInfo(row) {
        this.current = row;
        this.showInfo = true;
      },
      /* 打开导入弹窗 */
      openImport() {
        this.showImport = true;
      },
      /* 删除 */
      cancel(row) {
        const loading = this.$loading({ lock: true });
        cancel(row.id)
          .then((msg) => {
            loading.close();
            this.$message.success(this.$t('basics.success'));
            this.reload();
          })
          .catch((e) => {
            loading.close();
            this.$message.error(e);
          });
      },
      expandChange(row, expandedRows) {
        console.log(row);
        switch (row.type) {
          case 1: //ru
            row.timeWord = {
              time1: '到达货站时间',
              time2: '离开货站时间',
              time3: '到达仓库时间',
              time4: '离开仓库时间'
            };
            break;
          case 2: //chu
            row.timeWord = {
              time1: '到达仓库时间',
              time2: '离开仓库时间',
              time3: '到达目的地时间',
              time4: '离开目的地时间'
            };
            break;
          case 3: //huan
            row.timeWord = {
              time1: '到达仓库时间',
              time2: '离开仓库时间',
              time3: '到达货站时间',
              time4: '离开货站时间'
            };
            break;

          default:
            break;
        }
      }
    }
  };
</script>
<style>
  .ele-form-detail {
    font-size: 0;
  }
  .ele-form-detail label {
    width: 90px;
    color: #99a9bf;
  }
  .ele-form-detail .el-form-item {
    margin-right: 0;
    margin-bottom: 0;
    margin-left: 50px;
    /* width: 50%; */
  }
</style>
