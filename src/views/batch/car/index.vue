<template>
  <div class="ele-body">
    <el-card shadow="never">
      <!-- 搜索表单 -->
      <outbound-fba-search @search="reload" />
      <!-- 数据表格 -->
      <ele-pro-table
        ref="table"
        :initLoad="false"
        :columns="columns"
        :datasource="datasource"
        :selection.sync="selection"
        @expand-change="expandChange"
      >
        <template slot="toolbar">
          <el-button
            size="small"
            type="primary"
            icon="el-icon-plus"
            class="ele-btn-icon"
            @click="getCreate()"
          >
            {{ $t('batch.car.create1') }}
          </el-button>
          <el-button
            size="small"
            type="primary"
            icon="el-icon-plus"
            class="ele-btn-icon"
            @click="getCreate1()"
          >
            {{ $t('batch.car.create2') }}
          </el-button>
        </template>
        <template slot="action" slot-scope="{ row }">
          <el-link
            @click="getDetal(row)"
            type="primary"
            icon="el-icon-s-operation"
            :underline="false"
          >
            {{ $t('basics.detail') }}
          </el-link>
          <el-link
            @click="getInfo(row)"
            type="primary"
            icon="el-icon-s-operation"
            :underline="false"
          >
            {{ $t('basics.edit') }}
          </el-link>
          <el-link
            v-show="row.status == '1'"
            @click="getStatus(row)"
            type="primary"
            :underline="false"
          >
            {{ $t('batch.car.btn1') }}
          </el-link>
          <el-popconfirm
            class="ele-action"
            :title="$t('basics.confirmDel')"
            @confirm="cancel(row)"
          >
            <el-link type="danger" slot="reference" :underline="false">
              {{ $t('basics.delete') }}
            </el-link>
          </el-popconfirm>
        </template>
      </ele-pro-table>
    </el-card>
    <show-car :visible.sync="showCar" :data="current" @done="reload" />
    <show-info :visible.sync="showInfo" :data="current" @done="reload" />
    <show-info1 :visible.sync="showInfo1" :data="current" @done="reload" />
    <show-detail :visible.sync="showDetail" :data="current" @done="reload" />
    <show-success :visible.sync="showSuccess" :data="current" @done="reload" />
    <show-create :visible.sync="showCreate" :data="current" @done="reload" />
    <show-create1 :visible.sync="showCreate1" :data="current" @done="reload" />
  </div>
</template>

<script>
  import { list, status, cancel } from '@/api/list/planning';
  import OutboundFbaSearch from './components/search';
  import ShowCar from './components/car';
  import ShowInfo from './components/edit';
  import ShowDetail from './components/detail';
  import ShowInfo1 from './components/edit1';
  import ShowSuccess from './components/successTime';
  import ShowCreate from './components/create';
  import ShowCreate1 from './components/create1';

  export default {
    name: 'SystemUser',
    components: {
      OutboundFbaSearch,
      ShowCar,
      ShowInfo,
      ShowDetail,
      ShowSuccess,
      ShowCreate,
      ShowCreate1,
      ShowInfo1
    },
    data() {
      return {
        // 表格列配置
        columns: [
          {
            columnKey: 'index',
            type: 'index',
            width: 45,
            showOverflowTooltip: true,
            fixed: 'left'
          },
          {
            prop: 'address',
            label: this.$t('basicsMenu.address.name'),
            showOverflowTooltip: true,
            minWidth: 200
          },
          {
            prop: 'transporter',
            label: this.$t('car.transfers'),
            showOverflowTooltip: true,
            minWidth: 110
          },
          {
            prop: 'plate_number',
            label: this.$t('batch.car.plate_number'),
            showOverflowTooltip: true,
            minWidth: 110
          },
          {
            prop: 'reference',
            label: this.$t('batch.car.reference'),
            showOverflowTooltip: true,
            minWidth: 110
          },
          {
            prop: 'status',
            label: this.$t('basics.status'),
            showOverflowTooltip: true,
            minWidth: 110,
            formatter: (row, column, cellValue) => {
              return [
                this.$t('batch.car.status.s1'),
                this.$t('batch.car.status.s2')
              ][cellValue - 1];
            }
          },
          {
            prop: 'status_remark',
            label: this.$t('batch.car.status_remark'),
            showOverflowTooltip: true,
            minWidth: 110
          },
          {
            prop: 'remark',
            label: this.$t('batch.car.remark'),
            showOverflowTooltip: true,
            minWidth: 110
          },
          // {
          //   prop: 'finance_confirm',
          //   label: '财务',
          //   showOverflowTooltip: true,
          //   minWidth: 110,
          //   formatter: (row, column, cellValue) => {
          //     return ['未确认', '已确认'][cellValue-1];
          //   }
          // },
          {
            prop: 'loading_date',
            label: this.$t('batch.car.loadTime'),
            showOverflowTooltip: true,
            minWidth: 125
          },
          {
            prop: 'created_at',
            label: this.$t('basics.createTime'),
            showOverflowTooltip: true,
            minWidth: 125
          },
          {
            prop: 'user_name',
            label: this.$t('batch.car.user_name'),
            showOverflowTooltip: true,
            minWidth: 100
          },
          {
            columnKey: 'action',
            label: this.$t('basics.action'),
            width: 270,
            align: 'center',
            resizable: false,
            slot: 'action',
            hideInSetting: true,
            fixed: 'right'
          }
        ],
        // 表格选中数据
        selection: [],
        // 当前编辑数据
        current: null,
        // 是否显示编辑弹窗
        showCar: false,
        showInfo: false,
        showDetail: false,
        showSuccess: false,
        showCreate: false,
        showCreate1: false,
        showInfo1: false,
        // 是否显示导入弹窗
        showImport: false,
        // 是否显修改密码
        userId: null,
        showChangePassword: false,
        timeWord: {
          time1: '',
          time2: '',
          time3: '',
          time4: ''
        }
      };
    },
    created() {
      //或者登陆不显示货主名字其他登录显示名称
      if (
        this.$store.state.user.info.role_type === '2' ||
        this.$store.state.user.info.role_type === '3'
      ) {
        const index = this.columns.findIndex(function (key) {
          return key.prop === 'username';
        });
        this.columns.splice(index, index + 1);
      }
    },
    activated() {
      this.reload();
    },
    methods: {
      /* 表格数据源 */
      datasource({ page, limit, where, order }) {
        return list({
          ...where,
          ...order,
          page: page,
          num: limit
          // inbound_id: this.$route.query.inbound_id
        });
      },
      /* 刷新表格 */
      reload(where) {
        this.$refs.table.reload({ page: 1, where: where });
      },
      /* 打开编辑弹窗 */
      getcar(row) {
        this.current = row;
        this.showCar = true;
      },
      getInfo(row) {
        this.current = row;
        if (row.type == 1) {
          this.showInfo = true;
        } else {
          this.showInfo1 = true;
        }
      },
      getDetal(row) {
        this.current = row;
        this.showDetail = true;
      },
      getCreate() {
        // this.current = row;
        this.showCreate = true;
      },
      getCreate1() {
        // this.current = row;
        this.showCreate1 = true;
      },
      /* 打开导入弹窗 */
      openImport() {
        this.showImport = true;
      },
      /* 删除 */
      cancel(row) {
        const loading = this.$loading({ lock: true });
        cancel(row)
          .then((msg) => {
            loading.close();
            this.$message.success(this.$t('basics.success'));
            this.reload();
          })
          .catch((e) => {
            loading.close();
            this.$message.error(e);
          });
      },
      getStatus(row) {
        this.current = row;
        this.showSuccess = true;
      },
      expandChange(row, expandedRows) {
        console.log(row);
        switch (row.type) {
          case 1: //ru
            row.timeWord = {
              time1: '到达货站时间',
              time2: '离开货站时间',
              time3: '到达仓库时间',
              time4: '离开仓库时间'
            };
            break;
          case 2: //chu
            row.timeWord = {
              time1: '到达仓库时间',
              time2: '离开仓库时间',
              time3: '到达目的地时间',
              time4: '离开目的地时间'
            };
            break;
          case 3: //huan
            row.timeWord = {
              time1: '到达仓库时间',
              time2: '离开仓库时间',
              time3: '到达货站时间',
              time4: '离开货站时间'
            };
            break;

          default:
            break;
        }
      }
    }
  };
</script>
<style>
  .ele-form-detail {
    font-size: 0;
  }
  .ele-form-detail label {
    width: 90px;
    color: #99a9bf;
  }
  .ele-form-detail .el-form-item {
    margin-right: 0;
    margin-bottom: 0;
    margin-left: 50px;
    /* width: 50%; */
  }
</style>
