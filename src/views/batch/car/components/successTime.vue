<!-- 编辑弹窗 -->
<template>
  <ele-modal
    width="720px"
    :visible="visible"
    :close-on-click-modal="false"
    custom-class="ele-dialog-form"
    :title="this.$t('batch.car.btn1')"
    @update:visible="updateVisible"
  >
    <el-form ref="form" :model="form" :rules="rules" label-width="120px">
      <el-row type="flex">
        <el-col :lg="16" :span="12">
          <el-form-item :label="this.$t('batch.car.status_remark')+':'" prop="remark">
            <el-input
                clearable
                :maxlength="50"
                show-word-limit
                v-model="form.remark"
              />
          </el-form-item>
        </el-col>
      </el-row>
      <!-- <el-row type="flex">
        <el-col :span="12">
          <el-form-item label="时间:" prop="date">
            <el-date-picker
              v-model="form.date"
              placeholder="请选择时间"
              type="datetime"
              value-format="yyyy-MM-dd HH:mm:ss"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
      </el-row> -->
    </el-form>
    <div slot="footer">
      <el-button @click="updateVisible(false)">
        {{ $t('basics.cancel') }}
      </el-button>
      <el-button type="primary" :loading="loading" @click="save">
        {{ $t('basics.save') }}
      </el-button>
    </div>
  </ele-modal>
</template>

<script>
  import { status } from '@/api/list/planning';

  const DEFAULT_FORM = {
    id: null,
    remark: '',
    type:null,
  };

  export default {
    name: 'UserEdit',
    components: {},
    props: {
      visible: Boolean,
      data: Object
    },
    data() {
      return {
        // 表单数据
        form: { ...DEFAULT_FORM },
        // 表单验证规则
        rules: {
          date: [
            {
              required: true,
              message: this.$t('basics.pleaseChoose'),
              trigger: ['blur','change']
            }
          ],
        },
        // 提交状态
        loading: false,
        // 是否是修改
        isUpdate: false
      };
    },
    methods: {
      /* 保存编辑 */
      save() {
        this.$refs['form'].validate((valid) => {
          if (!valid) {
            return false;
          }
          this.loading = true;
          const data = {
            id:this.data.id,
            remark:this.form.remark,
          }
          status(data)
            .then((msg) => {
              this.loading = false;
              this.$message.success(this.$t('basics.success'));
              this.updateVisible(false);
              this.$emit('done');
            })
            .catch((e) => {
              this.loading = false;
              this.$message.error(e);
            });
        });
      },
      /* 更新visible */
      updateVisible(value) {
        this.$emit('update:visible', value);
      },
      /* 角色选择回调*/
      roleSelectCallback(value) {
        this.form.role_type = value.type;
      }
    },
    watch: {
      async visible(visible) {
        if (visible) {
          if (this.data) {
            this.isUpdate = true;
          } else {
            this.isUpdate = false;
          }
        } else {
          this.$refs['form'].clearValidate();
          this.form = { ...DEFAULT_FORM };
        }
      }
    }
  };
</script>
