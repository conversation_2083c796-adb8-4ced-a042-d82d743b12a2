<!-- 编辑弹窗 -->
<template>
  <ele-modal
    width="1080px"
    :visible="visible"
    :close-on-click-modal="false"
    custom-class="ele-dialog-form"
    :title="this.$t('basics.edit')"
    @update:visible="updateVisible"
  >
    <el-form ref="form" :model="form" label-width="150px">
      <el-row :gutter="15">
        <el-col :sm="12">
          <el-form-item :label="this.$t('basicsMenu.address.name')+':'" prop="address_code">
            <address-select
              v-model="form.address_code"
            />
          </el-form-item>
        </el-col>
        <el-col :sm="12">
          <el-form-item :label="this.$t('car.transfers')+':'" prop="transporter">
            <transfers-select
              v-model="form.transporter"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="15">
        <el-col :sm="12">
          <el-form-item :label="this.$t('batch.car.plate_number')+':'" prop="plate_number">
            <el-input
              clearable
              :maxlength="50"
              show-word-limit
              v-model="form.plate_number"
            />
          </el-form-item>
        </el-col>
        <el-col :sm="12">
          <el-form-item :label="this.$t('batch.car.reference')+':'" prop="reference">
            <el-input
              clearable
              :maxlength="50"
              show-word-limit
              v-model="form.reference"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="15">
        <el-col :sm="12">
          <el-form-item :label="this.$t('batch.car.estimated_loading_time')+':'" prop="estimated_loading_time">
            <el-date-picker
                v-model="form.estimated_loading_time"
                type="datetime"
                :placeholder="this.$t('basics.placeholder.time')"
                value-format="yyyy-MM-dd HH:mm:ss"
                style="width: 100%"
              />
          </el-form-item>
        </el-col>
        <el-col :sm="12">
          <el-form-item :label="this.$t('batch.car.loadTime')+':'" prop="loading_date">
            <el-date-picker
                v-model="form.loading_date"
                type="datetime"
                :placeholder="this.$t('basics.placeholder.time')"
                value-format="yyyy-MM-dd HH:mm:ss"
                style="width: 100%"
              />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="15">
        <el-col :sm="12">
          <el-form-item :label="this.$t('basics.status')+':'" prop="status">
            <el-select
              v-model="form.status"
              :disabled="true"
              clearable
              class="ele-fluid"
            >
              <el-option :label="this.$t('batch.car.status.s1')" :value="1" />
              <el-option :label="this.$t('batch.car.status.s2')" :value="2" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :sm="12" v-show="form.status == 2">
          <el-form-item :label="this.$t('batch.car.status_remark')+':'" prop="status_remark">
            <el-input
              clearable
              show-word-limit
              :disabled="true"
              v-model="form.status_remark"
            />
          </el-form-item>
        </el-col>
        <el-col :sm="12">
          <el-form-item :label="this.$t('batch.car.remark')+':'" prop="remark">
            <el-input
              clearable
              show-word-limit
              v-model="form.remark"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <!-- <el-row
        v-for="item in form.appoint"
        :gutter="15">
        <el-col :sm="8">
          <el-form-item label="卡车公司:" prop="company">
            <el-input
              clearable
              :maxlength="50"
              show-word-limit
              v-model="item.company"
              :disabled="true"
            />
          </el-form-item>
        </el-col>
        <el-col :sm="8">
          <el-form-item label="车牌:" prop="number">
            <el-input
              clearable
              :maxlength="50"
              show-word-limit
              v-model="item.number"
              :disabled="true"
            />
          </el-form-item>
        </el-col>
        <el-col :sm="8">
          <el-form-item label="约车时间:" prop="coming_time">
            <el-input
              clearable
              :maxlength="50"
              show-word-limit
              v-model="item.coming_time"
              :disabled="true"
            />
          </el-form-item>
        </el-col>
      </el-row> -->
    </el-form>
    <!-- <ele-pro-table
      ref="table"
      :columns="columns"
      :need-page="false"
      :datasource="datasource"
    >
    </ele-pro-table> -->
    <div slot="footer">
      <!-- <el-button @click="add()">
        增加提单信息
      </el-button> -->
      <!-- <el-button @click="updateVisible(false)">
        {{ $t('basics.cancel') }}
      </el-button> -->
      <el-button type="primary" :loading="loading" @click="save">
        {{ $t('basics.save') }}
      </el-button>
    </div>
  </ele-modal>
</template>

<script>
  import { edit } from '@/api/list/planning'
  import AddressSelect from '@/components/AddressSelect';
  import TransfersSelect from '@/components/TransfersSelect';
  import AwbSelect from '@/components/AwbSelect';
  const DEFAULT_FORM = {
    id: null,
    address_code:"",
    plate_number:"",
    transporter:"",
    reference: "",
    status_remark:'',
    remark:'',
    estimated_loading_time:"",
    loading_date:"",
    status:null,
    bols:[]
  };

  export default {
    name: 'EditAwb',
    components: { AddressSelect,TransfersSelect,AwbSelect },
    props: {
      // 弹窗是否打开
      visible: Boolean,
      // 修改回显的数据
      data: Object
    },
    data() {
      return {
        // 表单数据
        form: { ...DEFAULT_FORM },
        columns: [
          {
            prop:'bol_no',
            label: this.$t('awb.noa.awbno'),
            minWidth: 220,
            align: 'center',
            resizable: false,
          },
          {
            prop: 'weight',
            label: '库存',
            showOverflowTooltip: true,
            minWidth: 110
          },
          // {
          //   columnKey: 'remark',
          //   slot: 'remark',
          //   prop: 'remark',
          //   label: '备注',
          //   showOverflowTooltip: true,
          //   minWidth: 110
          // },
          // {
          //   columnKey: 'action',
          //   label: '操作',
          //   width: 220,
          //   align: 'center',
          //   resizable: false,
          //   slot: 'action',
          //   hideInSetting: true
          // }
        ],
        datasource:[],
        // 提交状态
        loading: false,
        // 是否是修改
        isUpdate: false
      };
    },
    async created() {

    },
    methods: {
      /* 保存编辑 */
      save() {
        this.$refs['form'].validate((valid) => {
          if (!valid) {
            return false;
          }
          this.loading = true;
          const data = {
            ...this.form,
            bol:this.datasource,
            type:1
          };
          edit(data)
            .then((msg) => {
              this.loading = false;
              this.$message.success(this.$t('basics.success'));
              this.updateVisible(false);
              this.$emit('done');
            })
            .catch((e) => {
              this.loading = false;
              this.$message.error(e);
            });
        });
      },
      add(){
        let lastArr = this.datasource.slice(-1)
        if(lastArr.length>0){
          if(lastArr[0].id == ''){
            this.$message.success('请填写完整提单信息')
            return
          }
        }
        this.datasource.push({
          id:'',
        })
      },
      deleteAwb(row,index){
        let that = this
        console.log(index)
        that.datasource.splice(index, 1);
      },
      /* 更新visible */
      updateVisible(value) {
        this.$emit('update:visible', value);
      },
      /* 角色选择回调*/
      roleSelectCallback(value) {
        this.form.role_type = value.type;
      }
    },
    watch: {
      async visible(visible) {
        if (visible) {
          if (this.data) {
            this.$util.assignObject(this.form, {
              ...this.data
            });
            console.log(this.form)
            this.datasource = this.form.bols
            this.isUpdate = true;
          } else {
            this.isUpdate = false;
          }
        } else {
          this.$refs['form'].clearValidate();
          this.form = { ...DEFAULT_FORM };
        }
      }
    }
  };
</script>
