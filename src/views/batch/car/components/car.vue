<!-- 编辑弹窗 -->
<template>
  <ele-modal
    width="720px"
    :visible="visible"
    :close-on-click-modal="false"
    custom-class="ele-dialog-form"
    title="约车"
    @update:visible="updateVisible"
  >
    <el-form ref="form" :model="form" :rules="rules" label-width="92px">
      <el-row
        v-for="(item,index) in form.reqData"
        :key="index"
        :gutter="15">
        <el-col :sm="12">
          <el-form-item label="卡车公司:" :prop="'reqData.'+ index +'.company'" :rules="rules.company">
            <el-input
              clearable
              :maxlength="50"
              show-word-limit
              v-model="item.company"
              placeholder="请输入卡车公司"
            />
          </el-form-item>
        </el-col>
        <el-col :sm="12">
          <el-form-item label="车牌:" :prop="'reqData.'+ index +'.number'" :rules="rules.number">
            <el-input
              clearable
              :maxlength="50"
              show-word-limit
              v-model="item.number"
              placeholder="请输入车牌"
            />
          </el-form-item>
        </el-col>
        <el-col :sm="12">
          <el-form-item label="约车时间:" :prop="'reqData.'+ index +'.coming_time'" :rules="rules.coming_time">
            <el-date-picker
              v-model="item.coming_time"
              type="datetime"
              value-format="yyyy-MM-dd HH:mm:ss"
              placeholder="请选择时间"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-button @click="removeNoa(item,index)">
           {{ $t('basics.delete') }}
        </el-button>
      </el-row>
    </el-form>
    <div slot="footer">
      <el-button @click="addData()">
        增加约车信息
      </el-button>
      <el-button type="primary" :loading="loading" @click="save">
        {{ $t('basics.save') }}
      </el-button>
    </div>
  </ele-modal>
</template>

<script>
  import { appoint } from '@/api/list/batch'

  const DEFAULT_FORM = {
    id: null,
    reqData:[{
      company: "",
      number: "",
      coming_time: ""
    }]
  };

  export default {
    name: 'ShowCar',
    components: { },
    props: {
      // 弹窗是否打开
      visible: Boolean,
      // 修改回显的数据
      data: Object
    },
    data() {
      return {
        // 表单数据
        form: { ...DEFAULT_FORM },
        reqData:[{
          company: "",
          number: "",
          coming_time: ""
        }],
        // 表单验证规则
        rules: {
          company: [
            {
              required: true,
              message: this.$t('basics.pleaseInput'),
              trigger: 'blur'
            }
          ],
          number: [
            {
              required: true,
              message: this.$t('basics.pleaseInput'),
              trigger: 'blur'
            }
          ],
          coming_time: [
            {
              required: true,
              message: this.$t('basics.pleaseInput'),
              trigger: 'blur'
            }
          ],
        },
        // 提交状态
        loading: false,
        // 是否是修改
        isUpdate: false
      };
    },
    methods: {
      /* 保存编辑 */
      save() {
        this.$refs['form'].validate((valid) => {
          if (!valid) {
            return false;
          }

          // const data = {
          //   ...this.form
          // };
          // console.log(this.form.reqData)
          let lastArr = this.form.reqData.slice(-1)
          if(lastArr[0].company == '' || lastArr[0].number == '' ||lastArr[0].coming_time == ''){
            this.$message.success('请填写完整信息')
            return
          }
          this.loading = true;
          appoint(this.form.id,{data:this.form.reqData})
            .then((msg) => {
              this.loading = false;
              this.$message.success(this.$t('basics.success'));
              this.updateVisible(false);
              this.$emit('done');
            })
            .catch((e) => {
              this.loading = false;
              this.$message.error(e);
            });
        });
      },
      addData(){
        let lastArr = this.form.reqData.slice(-1)
        console.log(lastArr)
        if(lastArr[0].company == '' || lastArr[0].number == '' ||lastArr[0].coming_time == ''){
          this.$message.success('请填写完上一条在添加')
          return
        }
        this.form.reqData.push({
          company: "",
          number: "",
          coming_time: ""
        })
      },
      removeNoa(item,index){
        this.form.reqData.splice(index,1)
      },
      /* 更新visible */
      updateVisible(value) {
        this.$emit('update:visible', value);
      },
      /* 角色选择回调*/
      roleSelectCallback(value) {
        this.form.role_type = value.type;
      }
    },
    watch: {
      async visible(visible) {
        if (visible) {
          let data = this.data;
          if (this.data) {
            this.$util.assignObject(this.form, {
              ...data
            });
            this.form.reqData = [{
              company: "",
              number: "",
              coming_time: ""
            }]
            this.isUpdate = true;
          } else {
            this.isUpdate = false;
          }
        } else {
          this.form = { ...DEFAULT_FORM };
        }
      }
    }
  };
</script>
