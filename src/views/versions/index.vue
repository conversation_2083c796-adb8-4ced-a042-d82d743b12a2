<template>
  <div>
    <div class="ele-page-header">
      <div class="ele-page-title">版本更新</div>
      <!-- <div class="ele-page-desc">
        表单页用于向用户收集或验证信息, 基础表单常见于数据项较少的表单场景。
      </div> -->
    </div>
    <div class="ele-body">
      <el-card shadow="never">
        <el-form
          ref="form"
          :model="form"
          :rules="rules"
          label-width="90px"
          style="max-width: 700px; margin: 10px auto"
        >
          <el-form-item label="版本号:" prop="version">
            <el-input
              v-model="form.version"
              placeholder="请输入版本号"
              clearable
            />
          </el-form-item>
          <el-form-item label="描述:" prop="msg">
            <el-input
              v-model="form.msg"
              placeholder="请输入描述"
              :rows="4"
              type="textarea"
            />
          </el-form-item>
          <el-form-item label="apk安装包:" prop="version">
            <el-upload
              action=""
              accept=".apk"
              :show-file-list="false"
              :before-upload="beforeUpload"
            >
              <el-input v-model="form.pkg" placeholder="请选择apk" disabled>
                <template slot="append">
                  <el-button slot="trigger" size="small" type="primary"
                    >选取文件</el-button
                  >
                </template>
              </el-input>
            </el-upload>
          </el-form-item>

          <el-form-item label="wgt安装包:" prop="version">
            <el-upload
              action=""
              accept=".wgt"
              :show-file-list="false"
              :before-upload="beforeUpload"
            >
              <el-input v-model="form.wgt" placeholder="请选择wgt" disabled>
                <template slot="append">
                  <el-button slot="trigger" size="small" type="primary"
                    >选取文件</el-button
                  >
                </template>
              </el-input>
            </el-upload>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" :loading="loading" @click="submit">
              提交
            </el-button>
          </el-form-item>
        </el-form>
      </el-card>
    </div>
  </div>
</template>

<script>
  import { getVersions, setVersions } from '@/api/views';
  import { uploadFile } from '@/api/system/file';
  const form = {
    version: null,
    msg: null,
    pkg: null,
    wgt: null
  };
  export default {
    name: 'FormBasic',
    data() {
      return {
        // 提交状态
        loading: false,
        // 表单数据
        form: { ...form },
        // 表单验证规则
        rules: {
          version: [
            {
              required: true,
              message: this.$t('basics.pleaseInput'),
              trigger: 'blur'
            }
          ],
          msg: [
            {
              required: true,
              message: this.$t('basics.pleaseInput'),
              trigger: 'blur'
            }
          ],
          pkg: [
            {
              required: true,
              message: this.$t('basics.pleaseInput'),
              trigger: 'blur'
            }
          ],
          wgt: [
            {
              required: true,
              message: this.$t('basics.pleaseInput'),
              trigger: 'blur'
            }
          ]
        }
      };
    },
    async mounted() {
      this.form = await getVersions();
    },
    methods: {
      /* 提交 */
      submit() {
        this.$refs['form'].validate((valid) => {
          if (valid) {
            this.loading = true;
            setVersions({
              version: this.form.version,
              msg: this.form.msg,
              pkg: this.form.pkg,
              wgt: this.form.wgt
            }).then(() => {
              this.loading = false;
              this.$message.success(this.$t('basics.success'));
            });
          } else {
            return false;
          }
        });
      },
      beforeUpload(file) {
        const loading = this.$messageLoading({
          message: '正在上传中..',
          background: 'rgba(0, 0, 0, .15)'
        });
        uploadFile(file)
          .then(({ view_files }) => {
            const fileName = file.name.split('.').pop();
            console.log(fileName);
            if (fileName === 'apk') {
              this.form.pkg = view_files;
            } else if (fileName === 'wgt') {
              this.form.wgt = view_files;
            }
            loading.close();
          })
          .catch((e) => {
            this.loading = false;
            this.$message.error(e);
          });
        return false;
      }
    }
  };
</script>
<style>
  .el-upload {
    width: 100% !important;
  }
</style>
