<template>
  <div>
    <div class="ele-page-header">
      <div class="ele-page-title">{{ $t('route.config._name') }}</div>
      <!-- <div class="ele-page-desc">
        表单页用于向用户收集或验证信息, 基础表单常见于数据项较少的表单场景。
      </div> -->
    </div>
    <div class="ele-body">
      <el-card shadow="never">
        <el-form
          ref="form"
          :model="form"
          :rules="rules"
          label-width="200"
          style="max-width: 1000px; margin: 10px auto"
        >
          <el-row :gutter="15">
            <el-col :sm="12">
              <el-form-item :label="`${$t('validity.validityBols.cmr_out_hour')}:`">
                <el-input v-model="form.cmr_out_hour" clearable />
              </el-form-item>
            </el-col>
            <el-col :sm="12">
              <el-form-item :label="`${$t('validity.validityBols.plan_hour')}:`">
                <el-input v-model="form.plan_hour" clearable />
              </el-form-item>
            </el-col>
            <el-col :sm="12">
              <el-form-item :label="`${$t('validity.validityBols.noa_pmc_hour')}:`">
                <el-input v-model="form.noa_pmc_hour" clearable />
              </el-form-item>
            </el-col>
            <el-col :sm="12">
              <el-form-item :label="`${$t('validity.validityBols.noa_loose_hour')}:`">
                <el-input v-model="form.noa_loose_hour" clearable />
              </el-form-item>
            </el-col>
            <el-col :sm="12">
              <el-form-item :label="`${$t('validity.validityBols.cmr_pmc_hour')}:`">
                <el-input v-model="form.cmr_pmc_hour" clearable />
              </el-form-item>
            </el-col>
            <el-col :sm="12">
              <el-form-item :label="`${$t('validity.validityBols.cmr_loose_hour')}:`">
                <el-input v-model="form.cmr_loose_hour" clearable />
              </el-form-item>
            </el-col>
            <el-col :sm="12">
              <el-form-item :label="`${$t('validity.validityBols.cmr_in_hour')}:`">
                <el-input v-model="form.cmr_in_hour" clearable />
              </el-form-item>
            </el-col>
            <el-col :sm="12">
              <el-form-item :label="`${$t('validity.validityBols.edt_in_hour')}:`">
                <el-input v-model="form.edt_in_hour" clearable />
              </el-form-item>
            </el-col>
            <el-col :sm="12">
              <el-form-item :label="`${$t('validity.validityBols.customs_hour')}:`">
                <el-input v-model="form.customs_hour" clearable />
              </el-form-item>
            </el-col>
            <el-col :sm="12">
              <el-form-item :label="`${$t('validity.validityBols.station_hour')}:`">
                <el-input v-model="form.station_hour" clearable />
              </el-form-item>
            </el-col>
            <el-col :sm="12">
              <el-form-item :label="`${$t('validity.validityBols.box_hour')}:`">
                <el-input v-model="form.box_hour" clearable />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="15">
            <el-col :sm="12">
              <el-form-item>
                <el-button type="primary" :loading="loading" @click="submit">
                  {{ $t('basics.save') }}
                </el-button>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </el-card>
    </div>
  </div>
</template>

<script>
  import { getProcess, setConfig } from '@/api/views';
  const form = {
    cmr_out_hour: 0, //出库时效
    plan_hour: 0, //预报段时效
    noa_pmc_hour: 0, //NOA段(PMC)
    noa_loose_hour: 0, //NOA段(LOOSE)
    cmr_pmc_hour: 0, //提货调度段(NOA)
    cmr_loose_hour: 0, //提货调度段(LOOSE)
    cmr_in_hour: 0, //货站装车段
    edt_in_hour: 0, //货站到EDT段
    box_hour: 0, //出库调整段
    customs_hour: 0, //清关段
    station_hour: 0 //库操段
  };
  //   “航班港口”为FRA收件人邮箱：
  export default {
    name: 'FormBasic',
    data() {
      return {
        // 提交状态
        loading: false,
        // 表单数据
        form: { ...form },
        // 表单验证规则
        rules: {}
      };
    },
    async mounted() {
      const config = await getProcess();
      this.form = config.content;
    },
    methods: {
      /* 提交 */
      submit() {
        this.$refs['form'].validate((valid) => {
          if (valid) {
            this.loading = true;
            setConfig({
              key: 'process',
              data: this.form
            }).then(() => {
              this.loading = false;
              this.$message.success(this.$t('basics.success'));
            });
          } else {
            return false;
          }
        });
      }
    }
  };
</script>
<style>
  .el-upload {
    width: 100% !important;
  }
</style>
