<!-- 编辑弹窗 -->
<template>
  <ele-modal
    width="720px"
    :visible="visible"
    custom-class="ele-dialog-form"
    :title="$t('message.info')"
    @update:visible="updateVisible"
  >
    <div>
      {{ form.content }}
    </div>
    <div slot="footer">
      <el-button @click="updateVisible(false)">
        {{ $t('message.cancel') }}
      </el-button>
    </div>
  </ele-modal>
</template>

<script>
  import { changeReadStatus } from '@/api/system/user';

  const DEFAULT_FORM = {
    content: null
  };

  export default {
    name: 'messageInfo',
    props: {
      // 弹窗是否打开
      visible: Boolean,
      // 修改回显的数据
      data: Object
    },
    data() {
      return {
        // 表单数据
        form: { ...DEFAULT_FORM }
      };
    },
    methods: {
      /* 更新visible */
      updateVisible(value) {
        this.$emit('update:visible', value);
      }
    },
    watch: {
      async visible(visible) {
        if (visible) {
          let data = this.data;
          if (this.data) {
            this.$util.assignObject(this.form, {
              ...data
            });
            changeReadStatus(data.id).then();
          }
        } else {
          this.form = { ...DEFAULT_FORM };
        }
      }
    }
  };
</script>
