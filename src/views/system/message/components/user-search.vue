<!-- 搜索表单 -->
<template>
  <el-form
    label-width="77px"
    class="ele-form-search"
    @keyup.enter.native="search"
    @submit.native.prevent
  >
    <el-row :gutter="15">
      <el-col :lg="6" :md="12">
        <el-form-item label="用户账号:">
          <el-input clearable v-model="where.name" placeholder="请输入" />
        </el-form-item>
      </el-col>
      <el-col :lg="6" :md="12">
        <el-form-item label="用户昵称:">
          <el-input clearable v-model="where.nickname" placeholder="请输入" />
        </el-form-item>
      </el-col>
      <el-col :lg="6" :md="12">
        <div class="ele-form-actions">
          <el-button
            type="primary"
            icon="el-icon-search"
            class="ele-btn-icon"
            @click="search"
          >
            {{ $t('basics.query') }}
          </el-button>
          <el-button @click="reset">{{ $t('basics.reset') }}</el-button>
        </div>
      </el-col>
    </el-row>
  </el-form>
</template>

<script>
  // import RoleSelect from './role-select';
  const DEFAULT_WHERE = {
    name: '',
    nickname: '',
    role_id: null
  };

  export default {
    name: 'UserSearch',
    components: { },
    data() {
      return {
        // 表单数据
        where: { ...DEFAULT_WHERE }
      };
    },
    methods: {
      /* 搜索 */
      search() {
        this.$emit('search', this.where);
      },
      roleSelectCallback(value) {
        this.where.role_id = value.id;
      },
      /*  重置 */
      reset() {
        this.where = { ...DEFAULT_WHERE };
        this.search();
      }
    }
  };
</script>
