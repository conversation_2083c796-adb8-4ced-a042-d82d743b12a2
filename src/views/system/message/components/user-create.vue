<!-- 编辑弹窗 -->
<template>
  <ele-modal
    width="720px"
    :visible="visible"
    :close-on-click-modal="false"
    custom-class="ele-dialog-form"
    :title="isUpdate ? '修改用户' : '新建用户'"
    @update:visible="updateVisible"
  >
    <el-form ref="form" :model="form" :rules="rules" label-width="92px">
      <el-row :gutter="15">
        <el-col :sm="12">
          <el-form-item label="用户账号:" prop="name">
            <el-input
              clearable
              :maxlength="60"
              :minlength="16"
              type="text"
              v-model="form.name"
              :disabled="isUpdate"
              placeholder="请输入用户账号"
            />
          </el-form-item>
          <el-form-item label="角色:" prop="role_id">
            <role-select
              v-model="form.role_id"
              @callback="roleSelectCallback"
            />
          </el-form-item>
          <!--  货主信息修改  -->
        </el-col>
        <el-col :sm="12">
          <el-form-item label="登录密码:" prop="password">
            <el-input
              show-password
              :maxlength="20"
              v-model="form.password"
              placeholder="请输入登录密码"
            />
          </el-form-item>
          <el-form-item
            label="确认密码:"
            prop="password_confirm"
          >
            <el-input
              show-password
              :maxlength="20"
              v-model="form.password_confirm"
              placeholder="请输入登录密码"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="15">
        <el-col :sm="12">
          <el-form-item label="用户昵称:" prop="nickname">
            <el-input
              clearable
              :maxlength="20"
              v-model="form.nickname"
              placeholder="请输入用户昵称"
            />
          </el-form-item>
        </el-col>
        <el-col :sm="12">
          <el-form-item label="用户邮箱:" prop="email">
            <el-input
              clearable
              :maxlength="60"
              v-model="form.email"
              placeholder="请输入用户邮箱"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <div style="margin: 6px 0 28px 0" v-if="form.role_type === '2'">
        <el-divider />
      </div>
    </el-form>
    <div slot="footer">
      <el-button @click="updateVisible(false)">
        {{ $t('basics.cancel') }}
      </el-button>
      <el-button type="primary" :loading="loading" @click="save">
        {{ $t('basics.save') }}
      </el-button>
    </div>
  </ele-modal>
</template>

<script>
  import RoleSelect from './role-select';
  import { addUser, updateUser } from '@/api/system/user';

  const DEFAULT_FORM = {
    id: null,
    nickname: '',
    name: '',
    email: '',
    password: '',
    password_confirm: '',
    role_id: null,
    role: '',
  };

  export default {
    name: 'UserEdit',
    components: { RoleSelect },
    props: {
      // 弹窗是否打开
      visible: Boolean,
      // 修改回显的数据
      data: Object
    },
    data() {
      return {
        // 表单数据
        form: { ...DEFAULT_FORM },
        // 表单验证规则
        rules: {
          name: [
            {
              required: true,
              message: '请输入用户账号',
              trigger: 'blur'
            }
          ],
          nickname: [
            {
              required: true,
              message: '请输入昵称',
              trigger: 'blur'
            }
          ],
          role_id: [
            {
              required: true,
              message: '请选择角色',
              trigger: 'blur'
            }
          ],
          password: [
            {
              required: true,
              pattern: /^[\S]{6,18}$/,
              message: '密码必须为6-18位非空白字符',
              trigger: 'blur'
            }
          ],
          password_confirm: [
            {
              required: true,
              trigger: 'blur',
              validator: (rule, value, callback) => {
                if (!value) {
                  return callback(new Error('请输入确认密码'));
                }
                if (value !== this.form.password) {
                  return callback(new Error('两次属于的密码不一致'));
                }
                callback();
              }
            }
          ]
        },
        // 提交状态
        loading: false,
        // 是否是修改
        isUpdate: false
      };
    },
    methods: {
      /* 保存编辑 */
      save() {
        this.$refs['form'].validate((valid) => {
          if (!valid) {
            return false;
          }
          this.loading = true;
          const data = {
            ...this.form
          };
          const saveOrUpdate = this.isUpdate ? updateUser : addUser;
          saveOrUpdate(data)
            .then((msg) => {
              this.loading = false;
              this.updateVisible(false);
              this.$emit('done');
              this.$message.success(this.$t('basics.success'));
            })
            .catch((e) => {
              this.loading = false;
              this.$message.error(e);
            });
        });
      },
      /* 更新visible */
      updateVisible(value) {
        this.$emit('update:visible', value);
      },
      /* 角色选择回调*/
      roleSelectCallback(value) {
        this.form.role_type = value.type;
      }
    },
    watch: {
      async visible(visible) {
        console.log(this.data);
        if (visible) {
          let data = this.data;
          if (this.data) {
            this.$util.assignObject(this.form, {
              ...data,
            });
            this.isUpdate = true;
          } else {
            this.isUpdate = false;
          }
        } else {
          this.$refs['form'].clearValidate();
          this.form = { ...DEFAULT_FORM };
        }
      }
    }
  };
</script>
