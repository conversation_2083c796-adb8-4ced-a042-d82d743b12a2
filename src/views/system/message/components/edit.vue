<!-- 编辑弹窗 -->
<template>
  <ele-modal
    width="720px"
    :visible="visible"
    :close-on-click-modal="false"
    custom-class="ele-dialog-form"
    title="编辑提单"
    @update:visible="updateVisible"
  >
    <el-form ref="form" :model="form" :rules="rules" label-width="92px">
      <el-row :gutter="15">
        <el-col :sm="12">
          <el-form-item label="提单号:" prop="bol_no">
            <el-input
              clearable
              :maxlength="50"
              show-word-limit
              v-model="form.bol_no"
              :disabled="true"
              placeholder="请输入提单号"
            />
          </el-form-item>
        </el-col>
        <el-col :sm="12">
          <el-form-item label="航班号:" prop="flight_no">
            <el-input
              clearable
              :maxlength="50"
              show-word-limit
              v-model="form.flight_no"
              placeholder="请输入航班号"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="15">
        <el-col :sm="12">
          <el-form-item label="T1:" prop="t1">
            <el-select
              v-model="form.t1"
              placeholder="请选择是否T1"
              clearable
              class="ele-fluid"
            >
              <el-option label="否" :value="1" />
              <el-option label="是" :value="2" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :sm="12">
          <el-form-item label="LGG:" prop="lgg">
            <el-select
              v-model="form.lgg"
              placeholder="请选择是否LGG"
              clearable
              class="ele-fluid"
            >
              <el-option label="否" :value="1" />
              <el-option label="是" :value="2" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="15">
        <el-col :sm="12">
          <el-form-item label="B2B:" prop="b2b">
            <el-select
              v-model="form.b2b"
              placeholder="请选择是否B2B"
              clearable
              class="ele-fluid"
            >
              <el-option label="否" :value="1" />
              <el-option label="是" :value="2" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :sm="12">
          <el-form-item label="计费重量:" prop="gross_mass_kg">
            <el-input
              clearable
              :maxlength="50"
              show-word-limit
              v-model="form.gross_mass_kg"
              placeholder="请输入计费重量"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="15">
        <el-col :sm="12">
          <el-form-item label="换标数:" prop="exchange_flag">
            <el-input
              clearable
              :maxlength="50"
              show-word-limit
              v-model="form.exchange_flag"
              placeholder="请输入换标数"
            />
          </el-form-item>
        </el-col>
        <el-col :sm="12">
          <el-form-item label="高价值数量:" prop="expensive">
            <el-input
              clearable
              :maxlength="50"
              show-word-limit
              v-model="form.expensive"
              placeholder="请输入高价值数量"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div slot="footer">
      <el-button @click="updateVisible(false)">
        {{ $t('basics.cancel') }}
      </el-button>
      <el-button type="primary" :loading="loading" @click="save">
        {{ $t('basics.save') }}
      </el-button>
    </div>
  </ele-modal>
</template>

<script>
  import { edit } from '@/api/order/awb'

  const DEFAULT_FORM = {
    id: null,
    bol_no:"",
    flight_time: "",
    flight_no:"",
    gross_mass_kg:"",
    exchange_flag:null,
    lgg:null,
    b2b:null,
    t1:null,
    expensive:null,
    clearance_fee:null,
    attach_fee:null
  };

  export default {
    name: 'EditAwb',
    components: { },
    props: {
      // 弹窗是否打开
      visible: Boolean,
      // 修改回显的数据
      data: Object
    },
    data() {
      return {
        // 表单数据
        form: { ...DEFAULT_FORM },
        // 表单验证规则
        rules: {
          // time2: [
          //   {
          //     required: true,
          //     message: '请输入地址代码',
          //     trigger: 'blur'
          //   }
          // ],
          // time3: [
          //   {
          //     required: true,
          //     message: '请输入地址代码',
          //     trigger: 'blur'
          //   }
          // ],
        },
        // 提交状态
        loading: false,
        // 是否是修改
        isUpdate: false
      };
    },
    methods: {
      /* 保存编辑 */
      save() {
        this.$refs['form'].validate((valid) => {
          if (!valid) {
            return false;
          }
          this.loading = true;
          const data = {
            ...this.form
          };
          edit(data)
            .then((msg) => {
              this.loading = false;
              this.$message.success(this.$t('basics.success'));
              this.updateVisible(false);
              this.$emit('done');
            })
            .catch((e) => {
              this.loading = false;
              this.$message.error(e);
            });
        });
      },
      /* 更新visible */
      updateVisible(value) {
        this.$emit('update:visible', value);
      },
      /* 角色选择回调*/
      roleSelectCallback(value) {
        this.form.role_type = value.type;
      }
    },
    watch: {
      async visible(visible) {
        console.log(this.data)
        if (visible) {
          let data = this.data;
          if (this.data) {
            this.$util.assignObject(this.form, {
              ...data
            });
            this.isUpdate = true;
          } else {
            this.isUpdate = false;
          }
        } else {
          this.$refs['form'].clearValidate();
          this.form = { ...DEFAULT_FORM };
        }
      }
    }
  };
</script>
