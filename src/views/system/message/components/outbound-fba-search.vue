<template>
  <el-card shadow="never" body-style="padding: 22px 22px 7px 22px;">
    <!-- 搜索表单 -->
    <el-form
      label-width="145px"
      class="ele-form-search"
      @keyup.enter.native="search"
      @submit.native.prevent
    >
      <el-row :gutter="15">
        <el-col :lg="8" :md="12">
          <el-form-item :label="this.$t('awb.noa.awbno') + ':'" prop="bol_no">
            <el-input
              v-model="where.bol_no"
              :placeholder="this.$t('basics.pleaseInput')"
            />
          </el-form-item>
        </el-col>
        <el-col :lg="8" :md="12">
          <el-form-item :label="this.$t('awb.box.box') + ':'">
            <el-input
              clearable
              show-word-limit
              v-model="where.box_no"
              :placeholder="this.$t('basics.pleaseInput')"
            />
          </el-form-item>
        </el-col>
        <el-col :lg="8" :md="12">
          <el-form-item :label="this.$t('awb.package.no') + ':'">
            <el-input
              clearable
              show-word-limit
              v-model="where.track_no"
              :placeholder="this.$t('basics.pleaseInput')"
            />
          </el-form-item>
        </el-col>

        <el-col :lg="8" :md="12">
          <el-form-item :label="this.$t('message.boxtype') + ':'">
            <el-select
              v-model="where.type"
              :placeholder="this.$t('message.boxtype')"
              clearable
              class="ele-fluid"
            >
              <el-option
                :label="this.$t('message.label1')"
                :value="5"
                v-if="this.$hasRole('sonOwner')"
              />
              <el-option
                :label="this.$t('message.label2')"
                :value="6"
                v-if="this.$hasRole('sonOwner')"
              />
              <el-option
                :label="this.$t('message.label3')"
                :value="0"
                v-if="this.$hasRole('sonOwner')"
              />
              <el-option
                :label="this.$t('message.label4')"
                :value="3"
                v-if="this.$hasRole('sonOwner')"
              />
              <el-option
                :label="this.$t('message.label5')"
                :value="4"
                v-if="this.$hasRole('sonOwner')"
              />
              <el-option
                :label="this.$t('message.label6')"
                :value="2"
                v-if="this.$hasRole('sonOwner')"
              />
              <el-option
                :label="this.$t('message.label7')"
                :value="7"
                v-if="!this.$hasRole('sonOwner')"
              />
              <el-option
                :label="this.$t('message.label8')"
                :value="8"
                v-if="!this.$hasRole('sonOwner')"
              />
            </el-select>
          </el-form-item> </el-col
        ><el-col :lg="8" :md="12">
          <el-form-item :label="$t('system.message.readOrNot') + ':'">
            <el-select v-model="where.status" clearable class="ele-fluid">
              <el-option :label="$t('system.message.read')" :value="2" />
              <el-option :label="$t('system.message.not')" :value="1" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :lg="8" :md="12">
          <div class="ele-form-actions">
            <el-button
              type="primary"
              icon="el-icon-search"
              class="ele-btn-icon"
              @click="search"
            >
              {{ $t('basics.query') }}
            </el-button>
            <el-button @click="reset">{{ $t('basics.reset') }}</el-button>
          </div>
        </el-col>
      </el-row>
    </el-form>
  </el-card>
</template>

<script>
  // 默认表单数据

  const DEFAULT_WHERE = {
    bol_no: '',
    box_no: '',
    track_no: '',
    status: '',
    type: ''
  };

  export default {
    name: 'outbound-fba-search',

    components: {},
    data() {
      return {
        // 表单数据
        where: { ...DEFAULT_WHERE },
        // 搜索表单是否展开
        searchExpand: false,
        goodsCategoryList: [],
        category_id: ''
      };
    },
    methods: {
      /* 搜索 */
      search() {
        //解决搜索 也搜索下级的数据
        this.$emit('search', this.where);
      },
      /* 重置搜索 */
      reset() {
        this.where = { ...DEFAULT_WHERE };
        this.search();
      },
      /* 搜索展开/收起 */
      toggleExpand() {
        this.searchExpand = !this.searchExpand;
        this.$emit('expand-change', this.searchExpand);
      },
      /* 搜索展开/收起 */
      goodsCategoryFilterListClick(e) {
        this.goodsCategoryList = e.data;
      }
    }
  };
</script>
