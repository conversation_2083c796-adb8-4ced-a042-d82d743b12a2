<!-- 修改密码弹窗 -->
<template>
  <ele-modal
    width="420px"
    title="修改密码"
    :visible="visible"
    :append-to-body="true"
    :close-on-click-modal="false"
    @update:visible="updateVisible"
    @closed="onClose"
  >
    <el-form
      ref="form"
      :model="form"
      :rules="rules"
      label-width="82px"
      @keyup.enter.native="save"
    >
      <el-form-item label="新密码:" prop="password">
        <el-input
          show-password
          v-model="form.password"
          placeholder="请输入新密码"
        />
      </el-form-item>
      <el-form-item label="确认密码:" prop="password_confirm">
        <el-input
          show-password
          v-model="form.password_confirm"
          placeholder="请再次输入新密码"
        />
      </el-form-item>
    </el-form>
    <div slot="footer">
      <el-button @click="updateVisible(false)">取消</el-button>
      <el-button type="primary" @click="save">确定</el-button>
    </div>
  </ele-modal>
</template>

<script>
  import { updateUser } from '@/api/system/user';
  export default {
    name: 'user-change-password.vue',
    props: {
      data: Object,
      visible: Boolean
    },
    data() {
      return {
        // 按钮loading
        loading: false,
        // 表单数据
        form: {
          oldPassword: '',
          password: '',
          password_confirm: ''
        },
        // 表单验证
        rules: {
          password: [
            {
              required: true,
              message: '请输入新密码',
              trigger: 'blur'
            }
          ],
          password_confirm: [
            {
              required: true,
              trigger: 'blur',
              validator: (_rule, value, callback) => {
                if (!value) {
                  return callback(new Error('请再次输入新密码'));
                }
                if (value !== this.form.password) {
                  return callback(new Error('两次输入密码不一致'));
                }
                callback();
              }
            }
          ]
        }
      };
    },
    methods: {
      /* 修改visible */
      updateVisible(value) {
        this.$emit('update:visible', value);
      },
      /* 保存修改 */
      save() {
        this.$refs['form'].validate((valid) => {
          // eslint-disable-next-line no-empty
          if (valid) {
            this.loading = true;
            updateUser({
              id: this.data.id,
              role_id:this.data.role_id,
              password: this.form.password,
              // password_confirm: this.form.password_confirm
            })
              .then((msg) => {
                this.onClose();
                this.updateVisible(false);
                this.$message.success(msg.status);
              })
              .catch((e) => {
                this.loading = false;
                this.$message.error(e);
              });
          }
        });
      },
      /* 关闭回调 */
      onClose() {
        this.form = {
          oldPassword: '',
          password: '',
          password2: ''
        };
        this.$refs['form'].resetFields();
        this.loading = false;
      }
    }
  };
</script>
