<template>
  <div class="ele-body">
    <el-card shadow="never">
      <!-- 搜索表单 -->
      <!-- <user-search @search="reload" /> -->
      <outbound-fba-search @search="reload" />
      <!-- 数据表格 -->
      <ele-pro-table
        ref="table"
        :columns="columnsData"
        :datasource="datasource"
        :selection.sync="selection"
      >
        <template slot="action" slot-scope="{ row }">
          <el-link @click="openInfo(row)" type="primary">
            {{ $t('message.info') }}
          </el-link>
        </template>
      </ele-pro-table>
      <message-info :visible.sync="showMessageInfo" :data="current" />
    </el-card>
  </div>
</template>

<script>
  import { getUserMail } from '@/api/system/user';
  import MessageInfo from '@/views/system/message/components/info';
  import OutboundFbaSearch from './components/outbound-fba-search';
  export default {
    name: 'SystemUser',
    components: {
      MessageInfo,
      OutboundFbaSearch
    },
    data() {
      return {
        // 表格列配置
        columnsData: [
          {
            columnKey: 'index',
            type: 'index',
            width: 45,
            align: 'center',
            showOverflowTooltip: true,
            fixed: 'left'
          },
          {
            prop: 'content',
            label: this.$t('message.content'),
            sortable: 'custom',
            showOverflowTooltip: true,
            minWidth: 240
          },
          {
            prop: 'created_at',
            // label: '时间',
            label: this.$t('message.time'),
            showOverflowTooltip: true,
            width: 165
          },
          {
            prop: 'status',
            label: this.$t('system.message.readOrNot'),
            showOverflowTooltip: true,
            minWidth: 50,
            align: 'center',
            formatter: (row, column, cellValue) => {
              return {
                1: this.$t('system.message.not'),
                2: this.$t('system.message.read')
              }[cellValue];
            }
          },
          {
            columnKey: 'action',
            label: this.$t('message.action'),
            width: 120,
            align: 'center',
            resizable: false,
            slot: 'action',
            hideInSetting: true
          }
        ],
        // 表格选中数据
        selection: [],
        // 当前编辑数据
        current: null,
        // 是否显示编辑弹窗
        showEdit: false,
        showFinanceEdit: false,
        showCreate: false,
        // 是否显示导入弹窗
        showImport: false,
        // 是否显修改密码
        userId: null,
        showMessageInfo: false
      };
    },
    methods: {
      columns() {
        return this.handleListName(this.columnsData, 'system.user');
      },
      /* 表格数据源 */
      datasource({ page, limit, where, order }) {
        return getUserMail({
          ...where,
          ...order,
          page: page,
          num: limit
        });
      },
      /* 刷新表格 */
      reload(where) {
        this.$refs.table.reload({ page: 1, where: where });
      },
      /* 打开编辑弹窗 */
      openInfo(row) {
        this.current = row;
        this.showMessageInfo = true;
      }
    }
  };
</script>
