<template>
  <div class="ele-body">
    <el-card shadow="never">
      <!-- 搜索表单 -->
      <!-- <menu-search @search="reload" /> -->
      <!-- 数据表格 -->
      <ele-pro-table
        ref="table"
        row-key="menu_id"
        :columns="columns"
        :datasource="datasource"
        default-expand-all
        :need-page="false"
        :parse-data="parseData"
        @done="onDone"
      >
        <!-- 表头工具栏 -->
        <template slot="toolbar">
          <el-button
            size="small"
            type="primary"
            icon="el-icon-plus"
            class="ele-btn-icon"
            @click="openEdit()"
          >
            添加
          </el-button>
          <el-button class="ele-btn-icon" size="small" @click="expandAll">
            展开全部
          </el-button>
          <el-button class="ele-btn-icon" size="small" @click="foldAll">
            折叠全部
          </el-button>
        </template>
        <!-- 标题列 -->
        <template slot="name" slot-scope="{ row }">
          <i :class="row.icon"></i> {{ row.name }}
        </template>
        <!-- 类型列 -->
        <template slot="menuType" slot-scope="{ row }">
          <el-tag
            :type="['', 'primary', 'info'][row.menu_type]"
            size="mini"
            :disable-transitions="true"
          >
            {{ ['', '菜单', '按钮'][row.menu_type] }}
          </el-tag>
        </template>
        <!-- 操作列 -->
        <template slot="action" slot-scope="{ row }">
          <el-link
            type="primary"
            :underline="false"
            icon="el-icon-plus"
            @click="openEdit(null, row.id)"
          >
            添加
          </el-link>
          <el-link
            type="primary"
            :underline="false"
            icon="el-icon-edit"
            @click="openEdit(row)"
          >
            修改
          </el-link>
          <el-popconfirm
            class="ele-action"
            title="确定要删除吗？"
            @confirm="remove(row)"
          >
            <el-link
              type="danger"
              slot="reference"
              :underline="false"
              icon="el-icon-delete"
            >
              删除
            </el-link>
          </el-popconfirm>
        </template>
      </ele-pro-table>
    </el-card>
    <!-- 编辑弹窗 -->
    <menu-edit
      :data="current"
      :parent_id="parent_id"
      :menu-list="menuList"
      :visible.sync="showEdit"
      @done="reload"
    />
  </div>
</template>

<script>
  import MenuSearch from './components/menu-search';
  import MenuEdit from './components/menu-edit';
  import { listMenus, removeMenu } from '@/api/system/menu';

  export default {
    name: 'SystemMenu',
    components: { MenuSearch, MenuEdit },
    data() {
      return {
        // 表格列配置
        columns: [
          // {
          //   columnKey: 'index',
          //   type: 'index',
          //   width: 45,
          //   align: 'center',
          //   showOverflowTooltip: true,
          //   fixed: 'left'
          // },
          {
            prop: 'title',
            label: '菜单名称',
            showOverflowTooltip: true,
            minWidth: 110,
            slot: 'title'
          },
          {
            prop: 'website_path',
            label: '路由地址',
            showOverflowTooltip: true,
            minWidth: 110
          },
          {
            prop: 'component',
            label: '组件路径',
            showOverflowTooltip: true,
            minWidth: 110
          },
          {
            prop: 'authority',
            label: '权限标识',
            showOverflowTooltip: true,
            minWidth: 110
          },
          {
            prop: 'path',
            label: '接口地址',
            showOverflowTooltip: true,
            minWidth: 110
          },
          {
            prop: 'sort_number',
            label: '排序',
            align: 'center',
            showOverflowTooltip: true,
            width: 60
          },
          {
            prop: 'hide',
            label: '可见',
            align: 'center',
            showOverflowTooltip: true,
            width: 60,
            formatter: (row, column, cellValue) => {
              return ['', '是', '否'][cellValue];
            }
          },
          {
            prop: 'menu_type',
            label: '类型',
            align: 'center',
            showOverflowTooltip: true,
            width: 60,
            slot: 'menuType'
          },
          // {
          //   prop: 'menu_type',
          //   label: '类型',
          //   align: 'center',
          //   showOverflowTooltip: true,
          //   width: 60,
          //   formatter: (row, column, cellValue) => {
          //     return ['', '菜单', '按钮'][cellValue];
          //   }
          // },
          // {
          //   prop: 'date_add',
          //   label: this.$t('basics.status'),
          //   showOverflowTooltip: true,
          //   minWidth: 110,
          //   formatter: (row, column, cellValue) => {
          //     return this.$util.toDateString(parseInt(cellValue));
          //   }
          // },
          // {
          //   prop: 'date_update',
          //   label: '更新时间',
          //   showOverflowTooltip: true,
          //   minWidth: 110,
          //   formatter: (row, column, cellValue) => {
          //     return this.$util.toDateString(parseInt(cellValue));
          //   }
          // },
          {
            columnKey: 'action',
            label: this.$t('basics.action'),
            width: 190,
            align: 'center',
            resizable: false,
            slot: 'action',
            hideInSetting: true
          }
        ],
        // 当前编辑数据
        current: null,
        // 是否显示编辑弹窗
        showEdit: false,
        // 全部菜单数据
        menuList: [],
        // 上级菜单id
        parent_id: null
      };
    },
    methods: {
      /* 表格数据源 */
      datasource({ where }) {
        let a = listMenus()
        return a
      },
      /* 数据转为树形结构 */
      parseData(data) {
        return this.$util.toTreeData({
          data: data,
          idField: 'menu_id',
          parentIdField: 'parent_id'
        });
      },
      /* 表格渲染完成回调 */
      onDone({ data }) {
        //if (!this.menuList.length) {
        this.menuList = data;
        //}
      },
      /* 刷新表格 */
      reload(where) {
        this.$refs.table.reload({ where: where });
      },
      /* 显示编辑 */
      openEdit(row, parentId) {
        if (row) {
          this.current = {
            ...row,
            // role_type:
            //   row && row.role_type
            //     ? row.role_type.split(',').map(function (i) {
            //         return parseInt(i);
            //       })
            //     : []
          };
        } else {
          this.current = null;
        }
        console.log(this.current)
        this.parent_id = parentId;
        this.showEdit = true;
      },
      /* 删除 */
      remove(row) {
        if (row.children?.length) {
          this.$message.error('请先删除子节点');
          return;
        }
        const loading = this.$loading({ lock: true });
        removeMenu({
          id: row.menu_id
        })
          .then((msg) => {
            loading.close();
            this.$message.success(this.$t('basics.success'));
            this.reload();
          })
          .catch((e) => {
            loading.close();
            this.$message.error(e);
          });
      },
      /* 展开全部 */
      expandAll() {
        this.$refs.table.toggleRowExpansionAll(true);
      },
      /* 折叠全部 */
      foldAll() {
        this.$refs.table.toggleRowExpansionAll(false);
      }
    }
  };
</script>
