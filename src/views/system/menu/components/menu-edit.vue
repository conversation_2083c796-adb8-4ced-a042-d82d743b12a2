<!-- 编辑弹窗 -->
<template>
  <ele-modal
    width="720px"
    :visible="visible"
    :close-on-click-modal="false"
    custom-class="ele-dialog-form"
    :title="isUpdate ? '修改菜单' : '添加菜单'"
    @update:visible="updateVisible"
  >
    <el-form ref="form" :model="form" :rules="rules" label-width="92px">
      <el-row :gutter="15">
        <el-col :sm="12">
          <el-form-item label="上级菜单:">
            <ele-tree-select
              clearable
              v-model="form.parent_id"
              :data="menuList"
              label-key="title"
              value-key="menu_id"
              default-expand-all
              placeholder="请选择上级菜单"
            />
          </el-form-item>
          <el-form-item label="菜单名称:" prop="title">
            <el-input
              clearable
              v-model="form.title"
              placeholder="请输入菜单名称"
            />
          </el-form-item>
        </el-col>
        <el-col :sm="12">
          <el-form-item label="菜单类型:">
            <el-radio-group v-model="form.menu_type" @change="onMenuTypeChange">
              <el-radio :label="1">菜单</el-radio>
              <el-radio :label="2">按钮</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="是否可见:">
            <el-switch
              :active-value="1"
              :inactive-value="2"
              v-model="form.hide"
              :disabled="form.type == 2"
            />
            <el-tooltip
              placement="top"
              content="选择不可见只注册路由不显示在侧边栏，比如添加页面应该选择不可见"
            >
              <i
                class="el-icon-_question"
                style="vertical-align: middle; margin-left: 8px"
              ></i>
            </el-tooltip>
          </el-form-item>
        </el-col>
      </el-row>
      <div style="margin: 6px 0 28px 0">
        <el-divider />
      </div>
      <el-row :gutter="15">
        <el-col :sm="12">
          <el-form-item name="path">
            <template slot="label">
              <el-tooltip
                v-if="form.type == '1'"
                placement="top"
                content="需要以`http://`、`https://`、`//`开头"
              >
                <i class="el-icon-_question"></i>
              </el-tooltip>
              <span>路由地址</span>
            </template>
            <el-input
              clearable
              v-model="form.website_path"
              :disabled="form.type == 2"
              placeholder="请输入路由地址"
            />
          </el-form-item>
          <el-form-item name="component" label="组件路径:">
            <el-input
              clearable
              v-model="form.component"
              :disabled="form.type === 2"
              placeholder="请输入内链地址"
            />
          </el-form-item>
          <el-form-item name="path" label="接口地址:">
            <el-input
              clearable
              v-model="form.path"
              placeholder="请输入接口地址"
            />
          </el-form-item>
          <!-- <el-form-item label="权限:">
            <el-select
              class="ele-block"
              v-model="form.role_type"
              multiple
              filterable
              allow-create
              default-first-option
              placeholder="请选择权限归属"
            >
              <el-option
                v-for="item in options"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item> -->
        </el-col>
        <el-col :sm="12">
          <el-form-item label="权限标识:">
            <el-input
              clearable
              v-model="form.authority"
              placeholder="请输入权限标识"
              :disabled="form.type === 1"
            />
          </el-form-item>
          <el-form-item label="菜单图标:">
            <ele-icon-picker
              v-model="form.icon"
              placeholder="请选择菜单图标"
              :disabled="form.type === 2"
            />
          </el-form-item>
          <el-form-item label="排序号:" prop="sortNumber">
            <el-input-number
              :min="0"
              v-model="form.sort_number"
              placeholder="请输入排序号"
              controls-position="right"
              class="ele-fluid ele-text-left"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div slot="footer">
      <el-button @click="updateVisible(false)">取消</el-button>
      <el-button type="primary" :loading="loading" @click="save">
        保存
      </el-button>
    </div>
  </ele-modal>
</template>

<script>
  import { EleIconPicker } from 'ele-admin';
  import { addMenu, updateMenu } from '@/api/system/menu';
  const DEFAULT_FORM = {
    menu_id: null,
    parent_id: '',
    title: '',
    path:'',
    menu_type: 1,
    open_type:1,
    method:'GET',
    icon: '',
    authority: '',
    sort_number: 1,
    website_path:'',
    component:'',
    hide: 1,
    meta: '',
    role_type: []
  };

  export default {
    name: 'MenuEdit',
    components: { EleIconPicker },
    props: {
      // 弹窗是否打开
      visible: Boolean,
      // 修改回显的数据
      data: Object,
      // 上级菜单id
      parent_id: String,
      // 全部菜单数据
      menuList: Array
    },
    data() {
      return {
        // 表单数据
        form: { ...DEFAULT_FORM },
        // 表单验证规则
        rules: {
          name: [
            {
              required: true,
              message: '请输入菜单名称',
              trigger: 'blur'
            }
          ],
          sort: [
            {
              required: true,
              message: '请输入排序号',
              trigger: 'blur'
            }
          ]
        },
        // 提交状态
        loading: false,
        // 是否是修改
        isUpdate: false,
        options: [
          {
            value: 1,
            label: '系统管理员'
          },
          {
            value: 4,
            label: '仓库'
          },
          {
            value: 2,
            label: '客户'
          },
          {
            value: 3,
            label: '财务'
          },
          {
            value: 5,
            label: 'PDA'
          }
        ]
      };
    },
    methods: {
      /* 保存编辑 */
      save() {
        this.$refs['form'].validate((valid) => {
          if (!valid) {
            return false;
          }
          this.loading = true;
          const data = {
            ...this.form,
            parent_id: this.form.parent_id || 0
          };
          const saveOrUpdate = this.isUpdate ? updateMenu : addMenu;
          saveOrUpdate(data)
            .then((msg) => {
              this.loading = false;
              this.$message.success(this.$t('basics.success'));
              this.updateVisible(false);
              this.$emit('done');
            })
            .catch((e) => {
              this.loading = false;
              this.$message.error(e);
            });
        });
      },
      /* 更新visible */
      updateVisible(value) {
        this.$emit('update:visible', value);
      },
      /* menuType选择改变 */
      onMenuTypeChange() {
        if (this.form.menu_type == 1) {
          this.form.permission_id = null;
        } else {
          this.form.icon = null;
          this.form.website_path = null;
          this.form.componet = null;
          this.form.hide = 1;
        }
      }
    },
    watch: {
      visible(visible) {
        if (visible) {
          if (this.data) {
            this.$util.assignObject(this.form, {
              ...this.data,
              parent_id:
                this.data.parent_id == '0' ? '' : this.data.parent_id ?? '',
            });
            console.log(this.form)
            this.isUpdate = true;
          } else {
            this.form.parent_id = this.parent_id ?? '';
            this.isUpdate = false;
          }
        } else {
          this.$refs['form'].clearValidate();
          this.form = { ...DEFAULT_FORM };
        }
      }
    }
  };
</script>
