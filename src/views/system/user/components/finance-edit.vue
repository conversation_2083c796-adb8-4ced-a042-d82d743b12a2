<!-- 编辑弹窗 -->
<template>
  <ele-modal
    width="1000px"
    :visible="visible"
    :close-on-click-modal="false"
    custom-class="ele-dialog-form"
    :title="isUpdate ? '修改用户' : '新建用户'"
    @update:visible="updateVisible"
  >
    <el-form ref="form" :model="form" :rules="rules" label-width="120px">
      <el-row :gutter="15">
        <el-col :sm="12">
          <el-form-item :label="`${$t('system.user.username')}:`" prop="name">
            <el-input
              clearable
              :maxlength="60"
              :minlength="6"
              v-model="form.name"
              :disabled="true"
            />
          </el-form-item>
          <el-form-item :label="`${$t('system.user.role')}:`" prop="role_id">
            <role-select
              v-model="form.role_id"
              :disabled="true"
              @callback="roleSelectCallback"
            />
          </el-form-item>
        </el-col>
        <el-col :sm="12">
          <el-form-item :label="`${$t('system.user.name')}:`" prop="nickname">
            <el-input
              clearable
              :maxlength="20"
              :disabled="true"
              v-model="form.nickname"
            />
          </el-form-item>
        </el-col>
        <el-col :sm="12">
          <el-form-item :label="`${$t('system.user.mail')}:`" prop="email">
            <el-input
              clearable
              :maxlength="60"
              v-model="form.email"
              :disabled="true"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <el-form ref="form" :model="form1" :rules="rules" label-width="120px">
      <el-row :gutter="15">
        <el-col :sm="12">
          <el-form-item label="计费模式:" prop="type">
            <el-select
              v-model="form1.type"
              placeholder="请选择计费模式"
              clearable
              class="ele-fluid"
            >
              <el-option label="全包" :value="1" />
              <el-option label="详细" :value="2" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="15" v-if="form1.type == 1">
        <el-col :sm="12">
          <el-form-item label="全包计费系数" prop="all">
            <el-input v-model="form1.all" placeholder="" clearable>
            </el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <div v-if="form1.type == 2">
        <el-row :gutter="15">
          <el-col :sm="12">
            <el-form-item label="机场放货费系数" prop="airplane_1">
              <el-input v-model="form1.airplane_1" placeholder="" clearable>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :sm="12">
            <el-form-item label="机场操作费" prop="airplane_2">
              <el-input v-model="form1.airplane_2" placeholder="固定" clearable>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :sm="12">
            <el-form-item label="T1文件费" prop="t1">
              <el-input v-model="form1.t1" placeholder="固定" clearable>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :sm="12">
            <el-form-item label="提货费/还板费系数" prop="car">
              <el-input v-model="form1.car" placeholder="计费重量* 客户系数" clearable>
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="15">
          <el-col :sm="12">
            <el-form-item label="仓租系数" prop="warehouse">
              <el-input v-model="form1.warehouse" placeholder="货站+免仓期外" clearable>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :sm="12">
            <el-form-item label="免仓期" prop="warehouse_free">
              <el-input v-model="form1.warehouse_free" placeholder="天数" clearable>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :sm="12">
            <el-form-item label="提单文件费" prop="bol">
              <el-input v-model="form1.bol" placeholder="固定" clearable>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :sm="12">
            <el-form-item label="清关费" prop="clearance">
              <el-input v-model="form1.clearance" placeholder="固定" clearable>
              </el-input>
            </el-form-item>
          </el-col>

        </el-row>
        <el-row :gutter="15">
          <el-col :sm="12">
            <el-form-item label="仓库操作费系数" prop="warehouse_operate">
              <el-input v-model="form1.warehouse_operate" placeholder="计费重量*系数" clearable>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :sm="12">
            <el-form-item label="仓库换标费系数" prop="warehouse_exchange">
              <el-input v-model="form1.warehouse_exchange" placeholder="标数*系数" clearable>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :sm="12">
            <el-form-item label="高货值系数" prop="warehouse_expensive">
              <el-input v-model="form1.warehouse_expensive" placeholder="高价值包裹数*系数" clearable>
              </el-input>
            </el-form-item>
          </el-col>
          <!-- <el-col :sm="12">
            <el-form-item label="关税附加费" prop="name72">
              <el-input v-model="form1.name72" placeholder="实报实销录入" clearable>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :sm="12">
            <el-form-item label="查验费" prop="name82">
              <el-input v-model="form1.name82" placeholder="海关提供，实报实销录入" clearable>
              </el-input>
            </el-form-item>
          </el-col> -->
        </el-row>
        <el-row :gutter="15">
          <el-col :sm="12">
            <el-form-item label="转运费-计费模式" prop="car_mode">
              <el-select
                v-model="form1.car_mode"
                placeholder="请选择计费模式"
                clearable
                class="ele-fluid"
              >
                <el-option label="计费重量*系数" :value="1" />
                <el-option label="车辆数*系数" :value="2" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :sm="12">
            <el-form-item label="转运费-重量系数" prop="car_mode1">
              <el-input v-model="form1.car_mode1" placeholder="计费重量*系数" clearable>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :sm="12">
            <el-form-item label="转运费-车辆系数" prop="car_mode2">
              <el-input v-model="form1.car_mode2" placeholder="车数*系数" clearable>
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </div>
    </el-form>
    <div slot="footer">
      <el-button @click="updateVisible(false)">
        {{ $t('basics.cancel') }}
      </el-button>
      <el-button type="primary" :loading="loading" @click="save">
        {{ $t('basics.save') }}
      </el-button>
    </div>
  </ele-modal>
</template>

<script>
  import RoleSelect from './role-select';
  import { setUserFinances, getUserFinances } from '@/api/system/user';

  const DEFAULT_FORM = {
    id: null,
    nickname: '',
    name: '',
    email: '',
    password: '',
    password_confirm: '',
    role_id: null,
    role: '',
  };
  const DEFAULT_FORM1 = {
    id:null,
    user_id:null,
    type:1,
    all:"",
    airplane_1:"",
    airplane_2:"",
    t1:"",
    car:"",
    warehouse:"",
    warehouse_free:null,
    warehouse_operate:"",
    warehouse_exchange:"",
    warehouse_expensive:"",
    bol:"",
    clearance:"",
    car_mode:null,
    car_mode1:"",
    car_mode2:"",
  }
  export default {
    name: 'UserEdit',
    components: { RoleSelect },
    props: {
      // 弹窗是否打开
      visible: Boolean,
      // 修改回显的数据
      data: Object
    },
    data() {
      return {
        // 表单数据
        form: { ...DEFAULT_FORM },
        form1:{ ...DEFAULT_FORM1 },
        // 表单验证规则
        rules: {
          name: [
            {
              required: true,
              message: '请输入用户账号',
              trigger: 'blur'
            }
          ],
          nickname: [
            {
              required: true,
              message: '请输入昵称',
              trigger: 'blur'
            }
          ],
          role_id: [
            {
              required: true,
              message: this.$t('basics.pleaseChoose'),
              trigger: 'blur'
            }
          ],
          password: [
            {
              pattern: /^[\S]{5,18}$/,
              message: '密码必须为5-18位非空白字符',
              trigger: 'blur'
            }
          ],
          password_confirm: [
            {
              trigger: 'blur',
              validator: (rule, value, callback) => {
                if (!value) {
                  return callback(new Error('请输入确认密码'));
                }
                if (value !== this.form.password) {
                  return callback(new Error('两次属于的密码不一致'));
                }
                callback();
              }
            }
          ]
        },
        // 提交状态
        loading: false,
        // 是否是修改
        isUpdate: false
      };
    },
    methods: {
      /* 保存编辑 */
      save() {
        this.$refs['form'].validate((valid) => {
          if (!valid) {
            return false;
          }
          this.loading = true;
          const data = {
            ...this.form1
          };
          // const saveOrUpdate = this.isUpdate ? updateUser : addUser;
          setUserFinances(data)
            .then((msg) => {
              this.loading = false;
              this.updateVisible(false);
              this.$message.success(this.$t('basics.success'));
              this.$emit('done');
            })
            .catch((e) => {
              this.loading = false;
              this.$message.error(e);
            });
        });
      },
      /* 更新visible */
      updateVisible(value) {
        this.$emit('update:visible', value);
      },
      /* 角色选择回调*/
      roleSelectCallback(value) {
        this.form.role_type = value.type;
      }
    },
    watch: {
      async visible(visible) {
        console.log(this.data)
        let a = await getUserFinances(this.data)
        this.form1 = a.result
        if (visible) {
          let data = this.data;
          if (this.data) {
            this.$util.assignObject(this.form, {
              ...data,
            });
            this.isUpdate = true;
          } else {
            this.isUpdate = false;
          }
        } else {
          this.$refs['form'].clearValidate();
          this.form = { ...DEFAULT_FORM };
        }
      }
    }
  };
</script>
