<!-- 编辑弹窗 -->
<template>
  <ele-modal
    width="840px"
    :visible="visible"
    :close-on-click-modal="false"
    custom-class="ele-dialog-form"
    :title="$t('system.user.userCreate.title')"
    @update:visible="updateVisible"
  >
    <el-form ref="form" :model="form" :rules="rules" label-width="140px">
      <el-row :gutter="15">
        <el-col :sm="12">
          <el-form-item
            :label="`${$t('system.user.username')}`"
            prop="name"
          >
            <el-input
              clearable
              :maxlength="60"
              :minlength="16"
              type="text"
              v-model="form.name"
              :disabled="isUpdate"
            />
          </el-form-item>
          <el-form-item
            :label="this.$t('system.user.role') + ':'"
            prop="role_id"
          >
            <role-select
              v-model="form.role_id"
              @callback="roleSelectCallback"
            />
          </el-form-item>
          <!--  货主信息修改  -->
        </el-col>
        <el-col :sm="12">
          <el-form-item
            :label="this.$t('system.user.password') + ':'"
            prop="password"
          >
            <el-input
              show-password
              :maxlength="20"
              onkeyup="this.value=this.value.replace(/(^\s*)|(\s*$)/g,'')"
              v-model="form.password"
            />
          </el-form-item>
          <el-form-item
            :label="this.$t('system.user.cpassword') + ':'"
            prop="password_confirm"
          >
            <el-input
              show-password
              :maxlength="20"
              onkeyup="this.value=this.value.replace(/(^\s*)|(\s*$)/g,'')"
              v-model="form.password_confirm"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="15">
        <el-col :sm="12">
          <el-form-item
            :label="this.$t('system.user.nickname') + ':'"
            prop="nickname"
          >
            <el-input clearable :maxlength="20" v-model="form.nickname" />
          </el-form-item>
          <el-form-item
            :label="this.$t('system.user.cmrShow') + ':'"
            prop="type"
          >
            <el-select v-model="form.cmr_show" class="ele-fluid">
              <el-option
                :label="this.$t('system.user.cmrShows.all')"
                :value="1"
              />
              <el-option
                :label="this.$t('system.user.cmrShows.showSelf')"
                :value="2"
              />
              <el-option
                :label="this.$t('system.user.cmrShows.showDispatch')"
                :value="3"
              />
              <el-option
                :label="this.$t('system.user.cmrShows.no')"
                :value="4"
              />
            </el-select>
          </el-form-item>
          <el-form-item :label="this.$t('system.user.isOut') + ':'" prop="type">
            <el-select v-model="form.is_out" class="ele-fluid">
              <el-option :label="this.$t('basics.no')" :value="1" />
              <el-option :label="this.$t('basics.yes')" :value="2" />
            </el-select>
          </el-form-item>
          <el-form-item :label="this.$t('system.user.isRevoke') + ':'" prop="type">
            <el-select v-model="form.is_revoke" class="ele-fluid">
              <el-option :label="this.$t('basics.no')" :value="1" />
              <el-option :label="this.$t('basics.yes')" :value="2" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :sm="12">
          <el-form-item :label="this.$t('system.user.mail') + ':'" prop="email">
            <el-input clearable :maxlength="60" v-model="form.email" />
          </el-form-item>
          <el-form-item
            :label="this.$t('system.user.pdaShow') + ':'"
            prop="type"
          >
            <el-select v-model="form.is_show_all" class="ele-fluid">
              <el-option
                :label="this.$t('system.user.pdaShows.all')"
                :value="1"
              />
              <el-option
                :label="this.$t('system.user.pdaShows.onlyCreate')"
                :value="2"
              />
            </el-select>
          </el-form-item>
          <el-form-item
            :label="this.$t('system.user.isAddBatch') + ':'"
            prop="type"
          >
            <el-select v-model="form.is_add_batch" class="ele-fluid">
              <el-option :label="this.$t('basics.no')" :value="1" />
              <el-option :label="this.$t('basics.yes')" :value="2" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <div style="margin: 6px 0 28px 0" v-if="form.role_type === '2'">
        <el-divider />
      </div>
    </el-form>
    <div slot="footer">
      <el-button @click="updateVisible(false)">
        {{ $t('basics.cancel') }}
      </el-button>
      <el-button type="primary" :loading="loading" @click="save">
        {{ $t('basics.save') }}
      </el-button>
    </div>
  </ele-modal>
</template>

<script>
  import RoleSelect from './role-select';
  import { addUser, updateUser } from '@/api/system/user';

  const DEFAULT_FORM = {
    id: null,
    nickname: '',
    name: '',
    email: '',
    password: '',
    password_confirm: '',
    role_id: null,
    role: '',
    cmr_show: 1,
    is_show_all: 1,
    is_out: 1,
    is_add_batch: 1,
    is_revoke: 1
  };

  export default {
    name: 'UserEdit',
    components: { RoleSelect },
    props: {
      // 弹窗是否打开
      visible: Boolean,
      // 修改回显的数据
      data: Object
    },
    data() {
      return {
        // 表单数据
        form: { ...DEFAULT_FORM },
        // 表单验证规则
        rules: {
          name: [
            {
              required: true,
              message: this.$t('basics.pleaseInput'),
              trigger: 'blur'
            }
          ],
          nickname: [
            {
              required: true,
              message: this.$t('basics.pleaseInput'),
              trigger: 'blur'
            }
          ],
          role_id: [
            {
              required: true,
              message: this.$t('basics.pleaseChoose'),
              trigger: 'blur'
            }
          ],
          password: [
            {
              required: true,
              pattern: /^[\S]{6,18}$/,
              message: this.$t('system.user.tips.t1'),
              trigger: 'blur'
            }
          ],
          password_confirm: [
            {
              required: true,
              trigger: 'blur',
              validator: (rule, value, callback) => {
                if (!value) {
                  return callback(new Error(this.$t('system.user.tips.t2')));
                }
                if (value !== this.form.password) {
                  return callback(new Error(this.$t('system.user.tips.t3')));
                }
                callback();
              }
            }
          ]
        },
        // 提交状态
        loading: false,
        // 是否是修改
        isUpdate: false
      };
    },
    methods: {
      /* 保存编辑 */
      save() {
        this.$refs['form'].validate((valid) => {
          if (!valid) {
            return false;
          }
          this.loading = true;
          const data = {
            ...this.form
          };
          const saveOrUpdate = this.isUpdate ? updateUser : addUser;
          saveOrUpdate(data)
            .then(() => {
              this.loading = false;
              this.updateVisible(false);
              this.$message.success(this.$t('basics.success'));
              this.$emit('done');
            })
            .catch((e) => {
              this.loading = false;
              this.$message.error(e);
            });
        });
      },
      /* 更新visible */
      updateVisible(value) {
        this.$emit('update:visible', value);
      },
      /* 角色选择回调*/
      roleSelectCallback(value) {
        this.form.role_type = value.type;
      }
    },
    watch: {
      async visible(visible) {
        console.log(this.data);
        if (visible) {
          let data = this.data;
          if (this.data) {
            this.$util.assignObject(this.form, {
              ...data
            });
            this.isUpdate = true;
          } else {
            this.isUpdate = false;
          }
        } else {
          this.$refs['form'].clearValidate();
          this.form = { ...DEFAULT_FORM };
        }
      }
    }
  };
</script>
