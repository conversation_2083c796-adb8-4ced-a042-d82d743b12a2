<template>
  <div class="ele-body">
    <el-card shadow="never">
      <!-- 搜索表单 -->
      <user-search @search="reload" />
      <!-- 数据表格 -->
      <ele-pro-table
        ref="table"
        :columns="columns()"
        :datasource="datasource"
        :selection.sync="selection"
      >
        <!--        <template slot="empty">-->
        <!--          <ele-empty :text="$t('basics.empty_text')" />-->
        <!--        </template>-->
        <!-- 表头工具栏 -->
        <table-slot-empty slot="empty" />
        <template slot="toolbar">
          <el-button
            v-permission="'user:create'"
            size="small"
            type="primary"
            icon="el-icon-plus"
            class="ele-btn-icon"
            @click="openCreate()"
          >
            {{ $t('basics.create') }}
          </el-button>
          <!-- <el-button
            size="small"
            type="danger"
            icon="el-icon-delete"
            class="ele-btn-icon"
            @click="removeBatch"
          >
            {{ $t('basics.delete') }}
          </el-button> -->
          <!-- <el-button
            size="small"
            icon="el-icon-upload2"
            class="ele-btn-icon"
            @click="openImport"
          >
            {{ $t('basics.import') }}
          </el-button> -->
        </template>
        <!-- 用户名列 -->
        <template slot="nickname" slot-scope="{ row }">
          <router-link :to="'/system/user-info?id=' + row.userId">
            {{ row.nickname }}
          </router-link>
        </template>
        <!-- 角色列 -->
        <template slot="roles" slot-scope="{ row }">
          <el-tag
            v-for="item in row.roles"
            :key="item.roleId"
            size="mini"
            type="primary"
            :disable-transitions="true"
          >
            {{ item.roleName }}
          </el-tag>
        </template>
        <!-- 解析数据 -->
        <template slot="status" slot-scope="{ row }">
          <el-switch
            :active-value="2"
            :inactive-value="1"
            v-model="row.status"
            @change="editStatus(row)"
          />
        </template>
        <!-- 操作列 -->
        <template slot="action" slot-scope="{ row }">
          <el-link
            v-permission="'user:update'"
            type="primary"
            :underline="false"
            @click="openEdit(row)"
          >
            {{$t('basics.edit')}}
          </el-link>
          <el-link
            v-permission="'user:get_finance'"
            type="primary"
            :underline="false"
            @click="openFinanceEdit(row)"
          >
            {{$t('basics.financeSetting')}}
          </el-link>
          <el-link
            type="primary"
            :underline="false"
            @click="resetPsw(row)"
          >
            {{$t('system.user.editPass')}}
          </el-link>
          <!-- <el-link
            v-permission="'users:resetPassword'"
            type="primary"
            :underline="false"
            @click="resetPsw(row)"
          >
            修改密码
          </el-link> -->
          <!-- <el-popconfirm
            class="ele-action"
            title="确定要删除此用户吗？"
            @confirm="remove(row)"
          >
            <el-link type="danger" slot="reference" :underline="false">
              删除
            </el-link>
          </el-popconfirm> -->
        </template>
      </ele-pro-table>
    </el-card>
    <!-- 编辑弹窗 -->
    <user-edit :visible.sync="showEdit" :data="current" @done="reload" />
    <finance-edit :visible.sync="showFinanceEdit" :data="current" @done="reload" />
    <user-create :visible.sync="showCreate" :data="current" @done="reload" />
    <!-- 导入弹窗 -->
    <user-import :visible.sync="showImport" @done="reload" />
    <!-- 导入弹窗 -->
    <user-change-password
      :visible.sync="showChangePassword"
      :data="current"
      @done="reload"
    />
  </div>
</template>

<script>
  import UserSearch from './components/user-search';
  import FinanceEdit from './components/finance-edit';
  import UserEdit from './components/user-edit';
  import UserCreate from './components/user-create';
  import UserImport from './components/user-import';
  import UserChangePassword from './components/user-change-password';
  import {
    pageUsers,
    removeUser,
    removeUsers,
    updateUserStatus
  } from '@/api/system/user';
  import TableSlotEmpty from '@/components/TableSlot/empty';
  export default {
    name: 'SystemUser',
    components: {
      TableSlotEmpty,
      UserSearch,
      UserEdit,
      FinanceEdit,
      UserCreate,
      UserImport,
      UserChangePassword
    },
    data() {
      return {
        // 表格列配置
        columnsData: [
          {
            columnKey: 'index',
            type: 'index',
            width: 45,
            align: 'center',
            showOverflowTooltip: true,
            fixed: 'left'
          },
          {
            prop: 'nickname',
            label: '用户昵称',
            sortable: 'custom',
            showOverflowTooltip: true,
            minWidth: 110
          },
          {
            prop: 'name',
            label: '用户账号',
            sortable: 'custom',
            showOverflowTooltip: true,
            minWidth: 110
          },
          {
            prop: 'role',
            label: '角色',
            showOverflowTooltip: true,
            minWidth: 110
          },
          {
            //1 超管 2货主 3子货主 4财务 5 PDA 6 仓库
            prop: 'cmr_show',
            label: this.$t('system.user.cmrShow'),
            showOverflowTooltip: true,
            minWidth: 110,
            formatter: (row, column, cellValue) => {
              return [ '', this.$t('system.user.cmrShows.all'), this.$t('system.user.cmrShows.showSelf'), this.$t('system.user.cmrShows.showDispatch'), this.$t('system.user.cmrShows.no') ][
                cellValue
              ];
            }
          },
          {
            prop: 'is_show_all',
            label: this.$t('system.user.pdaShow'),
            showOverflowTooltip: true,
            minWidth: 110,
            formatter: (row, column, cellValue) => {
              return [ '', this.$t('system.user.pdaShows.all'), this.$t('system.user.pdaShows.onlyCreate') ][
                cellValue
                ];
            }
          },
          {
            prop: 'created_at',
            label: this.$t('basics.createTime'),
            showOverflowTooltip: true,
            minWidth: 110
          },
          // {
          //   prop: 'date_update',
          //   label: '更新时间',
          //   sortable: 'custom',
          //   showOverflowTooltip: true,
          //   minWidth: 110,
          //   formatter: (row, column, cellValue) => {
          //     return this.$util.toDateString(parseInt(cellValue));
          //   }
          // },
          {
            prop: 'status',
            label: this.$t('basics.status'),
            align: 'center',
            sortable: 'custom',
            width: 100,
            resizable: false,
            slot: 'status'
          },
          {
            columnKey: 'action',
            label: this.$t('basics.action'),
            width: 220,
            align: 'center',
            resizable: false,
            slot: 'action',
            hideInSetting: true
          }
        ],
        // 表格选中数据
        selection: [],
        // 当前编辑数据
        current: null,
        // 是否显示编辑弹窗
        showEdit: false,
        showFinanceEdit: false,
        showCreate: false,
        // 是否显示导入弹窗
        showImport: false,
        // 是否显修改密码
        userId: null,
        showChangePassword: false
      };
    },
    methods: {
      columns() {
        return this.handleListName(this.columnsData, 'system.user');
      },
      /* 表格数据源 */
      datasource({ page, limit, where, order }) {
        return pageUsers({
          ...where,
          ...order,
          page: page,
          num: limit
        });
      },
      /* 刷新表格 */
      reload(where) {
        this.$refs.table.reload({ page: 1, where: where });
      },
      /* 打开编辑弹窗 */
      openEdit(row) {
        this.current = row;
        this.showEdit = true;
      },
      openFinanceEdit(row) {
        this.current = row;
        this.showFinanceEdit = true;
      },
      openCreate(row) {
        this.current = row;
        this.showCreate = true;
      },
      /* 打开导入弹窗 */
      openImport() {
        this.showImport = true;
      },
      /* 删除 */
      remove(row) {
        const loading = this.$loading({ lock: true });
        removeUser(row.id)
          .then((msg) => {
            loading.close();
            this.$message.success(this.$t('basics.success'));
            this.reload();
          })
          .catch((e) => {
            loading.close();
            this.$message.error(e);
          });
      },
      /* 批量删除 */
      removeBatch() {
        if (!this.selection.length) {
          this.$message.error('请至少选择一条数据');
          return;
        }
        this.$confirm('确定要删除选中的用户吗?', '提示', {
          type: 'warning'
        })
          .then(() => {
            const loading = this.$loading({ lock: true });
            removeUsers(this.selection.map((d) => d.userId))
              .then((msg) => {
                loading.close()
                this.$message.success(this.$t('basics.success'));
                this.reload();
              })
              .catch((e) => {
                loading.close();
                this.$message.error(e);
              });
          })
          .catch(() => {});
      },
      /* 重置用户密码 */
      resetPsw(row) {
        this.current = row
        this.showChangePassword = true;
      },
      /* 更改状态 */
      editStatus(row) {
        const loading = this.$loading({ lock: true });
        updateUserStatus({user_id:row.id})
          .then((msg) => {
            loading.close();
            console.log(msg)
            this.$message.success(this.$t('basics.success'))
          })
          .catch((e) => {
            loading.close();
            row.status = !row.status ? 1 : 0;
            this.$message.error(e);
          });
      }
    }
  };
</script>
