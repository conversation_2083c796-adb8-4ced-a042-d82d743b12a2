<template>
  <div>
    <div class="ele-page-header">
      <div class="ele-page-title">{{ $t('route.config._name') }}</div>
      <!-- <div class="ele-page-desc">
        表单页用于向用户收集或验证信息, 基础表单常见于数据项较少的表单场景。
      </div> -->
    </div>
    <div class="ele-body">
      <el-card shadow="never">
        <el-form
          ref="form"
          :model="form"
          :rules="rules"
          label-width="200"
          style="max-width: 700px; margin: 10px auto"
        >

          <el-form-item :label="`${$t('config.depart_time')}:`" prop="depart_time">
            <el-input v-model="form.depart_time" clearable />
          </el-form-item>
          <el-form-item :label="`${$t('config.host')}:`" prop="host">
            <el-input v-model="form.host" clearable />
          </el-form-item>
          <el-form-item :label="`${$t('config.username')}:`" prop="username">
            <el-input v-model="form.username" :rows="4" clearable />
          </el-form-item>
          <el-form-item :label="`${$t('config.password')}:`" prop="password">
            <el-input v-model="form.password" :rows="4" clearable />
          </el-form-item>
          <el-form-item :label="`${$t('config.from')}:`" prop="from">
            <el-input v-model="form.from" :rows="4" clearable />
          </el-form-item>
          <el-form-item :label="`${$t('config.from_name')}:`" prop="from_name">
            <el-input v-model="form.from_name" :rows="4" clearable />
          </el-form-item>
          <el-form-item :label="`${$t('config.addressee')}:`" prop="addressee">
            <el-input v-model="form.addressee" :rows="4" type="textarea" />
          </el-form-item>
          <el-form-item
            :label="`${$t('config.istMailRecipientsL')}:`"
            prop="addressee"
          >
            <el-input
              v-model="form.istMailRecipientsL"
              :rows="4"
              type="istMailRecipientsL"
            />
          </el-form-item>
          <el-form-item
            :label="`${$t('config.otherIstMailRecipientsL')}:`"
            prop="otherIstMailRecipientsL"
          >
            <el-input
              v-model="form.otherIstMailRecipientsL"
              :rows="4"
              type="textarea"
            />
          </el-form-item>
          <el-form-item
            :label="`${$t('config.fraRecipientMailbox')}:`"
            prop="fraRecipientMailbox"
          >
            <el-input
              v-model="form.fraRecipientMailbox"
              :rows="4"
              type="textarea"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" :loading="loading" @click="submit">
              {{ $t('basics.save') }}
            </el-button>
          </el-form-item>
        </el-form>
      </el-card>
    </div>
  </div>
</template>

<script>
  import { getConfig, setConfig } from '@/api/views';
  const form = {
    host: null, //邮件地址
    username: null, //邮件用户名
    password: null, //邮件密码
    from: null, //发件人(回复通知邮箱)
    from_name: null, //发件人用户名
    addressee: null, //导入/删除提单收件人
    istMailRecipientsL: null, //ist邮件收件人
    otherIstMailRecipientsL: null, //其他ist收件人
    fraRecipientMailbox: null // “航班港口”为FRA收件人邮箱：
  };
  //   “航班港口”为FRA收件人邮箱：
  export default {
    name: 'FormBasic',
    data() {
      return {
        // 提交状态
        loading: false,
        // 表单数据
        form: { ...form },
        // 表单验证规则
        rules: {
          depart_time: [
            {
              required: true,
              message: this.$t('basics.pleaseInput'),
              trigger: 'blur'
            }
          ],
          host: [
            {
              required: true,
              message: this.$t('basics.pleaseInput'),
              trigger: 'blur'
            }
          ],
          username: [
            {
              required: true,
              message: this.$t('basics.pleaseInput'),
              trigger: 'blur'
            }
          ],
          password: [
            {
              required: true,
              message: this.$t('basics.pleaseInput'),
              trigger: 'blur'
            }
          ],
          from: [
            {
              required: true,
              message: this.$t('basics.pleaseInput'),
              trigger: 'blur'
            }
          ],
          from_name: [
            {
              required: true,
              message: this.$t('basics.pleaseInput'),
              trigger: 'blur'
            }
          ],
          addressee: [
            {
              required: true,
              message: this.$t('basics.pleaseInput'),
              trigger: 'blur'
            }
          ],
          istMailRecipientsL: [
            {
              required: true,
              message: this.$t('basics.pleaseInput'),
              trigger: 'blur'
            }
          ],
          otherIstMailRecipientsL: [
            {
              required: true,
              message: this.$t('basics.pleaseInput'),
              trigger: 'blur'
            }
          ],
          fraRecipientMailbox: [
            {
              required: true,
              message: this.$t('basics.pleaseInput'),
              trigger: 'blur'
            }
          ]
        }
      };
    },
    async mounted() {
      const config = await getConfig();
      this.form = config.content;
    },
    methods: {
      /* 提交 */
      submit() {
        this.$refs['form'].validate((valid) => {
          if (valid) {
            this.loading = true;
            setConfig({
              key: 'mail',
              data: this.form
            }).then(() => {
              this.loading = false;
              this.$message.success(this.$t('basics.success'));
            });
          } else {
            return false;
          }
        });
      }
    }
  };
</script>
<style>
  .el-upload {
    width: 100% !important;
  }
</style>
