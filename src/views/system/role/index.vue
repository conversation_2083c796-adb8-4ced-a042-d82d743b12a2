<template>
  <div class="ele-body">
    <el-card shadow="never">
      <!-- 搜索表单 -->
      <role-search @search="reload" />
      <!-- 数据表格 -->
      <ele-pro-table
        ref="table"
        :columns="columns"
        :datasource="datasource"
        :selection.sync="selection"
      >
        <!-- 表头工具栏 -->
        <template slot="toolbar">
          <el-button
            v-permission="'roles:create'"
            size="small"
            type="primary"
            icon="el-icon-plus"
            class="ele-btn-icon"
            @click="openEdit()"
          >
            {{ $t('basics.create') }}
          </el-button>
        </template>
        <!-- 操作列 -->
        <template slot="action" slot-scope="{ row }">
          <el-link
            v-permission="'roles:update'"
            type="primary"
            :underline="false"
            icon="el-icon-edit"
            @click="openEdit(row)"
          >
            {{ $t('basics.edit') }}
          </el-link>
          <el-link
            v-permission="'roles:getRolePermissions'"
            type="primary"
            :underline="false"
            icon="el-icon-finished"
            @click="openAuth(row)"
          >
            {{ $t('system.role.assignPermission') }}
          </el-link>
          <!-- <el-popconfirm
            class="ele-action"
            title="确定要删除此角色吗？"
            @confirm="remove(row)"
          >
            <el-link
              type="danger"
              slot="reference"
              :underline="false"
              icon="el-icon-delete"
            >
              删除
            </el-link>
          </el-popconfirm> -->
        </template>
      </ele-pro-table>
    </el-card>
    <!-- 编辑弹窗 -->
    <role-edit :data="current" :visible.sync="showEdit" @done="reload" />
    <!-- 权限分配弹窗 -->
    <role-auth :data="current" :visible.sync="showAuth" />
  </div>
</template>

<script>
  import RoleSearch from './components/role-search';
  import RoleEdit from './components/role-edit';
  import RoleAuth from './components/role-auth';
  import { pageRoles, removeRole } from '@/api/system/role';

  export default {
    name: 'SystemRole',
    components: {
      RoleSearch,
      RoleEdit,
      RoleAuth
    },
    data() {
      return {
        // 表格列配置
        columns: [
          {
            columnKey: 'index',
            type: 'index',
            width: 45,
            showOverflowTooltip: true,
            fixed: 'left'
          },
          {
            prop: 'roleName',
            label: this.$t('system.role.roleName'),
            showOverflowTooltip: true,
            minWidth: 110
          },
          {
            prop: 'roleCode',
            label: this.$t('system.role.roleCode'),
            showOverflowTooltip: true,
            minWidth: 110
          },
          // {
          //   //1 超管 2货主 3子货主 4财务 5 PDA 6 仓库
          //   prop: 'type',
          //   label: '角色类型',
          //   showOverflowTooltip: true,
          //   minWidth: 110,
          //   formatter: (row, column, cellValue) => {
          //     return ['', '超管', '货主', '子货主', '财务', 'PDA', '仓库'][
          //       cellValue
          //     ];
          //   }
          // },
          {
            prop: 'created_at',
            label: this.$t('basics.createTime'),
            showOverflowTooltip: true,
            minWidth: 110
          },
          // {
          //   prop: 'date_update',
          //   label: '更新时间',
          //   sortable: 'custom',
          //   showOverflowTooltip: true,
          //   minWidth: 110,
          //   formatter: (row, column, cellValue) => {
          //     return this.$util.toDateString(parseInt(cellValue));
          //   }
          // },
          {
            columnKey: 'action',
            label: this.$t('basics.action'),
            width: 230,
            align: 'center',
            resizable: false,
            slot: 'action',
            hideInSetting: true
          }
        ],
        // 表格选中数据
        selection: [],
        // 当前编辑数据
        current: null,
        // 是否显示编辑弹窗
        showEdit: false,
        // 是否显示导入弹窗
        showAuth: false
      };
    },
    methods: {
      /* 表格数据源 */
      datasource({ page, limit, where, order }) {
        return pageRoles({
          ...where,
          ...order,
          page: page,
          num: limit
        });
      },
      /* 刷新表格 */
      reload(where) {
        this.$refs.table.reload({ page: 1, where: where });
      },
      /* 显示编辑 */
      openEdit(row) {
        this.current = row;
        this.showEdit = true;
      },
      /* 显示分配权限 */
      openAuth(row) {
        this.current = row;
        this.showAuth = true;
      },
      /* 删除 */
      remove(row) {
        const loading = this.$loading({ lock: true });
        removeRole({ id: row.id })
          .then(() => {
            loading.close();
            this.$message.success(this.$t('basics.success'));
            this.reload();
          })
          .catch((e) => {
            loading.close();
            this.$message.error(e);
          });
      }
    }
  };
</script>
