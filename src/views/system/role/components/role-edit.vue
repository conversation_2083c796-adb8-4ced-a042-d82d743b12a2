<!-- 角色编辑弹窗 -->
<template>
  <ele-modal
    width="460px"
    :visible="visible"
    :close-on-click-modal="false"
    :title="isUpdate ? $t('basics.edit') : $t('basics.create')"
    @update:visible="updateVisible"
  >
    <el-form ref="form" :model="form" :rules="rules" label-width="100px">
      <el-form-item :label="`${$t('system.role.roleName')}:`" prop="roleName">
        <el-input
          clearable
          :maxlength="20"
          v-model="form.roleName"
          :placeholder="$t('system.role.roleNamePlaceholder')"
        />
      </el-form-item>
      <el-form-item :label="`${$t('system.role.roleCode')}:`" prop="roleCode">
        <el-input
          clearable
          :maxlength="20"
          :disabled="isUpdate"
          v-model="form.roleCode"
          :placeholder="$t('system.role.roleCodePlaceholder')"
        />
      </el-form-item>
    </el-form>
    <div slot="footer">
      <el-button @click="updateVisible(false)">
        {{ $t('basics.cancel') }}
      </el-button>
      <el-button type="primary" :loading="loading" @click="save">
        {{ $t('basics.save') }}
      </el-button>
    </div>
  </ele-modal>
</template>

<script>
  import { addRole, updateRole } from '@/api/system/role';
  const DEFAULT_FORM = {
    id: null,
    roleName: '',
    roleCode: ''
  };

  export default {
    name: 'RoleEdit',
    props: {
      // 弹窗是否打开
      visible: Boolean,
      // 修改回显的数据
      data: Object
    },
    data() {
      return {
        // 表单数据
        form: { ...DEFAULT_FORM },
        // 表单验证规则
        rules: {
          roleName: [
            {
              required: true,
              message: this.$t('system.role.roleNamePlaceholder'),
              trigger: 'blur'
            }
          ],
          roleCode: [
            {
              required: true,
              message: this.$t('system.role.roleCodePlaceholder'),
              trigger: 'blur'
            }
          ],
          type: [
            {
              required: true,
              message: this.$t('system.role.roleCodePlaceholder'),
              trigger: 'blur'
            }
          ]
        },
        // 提交状态
        loading: false,
        // 是否是修改
        isUpdate: false
      };
    },
    methods: {
      /* 保存编辑 */
      save() {
        this.$refs['form'].validate((valid) => {
          if (!valid) {
            return false;
          }
          this.loading = true;
          const saveOrUpdate = this.isUpdate ? updateRole : addRole;
          saveOrUpdate(this.form)
            .then((msg) => {
              this.loading = false;
              this.$message.success(this.$t('basics.success'));
              this.updateVisible(false);
              this.$emit('done');
            })
            .catch((e) => {
              this.loading = false;
              this.$message.error(e);
            });
        });
      },
      /* 更新visible */
      updateVisible(value) {
        this.$emit('update:visible', value);
      }
    },
    watch: {
      visible(visible) {
        if (visible) {
          if (this.data) {
            this.$util.assignObject(this.form, this.data);
            this.isUpdate = true;
          } else {
            this.isUpdate = false;
          }
        } else {
          this.$refs['form'].clearValidate();
          this.form = { ...DEFAULT_FORM };
        }
      }
    }
  };
</script>
