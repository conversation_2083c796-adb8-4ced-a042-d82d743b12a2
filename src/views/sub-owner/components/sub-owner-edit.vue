<!-- 编辑弹窗 -->
<template>
  <ele-modal
    width="460px"
    :visible="visible"
    :close-on-click-modal="false"
    :title="isUpdate ? '修改角色' : '添加角色'"
    @update:visible="updateVisible"
  >
    <el-form ref="form" :model="form" :rules="rules" label-width="82px">
      <el-form-item label="用户账号:" prop="username">
        <el-input
          clearable
          :maxlength="20"
          v-model="form.username"
          placeholder="请输入用户账号"
        />
      </el-form-item>
      <el-form-item label="登录密码:" prop="password">
        <el-input
          show-password
          :maxlength="20"
          v-model="form.password"
          placeholder="请输入登录密码"
        />
      </el-form-item>
      <el-form-item v-if="!isUpdate" label="确认密码:" prop="password_confirm">
        <el-input
          show-password
          :maxlength="20"
          v-model="form.password_confirm"
          placeholder="请输入登录密码"
        />
      </el-form-item>
    </el-form>
    <div slot="footer">
      <el-button @click="updateVisible(false)">取消</el-button>
      <el-button type="primary" @click="save" :loading="loading">
        保存
      </el-button>
    </div>
  </ele-modal>
</template>

<script>
  import { create } from '@/api/sub-owner';

  const DEFAULT_FORM = {
    id: null,
    username: '',
    password: '',
    password_confirm: ''
  };

  export default {
    name: 'sub-owner-edit',
    props: {
      // 弹窗是否打开
      visible: Boolean,
      // 修改回显的数据
      data: Object
    },
    data() {
      return {
        // 表单数据
        form: { ...DEFAULT_FORM },
        // 表单验证规则
        rules: {
          username: [
            {
              required: true,
              message: '请输入用户账号',
              trigger: 'blur'
            }
          ],
          password: [
            {
              required: true,
              pattern: /^[\S]{5,18}$/,
              message: '密码必须为5-18位非空白字符',
              trigger: 'blur'
            }
          ],
          password_confirm: [
            {
              required: true,
              trigger: 'blur',
              validator: (rule, value, callback) => {
                if (!value) {
                  return callback(new Error('请输入确认密码'));
                }
                if (value !== this.form.password) {
                  return callback(new Error('两次属于的密码不一致'));
                }
                callback();
              }
            }
          ]
        },
        // 提交状态
        loading: false,
        // 是否是修改
        isUpdate: false
      };
    },
    methods: {
      /* 保存编辑 */
      save() {
        this.$refs['form'].validate((valid) => {
          if (!valid) {
            return false;
          }
          this.loading = true;
          const data = {
            ...this.form
          };
          create(data)
            .then((msg) => {
              this.loading = false;
              this.$message.success(this.$t('basics.success'));
              this.updateVisible(false);
              this.$emit('done');
            })
            .catch((e) => {
              this.loading = false;
              this.$message.error(e);
            });
        });
      },
      /* 更新visible */
      updateVisible(value) {
        this.$emit('update:visible', value);
      },
      /* 角色选择回调*/
      roleSelectCallback(value) {
        this.form.role_type = value.type;
      }
    },
    watch: {
      visible(visible) {
        if (visible) {
          if (this.data) {
            this.$util.assignObject(this.form, {
              ...this.data,
              password: ''
            });
            this.isUpdate = true;
          } else {
            this.isUpdate = false;
          }
        } else {
          this.$refs['form'].clearValidate();
          this.form = { ...DEFAULT_FORM };
        }
      }
    }
  };
</script>
