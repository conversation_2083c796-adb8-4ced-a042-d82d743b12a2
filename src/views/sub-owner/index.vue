<template>
  <div class="ele-body">
    <el-card shadow="never">
      <!-- 搜索表单 -->
      <sub-owner-search @search="reload" />
      <!-- 数据表格 -->
      <ele-pro-table
        ref="table"
        :columns="columns"
        :datasource="datasource"
        :selection.sync="selection"
      >
        <!-- 表头工具栏 -->
        <template slot="toolbar">
          <el-button
            size="small"
            type="primary"
            icon="el-icon-plus"
            class="ele-btn-icon"
            @click="openEdit()"
          >
            新建
          </el-button>
        </template>
        <!-- 状态列 -->
        <template slot="status" slot-scope="{ row }">
          <el-switch
            :active-value="0"
            :inactive-value="1"
            v-model="row.status"
            @change="editStatus(row)"
          />
        </template>
        <!-- 操作列 -->
        <template slot="action" slot-scope="{ row }">
          <el-link
            v-permission="'users:resetPassword'"
            type="primary"
            :underline="false"
            @click="resetPsw(row)"
          >
            修改密码
          </el-link>
          <el-popconfirm
            class="ele-action"
            title="确定要删除此用户吗？"
            @confirm="remove(row)"
          >
            <el-link type="danger" slot="reference" :underline="false">
              删除
            </el-link>
          </el-popconfirm>
        </template>
      </ele-pro-table>
    </el-card>
    <!-- 编辑弹窗 -->
    <sub-owner-edit :visible.sync="showEdit" :data="current" @done="reload" />
    <!-- 修改密码 -->
    <sub-owner-password
      :visible.sync="showChangePassword"
      :user-id="userId"
      @done="reload"
    />
  </div>
</template>

<script>
  import { list, SubOwnerDelete } from '@/api/sub-owner';
  import SubOwnerEdit from '@/views/sub-owner/components/sub-owner-edit';
  import SubOwnerSearch from '@/views/sub-owner/components/sub-owner-search';
  import SubOwnerPassword from '@/views/sub-owner/components/sub-owner-password';

  export default {
    name: 'sub-owner',
    components: {
      SubOwnerPassword,
      SubOwnerSearch,
      SubOwnerEdit
    },
    data() {
      return {
        // 表格列配置
        columns: [
          {
            columnKey: 'index',
            type: 'index',
            width: 45,
            align: 'center',
            showOverflowTooltip: true,
            fixed: 'left'
          },
          {
            prop: 'username',
            label: '用户账号',
            sortable: 'custom',
            showOverflowTooltip: true,
            minWidth: 110
          },
          {
            prop: 'date_add',
            label: this.$t('basics.status'),
            sortable: 'custom',
            showOverflowTooltip: true,
            minWidth: 110,
            formatter: (row, column, cellValue) => {
              return this.$util.toDateString(parseInt(cellValue));
            }
          },
          {
            prop: 'date_update',
            label: this.$t('basics.updateTime'),
            sortable: 'custom',
            showOverflowTooltip: true,
            minWidth: 110,
            formatter: (row, column, cellValue) => {
              return this.$util.toDateString(parseInt(cellValue));
            }
          },
          // {
          //   prop: 'status',
          //   label: '状态',
          //   align: 'center',
          //   sortable: 'custom',
          //   width: 80,
          //   resizable: false,
          //   slot: 'status'
          // },
          {
            columnKey: 'action',
            label: this.$t('basics.action'),
            width: 220,
            align: 'center',
            resizable: false,
            slot: 'action',
            hideInSetting: true
          }
        ],
        // 表格选中数据
        selection: [],
        // 当前编辑数据
        current: null,
        // 是否显示编辑弹窗
        showEdit: false,
        // 是否显示导入弹窗
        showImport: false,
        // 是否显修改密码
        userId: null,
        showChangePassword: false
      };
    },
    methods: {
      /* 表格数据源 */
      datasource({ page, limit, where, order }) {
        return list({
          ...where,
          ...order,
          cur_page: page,
          per_page: limit
        });
      },
      /* 刷新表格 */
      reload(where) {
        this.$refs.table.reload({ page: 1, where: where });
      },
      /* 打开编辑弹窗 */
      openEdit(row) {
        this.current = row;
        this.showEdit = true;
      },
      /* 打开导入弹窗 */
      openImport() {
        this.showImport = true;
      },
      /* 删除 */
      remove(row) {
        const loading = this.$loading({ lock: true });
        SubOwnerDelete(row.id)
          .then((msg) => {
            loading.close();
            this.$message.success(this.$t('basics.success'));
            this.reload();
          })
          .catch((e) => {
            loading.close();
            this.$message.error(e);
          });
      },
      /* 重置用户密码 */
      resetPsw(row) {
        this.userId = parseInt(row.id);
        this.showChangePassword = true;
      }
    }
  };
</script>
