<template>
  <div class="ele-body">
    <el-card shadow="never">
      <!-- 搜索表单 -->
      <search @search="reload" />
      <!-- 数据表格 -->
      <ele-pro-table
        ref="table"
        :initLoad="false"
        :columns="columns"
        :datasource="datasource"
        :selection.sync="selection"
      >
        <!-- 表头工具栏 -->
        <template slot="toolbar">
          <el-button
            v-permission="'abnormalHsCode:create'"
            size="small"
            type="primary"
            icon="el-icon-plus"
            class="ele-btn-icon"
            @click="openEdit()"
          >
            {{ $t('basics.create') }}
          </el-button>
        </template>
        <!-- 操作列 -->
        <template slot="action" slot-scope="{ row }">
          <el-link
            @click="openEdit(row)"
            v-permission="'abnormalHsCode:update'"
            type="primary"
            icon="el-icon-edit"
            :underline="false"
          >
            {{ $t('basics.edit') }}
          </el-link>
          <el-popconfirm
            class="ele-action"
            :title="$t('basics.confirmDel')"
            v-permission="'abnormalHsCode:delete'"
            @confirm="remove(row)"
            icon="el-icon-delete"
          >
            <el-link
              type="danger"
              slot="reference"
              icon="el-icon-delete"
              :underline="false"
            >
              {{ $t('basics.delete') }}
            </el-link>
          </el-popconfirm>
        </template>
      </ele-pro-table>
    </el-card>
    <address-edit :visible.sync="showEdit" :data="current" @done="reload" />
  </div>
</template>

<script>
  import AddressEdit from './components/edit';
  import search from './components/search';
  import { destroy, lists } from '@/api/AbnormalHsCode';

  export default {
    name: 'SystemUser',
    components: {
      AddressEdit,
      search
    },
    data() {
      return {
        // 表格列配置
        columns: [
          {
            columnKey: 'index',
            type: 'index',
            width: 45,
            showOverflowTooltip: true,
            fixed: 'left'
          },
          {
            prop: 'nomenclature',
            label: this.$t('order.abnormalHsCode.nomenclature'),
            showOverflowTooltip: true,
            minWidth: 110
          },
          {
            prop: 'remarks',
            label: this.$t('order.abnormalHsCode.remarks'),
            showOverflowTooltip: true,
            minWidth: 110
          },
          {
            prop: 'created_at',
            label: this.$t('basics.createTime'),
            showOverflowTooltip: true,
            minWidth: 110
          },
          {
            columnKey: 'action',
            noRole: 'sonOwner', //什么角色下不显示
            label: this.$t('basics.action'),
            width: 220,
            align: 'center',
            resizable: false,
            slot: 'action',
            hideInSetting: true,
            fixed: 'right'
          }
        ],
        // 表格选中数据
        selection: [],
        // 当前编辑数据
        current: null,
        // 是否显示编辑弹窗
        showEdit: false,
        showDetail: false,
        showFinanceEdit: false,
        // 是否显示导入弹窗
        showImport: false,
        // 是否显修改密码
        userId: null,
        showChangePassword: false
      };
    },
    activated() {
      this.reload();
    },
    methods: {
      /* 表格数据源 */
      datasource({ page, limit, where, order }) {
        return lists({
          ...where,
          ...order,
          page: page,
          num: limit
        });
      },
      /* 刷新表格 */
      reload(where) {
        this.$refs.table.reload({ page: 1, where: where });
      },
      /* 打开编辑弹窗 */
      openEdit(row) {
        this.current = row;
        this.showEdit = true;
      },
      openFinanceEdit(row) {
        this.current = row;
        this.showFinanceEdit = true;
      },
      openDetail(row) {
        this.current = row;
        this.showDetail = true;
      },
      /* 打开导入弹窗 */
      openImport() {
        this.showImport = true;
      },
      /* 删除 */
      remove(row) {
        const loading = this.$loading({ lock: true });
        destroy(row.id)
          .then(() => {
            loading.close();
            this.$message.success(this.$t('basics.success'));
            this.reload();
          })
          .catch((e) => {
            loading.close();
            this.$message.error(e);
          });
      }
    }
  };
</script>
