<!-- 编辑弹窗 -->
<template>
  <ele-modal
    width="840px"
    :visible="visible"
    :close-on-click-modal="false"
    custom-class="ele-dialog-form"
    :title="isUpdate ? this.$t('basics.edit') : this.$t('basics.create')"
    @update:visible="updateVisible"
  >
    <el-form ref="form" :model="form" :rules="rules" label-width="155px">
      <el-row :gutter="15">
        <el-col :sm="12">
          <el-form-item
            :label="$t('order.abnormalHsCode.nomenclature') + ':'"
            prop="nomenclature"
          >
            <el-input
              v-model="form.nomenclature"
              show-word-limit
              type="text"
              :maxlength="20"
            />
          </el-form-item>
        </el-col>
        <!--        <el-col :sm="12">-->
        <!--          <el-form-item-->
        <!--            :label="$t('AbnormalHsCode.excluded_for_ni_on_code') + ':'"-->
        <!--            prop="excluded_for_ni_on_code"-->
        <!--          >-->
        <!--            <el-input v-model="form.excluded_for_nion_code" />-->
        <!--          </el-form-item>-->
        <!--        </el-col>-->
        <el-col :sm="12">
          <el-form-item
            :label="$t('order.abnormalHsCode.remarks') + ':'"
            prop="remarks"
          >
            <el-input v-model="form.remarks" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div slot="footer">
      <el-button @click="updateVisible(false)">
        {{ $t('basics.cancel') }}
      </el-button>
      <el-button type="primary" :loading="loading" @click="save">
        {{ $t('basics.save') }}
      </el-button>
    </div>
  </ele-modal>
</template>

<script>
  import { update, create } from '@/api/AbnormalHsCode';

  const DEFAULT_FORM = {
    id: null,
    nomenclature: null,
    excluded_for_nion_code: null,
    remarks: ''
  };

  export default {
    name: 'UserEdit',
    props: {
      // 弹窗是否打开
      visible: Boolean,
      // 修改回显的数据
      data: Object
    },
    data() {
      return {
        // 表单数据
        form: { ...DEFAULT_FORM },
        // 表单验证规则
        rules: {
          nomenclature: [
            {
              required: true,
              message: this.$t('basics.pleaseChoose'),
              trigger: 'blur'
            }
          ]
        },
        // 提交状态
        loading: false,
        // 是否是修改
        isUpdate: false
      };
    },
    methods: {
      /* 保存编辑 */
      save() {
        this.$refs['form'].validate((valid) => {
          if (!valid) {
            return false;
          }
          this.loading = true;
          const data = {
            ...this.form
          };
          (this.isUpdate ? update(data) : create(data))
            .then(() => {
              this.loading = false;
              this.$message.success(this.$t('basics.success'));
              this.updateVisible(false);
              this.$emit('done');
            })
            .catch((e) => {
              this.loading = false;
              this.$message.error(e);
            });
        });
      },
      /* 更新visible */
      updateVisible(value) {
        this.$emit('update:visible', value);
      },
      /* 角色选择回调*/
      roleSelectCallback(value) {
        this.form.role_type = value.type;
      }
    },
    watch: {
      async visible(visible) {
        console.log(visible);
        if (visible) {
          let data = this.data;
          if (this.data) {
            this.$util.assignObject(this.form, {
              ...data
            });
            this.isUpdate = true;
          } else {
            this.isUpdate = false;
          }
        } else {
          this.$refs['form'].clearValidate();
          this.form = { ...DEFAULT_FORM };
        }
      }
    }
  };
</script>
