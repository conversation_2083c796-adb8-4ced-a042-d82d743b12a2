<!-- 编辑弹窗 -->
<template>
  <ele-modal
    width="720px"
    :visible="visible"
    :close-on-click-modal="false"
    custom-class="ele-dialog-form"
    :title="isUpdate ? this.$t('basics.edit') : this.$t('basics.create')"
    @update:visible="updateVisible"
  >
    <el-form ref="form" :model="form" :rules="rules" label-width="100px">
      <el-row :gutter="15">
        <el-col :sm="12">
          <el-form-item
            :label="this.$t('platform.platform.name') + ':'"
            prop="name"
          >
            <el-input :maxlength="100" v-model="form.name" />
          </el-form-item>
        </el-col>
        <el-col :sm="12">
          <el-form-item
            :label="this.$t('platform.pickup.countdown') + ':'"
            prop="is_time"
          >
            <el-select
              v-model="form.is_time"
              :placeholder="this.$t('basics.pleaseChoose')"
              class="ele-fluid"
            >
              <el-option :label="this.$t('basics.yes')" :value="1" />
              <el-option :label="this.$t('basics.no')" :value="2" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div slot="footer">
      <el-button @click="updateVisible(false)">
        {{ $t('basics.cancel') }}
      </el-button>
      <el-button type="primary" :loading="loading" @click="save">
        {{ $t('basics.save') }}
      </el-button>
    </div>
  </ele-modal>
</template>

<script>
  import { update, create } from '@/api/platform/platform';

  const DEFAULT_FORM = {
    id: null,
    name: '',
    is_time: 1
  };

  export default {
    name: 'UserEdit',
    components: { },
    props: {
      // 弹窗是否打开
      visible: Boolean,
      // 修改回显的数据
      data: Object
    },
    data() {
      return {
        // 表单数据
        form: { ...DEFAULT_FORM },
        // 表单验证规则
        rules: {
          name: [
            {
              required: true,
              message: this.$t('basics.pleaseInput'),
              trigger: 'blur'
            }
          ]
        },
        // 提交状态
        loading: false,
        // 是否是修改
        isUpdate: false
      };
    },
    methods: {
      /* 保存编辑 */
      save() {
        this.$refs['form'].validate((valid) => {
          if (!valid) {
            return false;
          }
          this.loading = true;
          const data = {
            ...this.form
          };
          const apiname = this.isUpdate? update :create
          apiname(data)
            .then((msg) => {
              this.loading = false;
              this.$message.success(this.$t('basics.success'));
              this.updateVisible(false);
              this.$emit('done');
            })
            .catch((e) => {
              this.loading = false;
              this.$message.error(e);
            });
        });
      },
      /* 更新visible */
      updateVisible(value) {
        this.$emit('update:visible', value);
      },
      /* 角色选择回调*/
      roleSelectCallback(value) {
        this.form.role_type = value.type;
      }
    },
    watch: {
      async visible(visible) {
        if (visible) {
          let data = this.data;
          if (this.data) {
            this.$util.assignObject(this.form, {
              ...data
            });
            this.isUpdate = true;
          } else {
            this.isUpdate = false;
          }
        } else {
          this.$refs['form'].clearValidate();
          this.form = { ...DEFAULT_FORM };
        }
      }
    }
  };
</script>
