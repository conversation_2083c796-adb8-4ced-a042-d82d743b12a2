<template>
  <div class="ele-body">
    <el-card shadow="never">
      <!-- 搜索表单 -->
      <outbound-fba-search @search="reload" />
      <!-- 数据表格 -->
      <ele-pro-table
        ref="table"
        :initLoad="false"
        :columns="columns"
        :datasource="datasource"
        :selection.sync="selection"
        :row-class-name="setLeaveRowColor"
      >
        <template slot="toolbar">
          <el-button
            size="small"
            icon="el-icon-download"
            class="ele-btn-icon"
            @click="exportXls"
            v-permission="'seal:export'"
          >
            {{ $t('basics.export') }}
          </el-button>
        </template>

        <template slot="countdown" slot-scope="{ row }">
          <span
            class="countdown"
            :data-time="row.mate_at_time"
            v-if="!row.leave_at"
          ></span>
        </template>

        <template slot="action" slot-scope="{ row }">
          <el-link
            v-permission="'pickup:edit'"
            v-if="row.leave_at == ''"
            @click="getInfo(row)"
            type="primary"
            :underline="false"
          >
            {{ $t('basics.edit') }}
          </el-link>
          <el-link
            v-permission="'pickup:confirm'"
            v-if="
              row.leave_at == '' &&
              row.platform_id != 0 &&
              row.platform_id != 16
            "
            @click="confirm(row)"
            type="primary"
            :underline="false"
          >
            {{ $t('platform.pickup.confirm') }}
          </el-link>
          <el-link @click="detailInfo(row)" type="primary" :underline="false">
            {{ $t('basics.detail') }}
          </el-link>
          <el-popconfirm
            v-permission="'pickup:delete'"
            v-if="row.leave_at == ''"
            class="ele-action"
            :title="$t('platform.pickup.tips')"
            @confirm="cancel(row)"
          >
            <el-link type="danger" slot="reference" :underline="false">
              {{ $t('basics.delete') }}
            </el-link>
          </el-popconfirm>
          <el-link
            v-permission="'pickup:copy'"
            @click="copy(row)"
            type="primary"
            :underline="false"
          >
            {{ $t('platform.pickup.copy') }}
          </el-link>
        </template>
      </ele-pro-table>
    </el-card>
    <show-info :visible.sync="showInfo" :data="current" @done="reload" />
    <details-info :visible.sync="detailsInfo" :data="current" @done="reload" />
  </div>
</template>

<script>
  import { cancel, lists, confirm, getAll, copy } from '@/api/platform/pickup';
  import OutboundFbaSearch from './components/search';
  import ShowInfo from './components/edit';
  import DetailsInfo from './components/details';
  import { utils, writeFile } from 'xlsx';
  import { getConfig } from '@/api/views';

  export default {
    name: 'SystemUser',
    components: {
      OutboundFbaSearch,
      ShowInfo,
      DetailsInfo
    },
    data() {
      return {
        // 表格列配置
        columns: [
          {
            columnKey: 'index',
            type: 'index',
            width: 45,
            showOverflowTooltip: true,
            fixed: 'left'
          },
          {
            prop: 'create_date',
            label: this.$t('platform.pickup.create_date'),
            showOverflowTooltip: true,
            minWidth: 100
          },
          {
            prop: 'name',
            label: this.$t('platform.pickup.name'),
            showOverflowTooltip: true,
            minWidth: 65,
            slot: 'name'
          },
          {
            prop: 'car_number',
            label: this.$t('platform.pickup.car_number'),
            showOverflowTooltip: true,
            minWidth: 75
          },
          {
            prop: 'remark',
            label: this.$t('batch.car.remark'),
            showOverflowTooltip: true,
            minWidth: 100
          },
          {
            prop: 'gsname',
            label: this.$t('platform.pickup.gsname'),
            showOverflowTooltip: true,
            minWidth: 130
          },
          {
            prop: 'tel',
            label: this.$t('platform.pickup.tel'),
            showOverflowTooltip: true,
            minWidth: 80
          },
          {
            prop: 'platform_name',
            label: this.$t('platform.pickup.platform_id'),
            showOverflowTooltip: true,
            minWidth: 80
          },
          {
            prop: 'created_at',
            label: this.$t('basics.createTime'),
            showOverflowTooltip: true,
            minWidth: 100
          },
          {
            prop: 'mate_at',
            label: this.$t('platform.pickup.mate_at'),
            showOverflowTooltip: true,
            minWidth: 100
          },
          {
            prop: 'leave_at',
            label: this.$t('platform.pickup.leave_at'),
            showOverflowTooltip: true,
            minWidth: 100
          },
          {
            // prop: 'leave_at',
            label: this.$t('platform.pickup.countdown'),
            showOverflowTooltip: true,
            minWidth: 50,
            slot: 'countdown'
          },
          /* {
            prop: 'remark',
            label: this.$t('batch.car.remark'),
            showOverflowTooltip: true,
            minWidth: 100
          }, */
          {
            columnKey: 'action',
            label: this.$t('basics.action'),
            width: 170,
            align: 'center',
            resizable: false,
            slot: 'action',
            hideInSetting: true,
            fixed: 'right'
          }
        ],
        // 表格选中数据
        selection: [],
        // 当前编辑数据
        current: null,
        // 是否显示编辑弹窗
        showInfo: false,
        detailsInfo: false,
        // 是否显示导入弹窗
        showImport: false,
        // 是否显修改密码
        userId: null,
        showChangePassword: false,
        lastWhere: {},
        depart_time: 0
      };
    },
    created() {
      //或者登陆不显示货主名字其他登录显示名称
    },
    async activated() {
      this.reload();
      const config = await getConfig();
      this.depart_time = parseInt(config.content.depart_time);
      setInterval(() => {
        let countdown = document.querySelectorAll('.countdown'),
          now_time = parseInt(Date.now() / 1000);
        countdown.forEach((item) => {
          let mate_time = item.getAttribute('data-time');
          if (mate_time != '') {
            mate_time =
              parseInt(mate_time) +
              parseInt(this.depart_time > 0 ? this.depart_time * 60 : 3600);
            let countdown_int = parseInt(mate_time - now_time);
            item.innerHTML = this.formatTime(countdown_int);
            item.setAttribute(
              'style',
              'color: ' + (countdown_int >= 0 ? '#2CB897' : '#FF0000')
            );
          } else {
            item.innerHTML = '';
          }
        });
      }, 1000);
    },
    methods: {
      formatTime(time) {
        let top = '';
        if (time < 0) {
          top = '-';
          time = time * -1;
        }
        let m = parseInt(time / 60),
          s = time - m * 60;
        if (m < 10) {
          m = '0' + m;
        }
        if (s < 10) {
          s = '0' + s;
        }
        return top + m + ':' + s;
      },
      formatDate(date) {
        const day = date.getDate().toString().padStart(2, '0');
        const month = (date.getMonth() + 1).toString().padStart(2, '0');
        const year = date.getFullYear();
        const hours = date.getHours().toString().padStart(2, '0');
        const minutes = date.getMinutes().toString().padStart(2, '0');
        const seconds = date.getSeconds().toString().padStart(2, '0');

        return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
      },
      setLeaveRowColor({ row, rowIndex }) {
        let now_time = Date.now(),
          leave_time = Date.parse(row.leave_at);
        if (now_time > leave_time) {
          return 'leave-row';
        }
        return '';
      },
      /* 表格数据源 */
      datasource({ page, limit, where, order }) {
        return lists({
          ...where,
          ...order,
          page: page,
          num: limit
          // inbound_id: this.$route.query.inbound_id
        });
      },
      /* 刷新表格 */
      reload(where) {
        this.lastWhere = where;
        this.$refs.table.reload({ page: 1, where: where });
      },
      exportXls() {
        const array = [];
        array.push([
          this.$t('platform.pickup.create_date'),
          this.$t('platform.pickup.name'),
          this.$t('platform.pickup.type'),
          this.$t('platform.pickup.car_number'),
          this.$t('platform.pickup.trailer_plate'),
          this.$t('platform.pickup.gsname'),
          this.$t('platform.pickup.tel'),
          this.$t('platform.pickup.platform_id'),
          this.$t('basics.createTime'),
          this.$t('platform.pickup.mate_at'),
          this.$t('platform.pickup.leave_at'),
          this.$t('platform.pickup.address_1'),
          'CMR'
        ]);
        const types = {
          Van: this.$t('platform.pickup.types.s1'),
          Truck: this.$t('platform.pickup.types.s1')
        };
        getAll({
          ...this.lastWhere
        }).then((res) => {
          res.result.forEach((d) => {
            array.push([
              d.create_date,
              d.name,
              types[d.type],
              d.car_number,
              d.trailer_plate,
              d.gsname,
              d.tel,
              d.platform_name,
              d.created_at,
              d.mate_at,
              d.leave_at,
              d.address,
              d.cmr
            ]);
          });
          const sheetName = 'Sheet1';
          const workbook = {
            SheetNames: [sheetName],
            Sheets: {}
          };
          workbook.Sheets[sheetName] = utils.aoa_to_sheet(array);
          writeFile(workbook, 'PICKUP.xlsx');
        });
      },
      getInfo(row) {
        this.current = row;
        this.showInfo = true;
      },
      detailInfo(row) {
        console.log(row);
        this.current = row;
        this.detailsInfo = true;
      },
      /* 打开导入弹窗 */
      openImport() {
        this.showImport = true;
      },
      /* 删除 */
      cancel(row) {
        const loading = this.$loading({ lock: true });
        cancel(row.id)
          .then((msg) => {
            loading.close();
            this.$message.success(this.$t('basics.success'));
            this.reload();
          })
          .catch((e) => {
            loading.close();
            this.$message.error(e);
          });
      },
      copy(row) {
        const loading = this.$loading({ lock: true });
        copy(row)
          .then((msg) => {
            loading.close();
            this.$message.success(this.$t('basics.success'));
            this.reload();
          })
          .catch((e) => {
            loading.close();
            this.$message.error(e);
          });
      },
      /* 确认 */
      confirm(row) {
        const loading = this.$loading({ lock: true });
        confirm(row)
          .then((msg) => {
            loading.close();
            this.$message.success(this.$t('basics.success'));
            this.reload();
          })
          .catch((e) => {
            loading.close();
            this.$message.error(e);
          });
      }
    }
  };
</script>
<style>
  .ele-form-detail {
    font-size: 0;
  }
  .ele-form-detail label {
    width: 90px;
    color: #99a9bf;
  }
  .ele-form-detail .el-form-item {
    margin-right: 0;
    margin-bottom: 0;
    margin-left: 50px;
    /* width: 50%; */
  }
  .leave-row {
    background-color: #f0f0f0 !important;
  }
</style>
