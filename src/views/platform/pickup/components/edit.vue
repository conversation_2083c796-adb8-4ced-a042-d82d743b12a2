<!-- 编辑弹窗 -->
<template>
  <ele-modal
    width="1080px"
    :visible="visible"
    :close-on-click-modal="false"
    custom-class="ele-dialog-form"
    :title="this.$t('basics.edit')"
    @update:visible="updateVisible"
  >
    <el-form ref="form" :model="form" label-width="180px">
      <el-row :gutter="15">
        <el-col :sm="12">
          <el-form-item
            :label="this.$t('platform.pickup.name') + ':'"
            prop="name"
          >
            <el-input clearable show-word-limit v-model="form.name" />
          </el-form-item>
        </el-col>
        <el-col :sm="12">
          <el-form-item
            :label="this.$t('platform.pickup.type') + ':'"
            prop="type"
          >
            <el-select v-model="form.type" class="ele-fluid">
              <el-option
                :label="this.$t('platform.pickup.types.s1')"
                value="Van"
              />
              <el-option
                :label="this.$t('platform.pickup.types.s2')"
                value="Truck"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :sm="12">
          <el-form-item
            :label="this.$t('platform.pickup.car_number') + ':'"
            prop="car_number"
          >
            <el-input clearable show-word-limit v-model="form.car_number" />
          </el-form-item>
        </el-col>
        <el-col :sm="12">
          <el-form-item
            :label="this.$t('platform.pickup.trailer_plate') + ':'"
            prop="trailer_plate"
          >
            <el-input clearable show-word-limit v-model="form.trailer_plate" />
          </el-form-item>
        </el-col>
        <el-col :sm="12">
          <el-form-item
            :label="this.$t('platform.pickup.gsname') + ':'"
            prop="gsname"
          >
            <el-input clearable show-word-limit v-model="form.gsname" />
          </el-form-item>
        </el-col>
        <el-col :sm="12">
          <el-form-item
            :label="this.$t('platform.pickup.tel') + ':'"
            prop="tel"
          >
            <el-input clearable show-word-limit v-model="form.tel" />
          </el-form-item>
        </el-col>
        <el-col :sm="12">
          <el-form-item
            :label="this.$t('platform.pickup.create_date') + ':'"
            prop="create_date"
          >
            <el-input
              clearable
              show-word-limit
              v-model="form.create_date"
              :disabled="true"
            />
          </el-form-item>
        </el-col>
        <el-col :sm="12">
          <el-form-item
            :label="`${$t('platform.pickup.platform_id')}:`"
            prop="platform_id"
          >
            <platform-select v-model="form.platform_id" />
          </el-form-item>
        </el-col>
        <el-col :sm="12">
          <el-form-item
            :label="this.$t('platform.pickup.mate_at') + ':'"
            prop="tel"
          >
            <el-date-picker
              v-model="form.mate_at"
              type="datetime"
              value-format="yyyy-MM-dd HH:mm:ss"
              class="ele-fluid"
            >
            </el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :sm="12">
          <el-form-item
            :label="this.$t('platform.pickup.sort') + ':'"
            prop="tel"
          >
            <el-input clearable show-word-limit v-model="form.sort" />
          </el-form-item>
        </el-col>
        <el-col :sm="12">
          <el-form-item
            :label="this.$t('batch.car.remark') + ':'"
            prop="tel">
            <el-input clearable show-word-limit v-model="form.remark" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <ele-pro-table
      ref="table"
      :columns="columns"
      :need-page="false"
      :datasource="form.address_1"
    >
      <template slot="address" slot-scope="{ row }">
        <el-input v-model="row.address" v-if="row.type == 2" />
        <el-select
          clearable
          filterable
          v-model="row.address"
          class="ele-fluid"
          v-if="row.type == 1"
        >
          <el-option
            v-for="{ id, addr_name } in addresses"
            :key="id"
            :value="addr_name"
            :label="addr_name"
          />
        </el-select>
      </template>
      <template slot="cmr" slot-scope="{ row }">
        <el-input v-model="row.cmr" />
      </template>
      <template slot="action" slot-scope="{ $index }">
        <el-link
          type="danger"
          slot="reference"
          :underline="false"
          @click="deleteAdd($index)"
        >
          {{ $t('basics.delete') }}
        </el-link>
      </template>
    </ele-pro-table>
    <div slot="footer">
      <el-button @click="add()">
        {{ $t('platform.pickup.address_2') }}
      </el-button>
      <el-button @click="add2()">
        {{ $t('platform.pickup.address_3') }}
      </el-button>
      <el-button type="primary" :loading="loading" @click="save">
        {{ $t('basics.save') }}
      </el-button>
    </div>
  </ele-modal>
</template>

<script>
  import { update } from '@/api/platform/pickup';
  import { lists } from '@/api/basics/address';
  import PlatformSelect from '@/components/PlatformSelect';

  const DEFAULT_FORM = {
    id: null,
    name: '',
    car_number: '',
    gsname: '',
    tel: null,
    platform_id: null,
    address_1: [],
    create_date: '',
    mate_at: '',
    type: '',
    trailer_plate: '',
    sort: 1,
    remark: ''
  };

  export default {
    name: 'EditAwb',
    components: { PlatformSelect },
    props: {
      // 弹窗是否打开
      visible: Boolean,
      // 修改回显的数据
      data: Object
    },
    data() {
      return {
        columns: [
          {
            columnKey: 'address',
            prop: 'address',
            label: this.$t('platform.pickup.address_1'),
            minWidth: 220,
            align: 'center',
            resizable: false,
            slot: 'address'
          },
          {
            columnKey: 'cmr',
            label: this.$t('platform.pickup.cmr'),
            prop: 'cmr',
            minWidth: 220,
            align: 'center',
            resizable: false,
            slot: 'cmr'
          },
          {
            columnKey: 'action',
            label: this.$t('basics.action'),
            width: 220,
            align: 'center',
            resizable: false,
            slot: 'action',
            hideInSetting: true
          }
        ],
        // 表单数据
        form: { ...DEFAULT_FORM },
        addresses: [],
        types: ['Truck', 'Van'],
        // 提交状态
        loading: false,
        // 是否是修改
        isUpdate: false
      };
    },
    async created() {
      let data = {
        page: 1,
        status: 2,
        num: 10000
      };
      let a = await lists(data);
      this.addresses = a.list;
    },
    methods: {
      add() {
        let lastArr = this.form.address_1.slice(-1);
        if (lastArr.length > 0) {
          if (lastArr[0].address == '' || lastArr[0].cmr == '') {
            return;
          }
        }
        this.form.address_1.push({
          address: '',
          cmr: '',
          type: 1
        });
      },
      add2() {
        let lastArr = this.form.address_1.slice(-1);
        if (lastArr.length > 0) {
          if (lastArr[0].address == '' || lastArr[0].cmr == '') {
            return;
          }
        }
        this.form.address_1.push({
          address: '',
          cmr: '',
          type: 2
        });
      },
      deleteAdd(index) {
        console.log(index);
        this.form.address_1.splice(index, 1);
      },
      /* 保存编辑 */
      save() {
        this.$refs['form'].validate((valid) => {
          if (!valid) {
            return false;
          }
          this.loading = true;
          const data = {
            ...this.form
          };
          update(data)
            .then((msg) => {
              this.loading = false;
              this.$message.success(this.$t('basics.success'));
              this.updateVisible(false);
              this.$emit('done');
            })
            .catch((e) => {
              this.loading = false;
              this.$message.error(e);
            });
        });
      },
      /* 更新visible */
      updateVisible(value) {
        this.$emit('update:visible', value);
      },
      /* 角色选择回调*/
      roleSelectCallback(value) {
        this.form.role_type = value.type;
      }
    },
    watch: {
      async visible(visible) {
        if (visible) {
          let data = this.data;
          if (this.data) {
            this.$util.assignObject(this.form, {
              ...data
            });
          }
        } else {
          this.$refs['form'].clearValidate();
          this.form = { ...DEFAULT_FORM };
        }
      }
    }
  };
</script>
