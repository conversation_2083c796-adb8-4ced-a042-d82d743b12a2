<template>
  <el-card shadow="never" body-style="padding: 22px 22px 7px 22px;">
    <!-- 搜索表单 -->
    <el-form
      label-width="185px"
      class="ele-form-search"
      @keyup.enter.native="search"
      @submit.native.prevent
    >
      <el-row :gutter="15">
        <el-col :lg="8" :md="12">
          <el-form-item :label="`${$t('car.addressOut')}:`">
            <address-select v-model="where.address_code" />
            <!-- <address-select :searchType="1" v-model="where.address_code" /> -->
          </el-form-item>
        </el-col>
        <!-- 所属用户-->
        <el-col :lg="8" :md="12">
          <el-form-item :label="`${$t('awb.awb.col9')}:`" prop="bol_no">
            <user-select v-model="where.user_id" />
          </el-form-item>
        </el-col>
        <!--实际落地时间-->
        <el-col :lg="8" :md="12">
          <el-form-item :label="$t('awb.awb.col21') + ':'" prop="ata_time">
            <el-date-picker
              v-model="where.ata_time"
              type="datetimerange"
              value-format="yyyy-MM-dd HH:mm:ss"
              :range-separator="$t('basics.to')"
              class="ele-fluid"
              :start-placeholder="$t('basics.beginTime')"
              :end-placeholder="$t('basics.endTime')"
            >
            </el-date-picker>
          </el-form-item>
        </el-col>
        <!--        -->
        <el-col :lg="8" :md="12">
          <div class="ele-form-actions">
            <el-button
              type="primary"
              icon="el-icon-search"
              class="ele-btn-icon"
              @click="search"
            >
              {{ $t('basics.query') }}
            </el-button>
            <el-button @click="reset">{{ $t('basics.reset') }}</el-button>
          </div>
        </el-col>
      </el-row>
    </el-form>
  </el-card>
</template>

<script>
  import AddressSelect from '@/components/AddressSelect';
  import UserSelect from '@/views/order/awb/components/user-select';
  const DEFAULT_WHERE = {
    address_code: '',
    ata_time: null
  };

  export default {
    name: 'outbound-fba-search',
    components: {
      AddressSelect,
      UserSelect
    },
    data() {
      return {
        // 表单数据
        where: { ...DEFAULT_WHERE },
        // 搜索表单是否展开
        searchExpand: false,
        goodsCategoryList: [],
        category_id: ''
      };
    },
    methods: {
      /* 搜索 */
      search() {
        //解决搜索 也搜索下级的数据
        this.$emit('search', this.where);
      },
      /* 重置搜索 */
      reset() {
        this.where = { ...DEFAULT_WHERE };
        this.search();
      },
      /* 搜索展开/收起 */
      toggleExpand() {
        this.searchExpand = !this.searchExpand;
        this.$emit('expand-change', this.searchExpand);
      },
      /* 搜索展开/收起 */
      goodsCategoryFilterListClick(e) {
        this.goodsCategoryList = e.data;
      }
    }
  };
</script>
