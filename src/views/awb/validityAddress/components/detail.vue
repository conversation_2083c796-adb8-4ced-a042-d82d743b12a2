<!-- 编辑弹窗 -->
<template>
  <ele-modal
    width="1080px"
    :visible="visible"
    :close-on-click-modal="false"
    custom-class="ele-dialog-form"
    :title="$t('basics.detail')"
    @update:visible="updateVisible"
  >
    <el-form
      ref="form"
      :model="form"
      label-width="170px"
    >
      <el-row :gutter="15">
        <el-col :sm="12">
          <el-form-item :label="`${$t('awb.noa.awbno')}:`" prop="bol_no">
            <el-input
              clearable
              :maxlength="50"
              show-word-limit
              v-model="form.bol_no"
              :disabled="true"
            />
          </el-form-item>
        </el-col>
        <el-col :sm="12">
          <el-form-item :label="`${$t('car.addressOut')}:`" prop="addr_name">
            <el-input
              clearable
              show-word-limit
              v-model="form.addr_name"
              :disabled="true"
            />
          </el-form-item>
        </el-col>
        <el-col :sm="12">
          <el-form-item :label="`${$t('validity.reason_str')}:`" prop="expensive">
            <el-select
              clearable
              multiple
              filterable
              v-model="form.reason_ids"
              class="ele-fluid"
              @change="$forceUpdate()"
              :disabled="true"
            >
              <el-option
                v-for="{ id, reason } in reasonList"
                :key="id"
                :value="id"
                :label="reason"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :sm="12">
          <el-form-item
            :label="`${$t('validity.reason_mark')}`"
          >
            <el-input
              clearable
              show-word-limit
              v-model="form.reason_mark"
              :disabled="true"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div style="width: 100%; height: 20px"></div>
  </ele-modal>
</template>

<script>
  import { awbReasonInfo } from '@/api/order/validity';
  const DEFAULT_FORM = {
    id: null,
    bol_id: '',
    bol_no: '',
    address_code: '',
    addr_name: '',
    reason_mark: '',
    reason_ids: []
  };

  export default {
    name: 'EditAwb',
    components: {},
    props: {
      // 弹窗是否打开
      visible: Boolean,
      // 修改回显的数据
      data: Object
    },
    data() {
      return {
        // 表单数据
        form: { ...DEFAULT_FORM },
        reasonList: [],
        showViewer: false,
        // 提交状态
        loading: false,
        // 是否是修改
        isUpdate: false
      };
    },
    methods: {
      /* 更新visible */
      updateVisible(value) {
        this.$emit('update:visible', value);
      },
      /* 角色选择回调*/
      roleSelectCallback(value) {
        this.form.role_type = value.type;
      }
    },
    watch: {
      async visible(visible) {
        if (visible) {
          //报错
          let a = await awbReasonInfo({
            bol_id: this.data.bol_id,
            bol_no: this.data.bol_no,
            address_code: this.data.address_code,
            addr_name: this.data.addr_name
          });
          if (this.data) {
            this.reasonList = a.result.reasonList;
            this.$util.assignObject(this.form, {
              ...a.result.info
            });
            this.isUpdate = true;
          } else {
            this.isUpdate = false;
          }
        } else {
          this.$refs['form'].clearValidate();
          this.form = { ...DEFAULT_FORM };
        }
      }
    }
  };
</script>
<style lang="scss">
  .steps {
    width: 100%;
    height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;

    .items {
      display: flex;
      align-items: center;
      cursor: pointer;
      .bor {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        padding: 13px;
        box-sizing: border-box;
        flex-shrink: 0;
        border: 1px solid #cecece;

        span {
          width: 100%;
          display: block;
          font-size: 30px;
          line-height: 30px;
          text-align: center;
        }
      }

      .active {
        background: #1296db;
        border: 1px solid #1296db;
      }

      .hr {
        width: 40px;
        height: 2px;
        background: #cecece;
        margin: 0 10px;
      }

      .hractive {
        background: #1296db;
      }
    }

    .items:last-child {
      .hr {
        display: none;
      }
    }
  }

  .el-form-item-time {
    .el-form-item__label {
      line-height: 60px;
    }
  }
</style>
