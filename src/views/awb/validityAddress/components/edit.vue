<!-- 编辑弹窗 -->
<template>
  <ele-modal
    width="720px"
    :visible="visible"
    :close-on-click-modal="false"
    custom-class="ele-dialog-form"
    :title="this.$t('validity.reason_setting')"
    @update:visible="updateVisible"
  >
    <el-form ref="form" :model="form" :rules="rules" label-width="155px">
      <el-row :gutter="15">
        <el-col :lg="16" :sm="12">
          <el-form-item
            :label="this.$t('validity.reason_name') + ':'"
            prop="reason_ids"
          >
            <el-select
              clearable
              multiple
              filterable
              v-model="form.reason_ids"
              class="ele-fluid"
              @change="$forceUpdate()"
            >
              <el-option
                v-for="{ id, reason } in reasonList"
                :key="id"
                :value="id"
                :label="reason"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :lg="16" :sm="12">
          <el-form-item
            :label="this.$t('validity.reason_mark') + ':'"
            prop="reason"
          >
            <el-input :maxlength="100" v-model="form.reason" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div slot="footer">
      <el-button @click="updateVisible(false)">
        {{ $t('basics.cancel') }}
      </el-button>
      <el-button type="primary" :loading="loading" @click="save">
        {{ $t('basics.save') }}
      </el-button>
    </div>
  </ele-modal>
</template>

<script>
  import { editReason } from '@/api/order/validity';
  import { lists } from '@/api/basics/reason';
  const DEFAULT_FORM = {
    reason_key_arr: null,
    reason: '',
    reason_ids: []
  };

  export default {
    name: 'UserEdit',
    components: {},
    props: {
      // 弹窗是否打开
      visible: Boolean,
      // 修改回显的数据
      data: Object
    },
    data() {
      return {
        // 表单数据
        form: { ...DEFAULT_FORM },
        reasonList: [],
        // 表单验证规则
        rules: {},
        // 提交状态
        loading: false,
        // 是否是修改
        isUpdate: false
      };
    },
    activated() {
      lists({
        page: 1,
        num: 10000
      }).then((res) => {
        this.reasonList = res.list;
      });
    },
    created() {},
    methods: {
      /* 保存编辑 */
      save() {
        this.$refs['form'].validate((valid) => {
          if (!valid) {
            return false;
          }
          this.loading = true;
          editReason(this.form)
            .then(() => {
              this.loading = false;
              this.$message.success(this.$t('basics.success'));
              this.updateVisible(false);
              this.$emit('done');
            })
            .catch((e) => {
              this.loading = false;
              this.$message.error(e);
            });
        });
      },
      /* 更新visible */
      updateVisible(value) {
        this.$emit('update:visible', value);
      },
      /* 角色选择回调*/
      roleSelectCallback(value) {
        this.form.role_type = value.type;
      }
    },
    watch: {
      async visible(visible) {
        console.log(visible);
        if (visible) {
          let data = this.data;
          if (this.data) {
            this.$util.assignObject(this.form, {
              ...data
            });
            this.isUpdate = true;
          } else {
            this.isUpdate = false;
          }
        } else {
          this.$refs['form'].clearValidate();
          this.form = { ...DEFAULT_FORM };
        }
      }
    }
  };
</script>
