<template>
  <div class="ele-body">
    <el-card shadow="never">
      <!-- 搜索表单 -->
      <search @search="reload" />
      <!-- 数据表格 -->
      <ele-pro-table
        ref="table"
        :columns="columnsData"
        :datasource="datasource"
        :selection.sync="selection"
      >
        <!-- 表头工具栏 -->
        <template slot="toolbar">
          <el-button
            size="small"
            icon="el-icon-plus"
            class="ele-btn-icon"
            @click="batchEditReason"
            v-permission="'bols:editReason'"
          >
            {{ $t('validity.reason_setting') }}
          </el-button>
        </template>
        <template slot="cmr_out_hour" slot-scope="{ row }">
          <span
            style="color: var(--color-primary)"
            v-if="row.cmr_out_hour > setting.cmr_out_hour"
            >{{ row.cmr_out_hour }}</span
          >
          <span v-else>{{ row.cmr_out_hour }}</span>
        </template>
        <template slot="plan_hour" slot-scope="{ row }">
          <span
            style="color: var(--color-primary)"
            v-if="row.plan_hour > setting.plan_hour"
            >{{ row.plan_hour }}</span
          >
          <span v-else>{{ row.plan_hour }}</span>
        </template>
        <template slot="noa_pmc_hour" slot-scope="{ row }">
          <span
            style="color: var(--color-primary)"
            v-if="row.noa_pmc_hour > setting.noa_pmc_hour"
            >{{ row.noa_pmc_hour }}</span
          >
          <span v-else>{{ row.noa_pmc_hour }}</span>
        </template>
        <template slot="noa_loose_hour" slot-scope="{ row }">
          <span
            style="color: var(--color-primary)"
            v-if="row.noa_loose_hour > setting.noa_loose_hour"
            >{{ row.noa_loose_hour }}</span
          >
          <span v-else>{{ row.noa_loose_hour }}</span>
        </template>
        <template slot="cmr_pmc_hour" slot-scope="{ row }">
          <span
            style="color: var(--color-primary)"
            v-if="row.cmr_pmc_hour > setting.cmr_pmc_hour"
            >{{ row.cmr_pmc_hour }}</span
          >
          <span v-else>{{ row.cmr_pmc_hour }}</span>
        </template>
        <template slot="cmr_loose_hour" slot-scope="{ row }">
          <span
            style="color: var(--color-primary)"
            v-if="row.cmr_loose_hour > setting.cmr_loose_hour"
            >{{ row.cmr_loose_hour }}</span
          >
          <span v-else>{{ row.cmr_loose_hour }}</span>
        </template>
        <template slot="cmr_in_hour" slot-scope="{ row }">
          <span
            style="color: var(--color-primary)"
            v-if="row.cmr_in_hour > setting.cmr_in_hour"
            >{{ row.cmr_in_hour }}</span
          >
          <span v-else>{{ row.cmr_in_hour }}</span>
        </template>
        <template slot="edt_in_hour" slot-scope="{ row }">
          <span
            style="color: var(--color-primary)"
            v-if="row.edt_in_hour > setting.edt_in_hour"
            >{{ row.edt_in_hour }}</span
          >
          <span v-else>{{ row.edt_in_hour }}</span>
        </template>
        <template slot="customs_hour" slot-scope="{ row }">
          <span
            style="color: var(--color-primary)"
            v-if="row.customs_hour > setting.customs_hour"
            >{{ row.customs_hour }}</span
          >
          <span v-else>{{ row.customs_hour }}</span>
        </template>
        <template slot="box_hour" slot-scope="{ row }">
          <span
            style="color: var(--color-primary)"
            v-if="row.box_hour > setting.box_hour"
            >{{ row.box_hour }}</span
          >
          <span v-else>{{ row.box_hour }}</span>
        </template>
        <template slot="station_hour" slot-scope="{ row }">
          <span
            style="color: var(--color-primary)"
            v-if="row.station_hour > setting.station_hour"
            >{{ row.station_hour }}</span
          >
          <span v-else>{{ row.station_hour }}</span>
        </template>
        <template slot="action" slot-scope="{ row }">
          <el-link
            @click="editReason(row)"
            type="primary"
            icon="el-icon-tickets"
            v-permission="'bols:editReason'"
            :underline="false"
          >
            {{ $t('validity.reason_setting') }}
          </el-link>
          <el-link
            @click="detailawb(row)"
            type="primary"
            icon="el-icon-tickets"
            :underline="false"
          >
            {{ $t('basics.detail') }}
          </el-link>
        </template>
      </ele-pro-table>
    </el-card>
    <detail-awb :visible.sync="showDetailAwb" :data="current" />
    <edit-reason :visible.sync="showEdit" :data="current" />
  </div>
</template>

<script>
  import { validityAddressLists } from '@/api/order/validity';
  import { getProcess } from '@/api/views';
  import search from './components/search';
  import DetailAwb from './components/detail';
  import EditReason from './components/edit';

  export default {
    name: 'billOfLadingAddress',
    components: {
      DetailAwb,
      EditReason,
      search
    },
    data() {
      return {
        columnsData: [
          {
            width: 60,
            type: 'selection',
            columnKey: 'selection',
            align: 'center',
            showOverflowTooltip: true,
            fixed: 'left'
          },
          //提单号
          {
            prop: 'bol_no',
            label: this.$t('awb.noa.awbno'),
            showOverflowTooltip: true,
            minWidth: 200
          },
          //派送地址
          {
            prop: 'addr_name',
            label: this.$t('car.addressOut'), //地址名称
            showOverflowTooltip: true,
            minWidth: 200
          },
          //创建时间
          {
            prop: 'created_at',
            label: this.$t('validity.validityBols.created_at'),
            showOverflowTooltip: true,
            minWidth: 160
          },
          //实际落地时间
          {
            prop: 'bol_flight',
            label: this.$t('validity.validityBols.bol_flight'),
            showOverflowTooltip: true,
            minWidth: 160
          },
          {
            // NOA时间(PMC)
            prop: 'bol_pmc_noa',
            label: this.$t('validity.validityBols.bol_pmc_noa'),
            showOverflowTooltip: true,
            minWidth: 160
          },
          {
            // NOA时间(LOOSE)
            prop: 'bol_loose_noa',
            label: this.$t('validity.validityBols.bol_loose_noa'),
            showOverflowTooltip: true,
            minWidth: 160
          },
          {
            // NOA时间(LOOSE)
            prop: 'bol_customs',
            label: this.$t('validity.validityBols.bol_customs'),
            showOverflowTooltip: true,
            minWidth: 160
          },
          {
            // 到达货站时间
            prop: 'bol_cmr_station_in',
            label: this.$t('validity.validityBols.bol_cmr_station_in'),
            showOverflowTooltip: true,
            minWidth: 160
          },
          {
            // 到达货站时间(PMC)
            prop: 'bol_station_in_pmc',
            label: this.$t('validity.validityBols.bol_station_in_pmc'),
            showOverflowTooltip: true,
            show: false,
            minWidth: 160
          },
          {
            // 到达货站时间(LOOSE)
            prop: 'bol_station_in_loose',
            label: this.$t('validity.validityBols.bol_station_in_loose'),
            showOverflowTooltip: true,
            show: false,
            minWidth: 160
          },
          {
            // 离开货站时间
            prop: 'bol_cmr_station_out',
            label: this.$t('validity.validityBols.bol_cmr_station_out'),
            showOverflowTooltip: true,
            minWidth: 160
          },
          {
            // 离开货站时间
            prop: 'bol_cmr_station_out_edt',
            label: this.$t('validity.validityBols.bol_cmr_station_out_edt'),
            showOverflowTooltip: true,
            show: false,
            minWidth: 160
          },
          {
            // 到达仓库时间
            prop: 'bol_cmr_in',
            label: this.$t('validity.validityBols.bol_cmr_in'),
            showOverflowTooltip: true,
            minWidth: 160
          },
          {
            // 离开仓库时间
            prop: 'bol_cmr_out',
            label: this.$t('validity.validityBols.bol_cmr_out'),
            showOverflowTooltip: true,
            minWidth: 160
          },
          {
            // 出仓时间 扫描最后一箱时间
            prop: 'box_in_last_time',
            label: this.$t('validity.validityBols.box_in_last_time'),
            showOverflowTooltip: true,
            minWidth: 160
          },
          {
            // 出仓时间 扫描最后一箱时间
            prop: 'box_out_last_time',
            label: this.$t('validity.validityBols.box_out_last_time'),
            showOverflowTooltip: true,
            minWidth: 160
          },
          {
            // 箱子入库时间
            prop: 'bol_box_in',
            label: this.$t('validity.validityBols.bol_box_in'),
            showOverflowTooltip: true,
            show: false,
            minWidth: 160
          },
          {
            // 箱子出库时间
            prop: 'bol_box_out',
            label: this.$t('validity.validityBols.bol_box_out'),
            showOverflowTooltip: true,
            show: false,
            minWidth: 160
          },
          {
            // 出库时效
            prop: 'cmr_out_hour',
            label: this.$t('validity.validityBols.cmr_out_hour'),
            showOverflowTooltip: true,
            minWidth: 160,
            slot: 'cmr_out_hour'
          },
          {
            // 预报段时效
            prop: 'plan_hour',
            label: this.$t('validity.validityBols.plan_hour'),
            showOverflowTooltip: true,
            minWidth: 160,
            slot: 'plan_hour'
          },
          {
            // NOA段时效(PMC)
            prop: 'noa_pmc_hour',
            label: this.$t('validity.validityBols.noa_pmc_hour'),
            showOverflowTooltip: true,
            minWidth: 160,
            slot: 'noa_pmc_hour'
          },
          {
            // NOA段时效(LOOSE)
            prop: 'noa_loose_hour',
            label: this.$t('validity.validityBols.noa_loose_hour'),
            showOverflowTooltip: true,
            minWidth: 160,
            slot: 'noa_loose_hour'
          },
          {
            // 提货调度段时效(NOA)
            prop: 'cmr_pmc_hour',
            label: this.$t('validity.validityBols.cmr_pmc_hour'),
            showOverflowTooltip: true,
            minWidth: 160,
            slot: 'cmr_pmc_hour'
          },
          {
            // 提货调度段时效(LOOSE)
            prop: 'cmr_loose_hour',
            label: this.$t('validity.validityBols.cmr_loose_hour'),
            showOverflowTooltip: true,
            minWidth: 160,
            slot: 'cmr_loose_hour'
          },
          {
            // 货站装车段时效
            prop: 'cmr_in_hour',
            label: this.$t('validity.validityBols.cmr_in_hour'),
            showOverflowTooltip: true,
            minWidth: 160,
            slot: 'cmr_in_hour'
          },
          {
            // 货站到EDT段时效
            prop: 'edt_in_hour',
            label: this.$t('validity.validityBols.edt_in_hour'),
            showOverflowTooltip: true,
            minWidth: 160,
            slot: 'edt_in_hour'
          },
          {
            // 出库调整段时效
            prop: 'box_hour',
            label: this.$t('validity.validityBols.box_hour'),
            showOverflowTooltip: true,
            minWidth: 160,
            slot: 'box_hour'
          },
          {
            // 清关段时效
            prop: 'customs_hour',
            label: this.$t('validity.validityBols.customs_hour'),
            showOverflowTooltip: true,
            minWidth: 160,
            slot: 'customs_hour'
          },
          {
            // 库操段时效
            prop: 'station_hour',
            label: this.$t('validity.validityBols.station_hour'),
            showOverflowTooltip: true,
            minWidth: 160,
            slot: 'station_hour'
          },
          {
            columnKey: 'action',
            label: this.$t('basics.action'),
            width: 220,
            align: 'center',
            resizable: false,
            slot: 'action',
            hideInSetting: true,
            fixed: 'right'
          }
        ],
        // 表格选中数据
        selection: [],
        where: [],
        // 当前编辑数据
        current: null,
        currentRow: null,
        // 是否显示编辑弹窗
        showEdit: false,
        showChangePassword: false,
        setting: {},
        showDetail: false,
        showDetailAwb: false,
        loading: false
      };
    },
    activated() {
      this.reload();
    },
    async mounted() {
      const config = await getProcess();
      this.setting = config.content;
    },
    methods: {
      detailawb(row) {
        //查看
        this.current = {
          ...row
        };
        this.showDetailAwb = true;
      },
      batchEditReason() {
        this.current = {
          reason_key_arr: this.selection.map((i) => ({
            bol_id: i.bol_id,
            address_code: i.address_code
          }))
        };
        this.showEdit = true;
      },
      editReason(row) {
        this.current = {
          reason_key_arr: [
            { bol_id: row.bol_id, address_code: row.address_code }
          ]
        };
        this.showEdit = true;
      },
      /* 表格数据源 */
      datasource({ page, limit, where, order }) {
        return validityAddressLists({
          ...where,
          ...order,
          page: page,
          num: limit
        });
      },
      /* 刷新表格 */
      reload(where) {
        this.where = where;
        this.$refs.table.reload({ page: 1, where: where });
      }
    }
  };
</script>
