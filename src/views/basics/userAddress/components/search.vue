<!-- 搜索表单 -->
<template>
  <el-form
    label-width="150px"
    class="ele-form-search"
    @keyup.enter.native="search"
    @submit.native.prevent
  >
    <el-row :gutter="15">
      <el-col :lg="8" :md="12">
        <el-form-item :label="this.$t('car.addressOut') + ':'" prop="code">
          <address-select v-model="where.addresses_id" :searchType="1" />
        </el-form-item>
      </el-col>
      <el-col :lg="8" :md="12" v-if="!this.$hasRole('sonOwner')">
        <el-form-item :label="$t('awb.awb.col9') + ':'" prop="user_id">
          <user-select v-model="where.user_id" />
        </el-form-item>
      </el-col>
      <el-col :lg="8" :md="12">
        <el-form-item
          :label="$t('userAddress.user_addresses') + ':'"
          prop="user_addresses"
        >
          <el-input v-model="where.user_addresses" />
        </el-form-item>
      </el-col>
      <el-col :lg="6" :md="12">
        <div class="ele-form-actions">
          <el-button
            type="primary"
            icon="el-icon-search"
            class="ele-btn-icon"
            @click="search"
          >
            {{ $t('basics.query') }}
          </el-button>
          <el-button @click="reset">{{ $t('basics.reset') }}</el-button>
        </div>
      </el-col>
    </el-row>
  </el-form>
</template>

<script>
  import AddressSelect from './addressSelect';
  import userSelect from './user-select';
  const DEFAULT_WHERE = {
    user_id: null,
    addresses_id: null,
    user_addresses: null
  };

  export default {
    name: 'RoleSearch',
    components: { AddressSelect, userSelect },
    data() {
      console.log(this.$hasRole('owner'))
      return {
        // 表单数据
        where: { ...DEFAULT_WHERE }
      };
    },
    methods: {
      /* 搜索 */
      search() {
        this.$emit('search', this.where);
      },
      /*  重置 */
      reset() {
        this.where = { ...DEFAULT_WHERE };
        this.search();
      }
    }
  };
</script>
