<!-- 编辑弹窗 -->
<template>
  <ele-modal
    width="840px"
    :visible="visible"
    :close-on-click-modal="false"
    custom-class="ele-dialog-form"
    :title="isUpdate ? this.$t('basics.edit') : this.$t('basics.create')"
    @update:visible="updateVisible"
  >
    <el-form ref="form" :model="form" :rules="rules" label-width="155px">
      <el-row :gutter="15">
        <el-col :sm="12">
          <el-form-item :label="$t('car.address') + ':'" prop="addresses_id">
            <address-select v-model="form.addresses_id" :searchType="1" />
          </el-form-item>
        </el-col>
        <el-col :sm="12">
          <el-form-item :label="$t('awb.awb.col9') + ':'" prop="user_id">
            <user-select v-model="form.user_id" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="15">
        <el-col :sm="12">
          <el-form-item
            :label="$t('userAddress.user_addresses') + ':'"
            prop="user_addresses"
          >
            <el-input v-model="form.user_addresses" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div slot="footer">
      <el-button @click="updateVisible(false)">
        {{ $t('basics.cancel') }}
      </el-button>
      <el-button type="primary" :loading="loading" @click="save">
        {{ $t('basics.save') }}
      </el-button>
    </div>
  </ele-modal>
</template>

<script>
  import { update, create } from '@/api/basics/useraddress';
  import AddressSelect from './addressSelect';
  import userSelect from './user-select';

  const DEFAULT_FORM = {
    id: null,
    user_id: null,
    addresses_id: null,
    user_addresses: ''
  };

  export default {
    name: 'UserEdit',
    components: { AddressSelect, userSelect },
    props: {
      // 弹窗是否打开
      visible: Boolean,
      // 修改回显的数据
      data: Object
    },
    data() {
      return {
        // 表单数据
        form: { ...DEFAULT_FORM },
        // 表单验证规则
        rules: {
          addresses_id: [
            {
              required: true,
              message: this.$t('basics.pleaseChoose'),
              trigger: 'blur'
            }
          ],
          user_id: [
            {
              required: true,
              message: this.$t('basics.pleaseChoose'),
              trigger: 'blur'
            }
          ],
          user_addresses: [
            {
              required: true,
              message: this.$t('basics.pleaseInput'),
              trigger: 'blur'
            }
          ]
        },
        // 提交状态
        loading: false,
        // 是否是修改
        isUpdate: false
      };
    },
    methods: {
      /* 保存编辑 */
      save() {
        this.$refs['form'].validate((valid) => {
          if (!valid) {
            return false;
          }
          this.loading = true;
          const data = {
            ...this.form
          };
          (this.isUpdate ? update(data) : create(data))
            .then(() => {
              this.loading = false;
              this.$message.success(this.$t('basics.success'));
              this.updateVisible(false);
              this.$emit('done');
            })
            .catch((e) => {
              this.loading = false;
              this.$message.error(e);
            });
        });
      },
      /* 更新visible */
      updateVisible(value) {
        this.$emit('update:visible', value);
      },
      /* 角色选择回调*/
      roleSelectCallback(value) {
        this.form.role_type = value.type;
      }
    },
    watch: {
      async visible(visible) {
        console.log(visible);
        if (visible) {
          let data = this.data;
          if (this.data) {
            this.$util.assignObject(this.form, {
              ...data,
              user_id: parseInt(data.user_id),
              addresses_id: parseInt(data.addresses_id)
            });
            console.log(this.form);
            this.isUpdate = true;
          } else {
            this.isUpdate = false;
          }
        } else {
          this.$refs['form'].clearValidate();
          this.form = { ...DEFAULT_FORM };
        }
      }
    }
  };
</script>
