<template>
  <div class="ele-body">
    <el-card shadow="never">
      <!-- 搜索表单 -->
      <search @search="reload" />
      <!-- 数据表格 -->
      <ele-pro-table
        ref="table"
        :initLoad="false"
        :columns="columns()"
        :datasource="datasource"
        :selection.sync="selection"
      >
        <!-- 表头工具栏 -->
        <template slot="toolbar">
          <el-button
            v-permission="'AddressUser:create'"
            size="small"
            type="primary"
            icon="el-icon-plus"
            class="ele-btn-icon"
            @click="openEdit()"
          >
            {{ $t('basics.create') }}
          </el-button>
        </template>
        <!-- 操作列 -->
        <template slot="action" slot-scope="{ row }">
          <el-link
            @click="openEdit(row)"
            v-permission="'AddressUser:update'"
            type="primary"
            icon="el-icon-edit"
            :underline="false"
          >
            {{ $t('basics.edit') }}
          </el-link>
          <el-popconfirm
            class="ele-action"
            :title="$t('basics.confirmDel')"
            v-permission="'AddressUser:delete'"
            @confirm="remove(row)"
            icon="el-icon-delete"
          >
            <el-link
              type="danger"
              slot="reference"
              icon="el-icon-delete"
              :underline="false"
            >
              {{ $t('basics.delete') }}
            </el-link>
          </el-popconfirm>
        </template>
      </ele-pro-table>
    </el-card>
    <address-edit :visible.sync="showEdit" :data="current" @done="reload" />
  </div>
</template>

<script>
  import { lists, destroy } from '@/api/basics/useraddress';
  import AddressEdit from './components/edit';
  import search from './components/search';

  export default {
    name: 'SystemUser',
    components: {
      AddressEdit,
      search
    },
    data() {
      return {
        // 表格列配置
        columnsData: [
          {
            columnKey: 'index',
            type: 'index',
            width: 45,
            showOverflowTooltip: true,
            fixed: 'left'
          },
          {
            prop: 'user_addresses',
            label: this.$t('userAddress.user_addresses'),
            showOverflowTooltip: true,
            minWidth: 110
          },
          {
            prop: 'user_name',
            noRole: 'sonOwner', //什么角色下不显示
            label: this.$t('awb.awb.col9'),
            showOverflowTooltip: true,
            minWidth: 210
          },
          {
            prop: 'addresses.addr_name',
            label: this.$t('basicsMenu.address.name'),
            showOverflowTooltip: true,
            minWidth: 210
          },
          {
            prop: 'addresses.country',
            label: this.$t('basicsMenu.address.country'),
            showOverflowTooltip: true,
            minWidth: 210
          },
          {
            prop: 'addresses.address',
            label: this.$t('basicsMenu.address.address'),
            showOverflowTooltip: true,
            minWidth: 210
          },
          {
            prop: 'addresses.opens',
            label: this.$t('basicsMenu.address.openTime'),
            showOverflowTooltip: true,
            minWidth: 210
          },
          {
            prop: 'created_at',
            label: this.$t('basics.createTime'),
            showOverflowTooltip: true,
            minWidth: 110
          },
          {
            columnKey: 'action',
            noRole: 'sonOwner', //什么角色下不显示
            label: this.$t('basics.action'),
            width: 220,
            align: 'center',
            resizable: false,
            slot: 'action',
            hideInSetting: true,
            fixed: 'right'
          }
        ],
        // 表格选中数据
        selection: [],
        // 当前编辑数据
        current: null,
        // 是否显示编辑弹窗
        showEdit: false,
        showDetail: false,
        showFinanceEdit: false,
        // 是否显示导入弹窗
        showImport: false,
        // 是否显修改密码
        userId: null,
        showChangePassword: false
      };
    },
    activated() {
      this.reload();
    },
    methods: {
      columns() {
        return this.columnsData.filter((i) => {
          return i.noRole ? !this.$hasRole(i.noRole) : true;
        });
      },
      /* 表格数据源 */
      datasource({ page, limit, where, order }) {
        return lists({
          ...where,
          ...order,
          page: page,
          num: limit
        });
      },
      /* 刷新表格 */
      reload(where) {
        this.$refs.table.reload({ page: 1, where: where });
      },
      /* 打开编辑弹窗 */
      openEdit(row) {
        this.current = row;
        this.showEdit = true;
      },
      openFinanceEdit(row) {
        this.current = row;
        this.showFinanceEdit = true;
      },
      openDetail(row) {
        this.current = row;
        this.showDetail = true;
      },
      /* 打开导入弹窗 */
      openImport() {
        this.showImport = true;
      },
      /* 删除 */
      remove(row) {
        const loading = this.$loading({ lock: true });
        destroy({ id: row.id })
          .then((msg) => {
            loading.close();
            this.$message.success(msg.status);
            this.reload();
          })
          .catch((e) => {
            loading.close();
            this.$message.error(e);
          });
      }
    }
  };
</script>
