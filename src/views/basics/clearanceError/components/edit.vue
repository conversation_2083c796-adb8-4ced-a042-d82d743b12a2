<!-- 编辑弹窗 -->
<template>
  <ele-modal
    width="720px"
    :visible="visible"
    :close-on-click-modal="false"
    custom-class="ele-dialog-form"
    :title="this.$t('clearanceError.error_value')"
    @update:visible="updateVisible"
  >
    <el-form ref="form" :model="form" :rules="rules" label-width="130px"
             @keyup.enter.native="save"
             @submit.native.prevent>
      <el-row :gutter="15">
        <el-col :sm="12">
          <el-form-item
            prop="error_value"
            :label="this.$t('clearanceError.error_value')+':'">
            <el-input :maxlength="100" v-model="form.error_value" />
          </el-form-item>
        </el-col>
        <!--<el-col :sm="12">
          <el-form-item
            prop="error_value_en"
            :label="this.$t('clearanceError.error_value_en')+':'">
            <el-input :maxlength="100" v-model="form.error_value_en" />
          </el-form-item>
        </el-col>-->
      </el-row>
    </el-form>
    <div slot="footer">
      <el-button @click="updateVisible(false)">
        {{ $t("basics.cancel") }}
      </el-button>
      <el-button type="primary" :loading="loading" @click="save">
        {{ $t("basics.save") }}
      </el-button>
    </div>
  </ele-modal>
</template>

<script>
import { create, update } from "@/api/basics/clearanceError";

const DEFAULT_FORM = {
  error_value: "",
  error_value_en: ""
};

export default {
  name: "clearanceError",
  components: {},
  props: {
    // 弹窗是否打开
    visible: Boolean,
    // 修改回显的数据
    data: Object
  },
  data() {
    return {
      // 表单数据
      form: { ...DEFAULT_FORM },
      // 表单验证规则
      rules: {
        error_value: [
          {
            required: true,
            message: this.$t("basics.pleaseInput"),
            trigger: "blur"
          }
        ]
      },
      // 提交状态
      loading: false,
      // 是否是修改
      isUpdate: false
    };
  },
  methods: {
    /* 保存编辑 */
    save() {
      this.$refs["form"].validate((valid) => {
        if (!valid) return false;

        let form = { ...this.form };
        console.log(form);
        this.loading = true;
        if (this.data == null) {
          console.log("create");
          create(form)
            .then((msg) => {
              this.$message.success(this.$t("basics.success"));
              this.updateVisible(false);
              this.$emit("done");
            })
            .catch((e) => {
              this.$message.error(e);
            })
            .finally(() => {
              this.loading = false;
            });
        } else {
          console.log("update");
          update(form)
            .then((res) => {
              this.$message.success(this.$t("basics.success"));
              this.updateVisible(false);
              this.$emit("done");
            })
            .catch((err) => {
              console.log(err);
              this.$message.error(err);
            })
            .finally(() => {
              this.loading = false;
            });
        }
      });
    },
    /* 更新visible */
    updateVisible(value) {
      this.$emit("update:visible", value);
    },
    /* 角色选择回调*/
    roleSelectCallback(value) {
      this.form.role_type = value.type;
    }
  },
  watch: {
    async visible() {
      this.form = (this.data == null) ?
        { ...DEFAULT_FORM } : { ...this.data };
    }
  }
};
</script>
