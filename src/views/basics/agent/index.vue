<template>
  <div class="ele-body">
    <el-card shadow="never">
      <!-- 搜索表单 -->
      <search @search="reload" />
      <!-- 数据表格 -->
      <ele-pro-table
        ref="table"
        :initLoad="false"
        :columns="columns"
        :datasource="datasource"
        :selection.sync="selection"
      >
        <!-- 表头工具栏 -->
        <template slot="toolbar">
          <el-button
            v-permission="'agent:create'"
            size="small"
            type="primary"
            icon="el-icon-plus"
            class="ele-btn-icon"
            @click="openEdit()"
          >
            {{$t('basics.create')}}
          </el-button>
        </template>
        <!-- 操作列 -->
        <template slot="action" slot-scope="{ row }">
          <el-link
            v-permission="'agent:edit'"
            @click="openEdit(row)"
            type="primary"
            icon="el-icon-edit"
            :underline="false">
            {{$t('basics.edit')}}
          </el-link>
          <!-- <el-link
            v-permission="'users:update'"
            :disabled="row.role_type !== '2'"
            type="primary"
            :underline="false"
            @click="openEdit(row)"
          >
            修改
          </el-link> -->
          <el-popconfirm
            class="ele-action"
            :title="$t('basics.confirmDel')"
            @confirm="cancel(row)"
          >
            <el-link type="danger" v-permission="'agent:delete'" slot="reference" icon="el-icon-delete" :underline="false">
              {{$t('basics.delete')}}
            </el-link>
          </el-popconfirm>
        </template>
      </ele-pro-table>
    </el-card>
    <port-edit :visible.sync="showEdit" :data="current" @done="reload" />
  </div>
</template>

<script>
  import { cancel, lists, update, create } from '@/api/basics/agent';
  import PortEdit from './components/edit';
  import Search from './components/outbound-fba-search';

  export default {
    name: 'SystemUser',
    components: {
      Search,
      PortEdit
      // UserSearch,
      // UserEdit,
      // UserImport,
      // UserChangePassword
    },
    data() {
      return {
        // 表格列配置
        columns: [
          {
            columnKey: 'index',
            type: 'index',
            width: 45,
            showOverflowTooltip: true,
            fixed: 'left'
          },
          {
            prop: 'name',
            label: this.$t('basicsMenu.agent.name'),
            showOverflowTooltip: true,
            minWidth: 110
          },
          {
            prop: 'created_at',
            label: this.$t('basics.createTime'),
            showOverflowTooltip: true,
            minWidth: 110
          },
          {
            columnKey: 'action',
            label: this.$t('basics.action'),
            width: 220,
            align: 'center',
            resizable: false,
            slot: 'action',
            hideInSetting: true
          }
        ],
        // 表格选中数据
        selection: [],
        // 当前编辑数据
        current: null,
        // 是否显示编辑弹窗
        showEdit: false,
        // 是否显示导入弹窗
        showImport: false,
        // 是否显修改密码
        userId: null,
        showChangePassword: false
      };
    },
    created() {
      //或者登陆不显示货主名字其他登录显示名称
      if (
        this.$store.state.user.info.role_type === '2' ||
        this.$store.state.user.info.role_type === '3'
      ) {
        const index = this.columns.findIndex(function (key) {
          return key.prop === 'username';
        });
        this.columns.splice(index, index + 1);
      }
    },
    activated() {
      this.reload();
    },
    methods: {
      /* 表格数据源 */
      datasource({ page, limit, where, order }) {
        return lists({
          ...where,
          ...order,
          page: page,
          num: limit,
          // inbound_id: this.$route.query.inbound_id
        });
      },
      /* 刷新表格 */
      reload(where) {
        this.$refs.table.reload({ page: 1, where: where });
      },
      /* 打开编辑弹窗 */
      openEdit(row) {
        this.current = row;
        this.showEdit = true;
      },
      /* 打开导入弹窗 */
      openImport() {
        this.showImport = true;
      },
      /* 删除 */
      cancel(row) {
        const loading = this.$loading({ lock: true });
        cancel(row.id)
          .then((msg) => {
            loading.close();
            this.$message.success(this.$t('basics.success'));
            this.reload();
          })
          .catch((e) => {
            loading.close();
            this.$message.error(e);
          });
      }
    }
  };
</script>
