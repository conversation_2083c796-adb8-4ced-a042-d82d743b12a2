<template>
  <div class="ele-body">
    <el-card shadow="never">
      <!-- 搜索表单 -->
      <outbound-fba-search @search="reload" />
      <!-- 数据表格 -->
      <ele-pro-table
        ref="table"
        :initLoad="false"
        :columns="columns"
        :datasource="datasource"
        :selection.sync="selection"
      >
        <!-- 表头工具栏 -->
        <template slot="toolbar">
          <el-button
            v-permission="'address:create'"
            size="small"
            type="primary"
            icon="el-icon-plus"
            class="ele-btn-icon"
            @click="openEdit()"
          >
            {{ $t('basics.create') }}
          </el-button>
        </template>
        <!-- 解析数据 -->
        <template slot="status" slot-scope="{ row }">
          <el-switch
            :active-value="2"
            :inactive-value="1"
            v-model="row.status"
            @change="editStatus(row)"
          />
        </template>
        <template slot="tape_color" slot-scope="{ row }">
          <span :style="initColor(row.tape_color)">{{row.tape_color}}</span>
        </template>
        <!-- 操作列 -->
        <template slot="action" slot-scope="{ row }">
          <el-link
            @click="openEdit(row)"
            v-permission="'address:edit'"
            type="primary"
            icon="el-icon-edit"
            :underline="false"
          >
            {{ $t('basics.edit') }}
          </el-link>
          <el-link @click="openDetail(row)" type="primary" :underline="false">
            {{ $t('basics.detail') }}
          </el-link>
          <el-link
            v-permission="'address:finance'"
            @click="openFinanceEdit(row)"
            type="primary"
            :underline="false"
          >
            {{ $t('basics.financeSetting') }}
          </el-link>
          <el-popconfirm
            class="ele-action"
            :title="$t('basics.confirmDel')"
            @confirm="remove(row)"
            icon="el-icon-delete"
          >
            <el-link
              type="danger"
              v-permission="'address:delete'"
              slot="reference"
              icon="el-icon-delete"
              :underline="false"
            >
              {{ $t('basics.delete') }}
            </el-link>
          </el-popconfirm>
        </template>
      </ele-pro-table>
    </el-card>
    <address-edit :visible.sync="showEdit" :data="current" @done="reload" />
    <finance-edit
      :visible.sync="showFinanceEdit"
      :data="current"
      @done="reload"
    />
    <address-detail :visible.sync="showDetail" :data="current" @done="reload" />
  </div>
</template>

<script>
  import { cancel, lists, updateAddressStatus } from '@/api/basics/address';
  import AddressEdit from './components/edit';
  import AddressDetail from './components/detail';
  import OutboundFbaSearch from './components/outbound-fba-search';
  import FinanceEdit from './components/finance-edit';

  export default {
    name: 'SystemUser',
    components: {
      OutboundFbaSearch,
      AddressEdit,
      AddressDetail,
      FinanceEdit
    },
    data() {
      return {
        // 表格列配置
        columns: [
          {
            columnKey: 'index',
            type: 'index',
            width: 45,
            showOverflowTooltip: true,
            fixed: 'left'
          },
          {
            prop: 'code',
            label: this.$t('basicsMenu.address.code'),
            showOverflowTooltip: true,
            minWidth: 100
          },
          {
            prop: 'addr_name',
            label: this.$t('basicsMenu.address.name'),
            showOverflowTooltip: true,
            minWidth: 100
          },
          {
            prop: 'country',
            label: this.$t('basicsMenu.address.country'),
            showOverflowTooltip: true,
            minWidth: 40
          },
          {
            prop: 'address',
            label: this.$t('basicsMenu.address.address'),
            showOverflowTooltip: true,
            minWidth: 120
          },
          {
            prop: 'opens',
            label: this.$t('basicsMenu.address.openTime'),
            showOverflowTooltip: true,
            minWidth: 100
          },
          // {
          //   prop: 'city',
          //   label: '城市',
          //   showOverflowTooltip: true,
          //   minWidth: 110
          // },
          // {
          //   prop: 'name',
          //   label: '姓名',
          //   showOverflowTooltip: true,
          //   minWidth: 110
          // },
          // {
          //   prop: 'name',
          //   label: '姓名',
          //   showOverflowTooltip: true,
          //   minWidth: 110
          // },
          // {
          //   prop: 'user_id',
          //   label: '客户',
          //   showOverflowTooltip: true,
          //   minWidth: 80
          // },
          {
            prop: 'type',
            label: this.$t('basicsMenu.address.type'),
            showOverflowTooltip: true,
            minWidth: 60,
            formatter: (row, column, cellValue) => {
              return [
                this.$t('basicsMenu.address.delivery'),
                this.$t('basicsMenu.address.station')
              ][cellValue - 1];
            }
          },
          // {
          //   prop: 'clearance',
          //   label: '清关状态',
          //   showOverflowTooltip: true,
          //   minWidth: 110,
          //   formatter: (row, column, cellValue) => {
          //     return ['未完成', '需查验', '部分完成', '全部完成'][cellValue];
          //   }
          // },
          {
            prop: 'created_at',
            label: this.$t('basics.createTime'),
            showOverflowTooltip: true,
            minWidth: 80
          },
          {
            prop: 'tape_color',
            label: this.$t('basicsMenu.address.tape_color'),
            align: 'center',
            width: 85,
            slot: 'tape_color'
          },
          {
            prop: 'status',
            label: this.$t('basics.status'),
            align: 'center',
            width: 85,
            slot: 'status'
          },
          {
            columnKey: 'action',
            label: this.$t('basics.action'),
            width: 180,
            align: 'center',
            resizable: false,
            slot: 'action',
            hideInSetting: true,
            fixed: 'right'
          }
        ],
        // 表格选中数据
        selection: [],
        // 当前编辑数据
        current: null,
        // 是否显示编辑弹窗
        showEdit: false,
        showDetail: false,
        showFinanceEdit: false,
        // 是否显示导入弹窗
        showImport: false,
        // 是否显修改密码
        userId: null,
        showChangePassword: false
      };
    },
    created() {
      //或者登陆不显示货主名字其他登录显示名称
      if (
        this.$store.state.user.info.role_type === '2' ||
        this.$store.state.user.info.role_type === '3'
      ) {
        const index = this.columns.findIndex(function (key) {
          return key.prop === 'username';
        });
        this.columns.splice(index, index + 1);
      }
    },
    activated() {
      this.reload();
    },
    methods: {
      /* 表格数据源 */
      datasource({ page, limit, where, order }) {
        return lists({
          ...where,
          ...order,
          page: page,
          num: limit
        });
      },
      /* 刷新表格 */
      reload(where) {
        this.$refs.table.reload({ page: 1, where: where });
      },
      /* 打开编辑弹窗 */
      openEdit(row) {
        this.current = row;
        this.showEdit = true;
      },
      openFinanceEdit(row) {
        this.current = row;
        this.showFinanceEdit = true;
      },
      openDetail(row) {
        this.current = row;
        this.showDetail = true;
      },
      /* 打开导入弹窗 */
      openImport() {
        this.showImport = true;
      },
      initColor(color) {
        if (color) {
          return "color:"+color;
        } else {
          return '';
        }
      },
      /* 删除 */
      remove(row) {
        const loading = this.$loading({ lock: true });
        cancel(row.id)
          .then((msg) => {
            loading.close();
            this.$message.success(msg.status);
            this.reload();
          })
          .catch((e) => {
            loading.close();
            this.$message.error(e);
          });
      },
      /* 更改状态 */
      editStatus(row) {
        const loading = this.$loading({ lock: true });
        updateAddressStatus({ addresses_id: row.id })
          .then((msg) => {
            loading.close();
            console.log(msg)
            this.$message.success(this.$t('basics.success'))
          })
          .catch((e) => {
            loading.close();
            row.status = row.status == 2 ? 1 : 2;
            this.$message.error(e);
          });
      }
    }
  };
</script>
