<!-- 编辑弹窗 -->
<template>
  <ele-modal
    width="720px"
    :visible="visible"
    :close-on-click-modal="false"
    custom-class="ele-dialog-form"
    :title="isUpdate ? this.$t('basics.edit') : this.$t('basics.create')"
    @update:visible="updateVisible"
  >
    <el-form ref="form" :model="form" :rules="rules" label-width="120px">
      <el-row :gutter="15">
        <el-col :sm="12">
          <el-form-item
            :label="this.$t('basicsMenu.address.code') + ':'"
            prop="code"
          >
            <el-input
              :disabled="isUpdate"
              :maxlength="60"
              v-model="form.code"
            />
          </el-form-item>
        </el-col>
        <el-col :sm="12" v-permission="'address:nouser'">
          <el-form-item
            :label="this.$t('basicsMenu.address.type') + ':'"
            prop="type"
          >
            <el-select v-model="form.type" clearable class="ele-fluid">
              <el-option
                :label="this.$t('basicsMenu.address.delivery')"
                :value="1"
              />
              <el-option
                :label="this.$t('basicsMenu.address.station')"
                :value="2"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="15">
        <el-col :sm="12">
          <el-form-item
            :label="this.$t('basicsMenu.address.name') + ':'"
            prop="addr_name"
          >
            <el-input v-model="form.addr_name" />
          </el-form-item>
        </el-col>
        <el-col :sm="12">
          <el-form-item
            :label="this.$t('basicsMenu.address.openTime') + ':'"
            prop="opens"
          >
            <el-input v-model="form.opens" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="15">
        <el-col :sm="12">
          <el-form-item
            :label="this.$t('basicsMenu.address.country') + ':'"
            prop="country"
          >
            <country-code v-model="form.country" prop="country" />
          </el-form-item>
        </el-col>
        <el-col :sm="12">
          <el-form-item
            :label="this.$t('basicsMenu.address.city') + ':'"
            prop="city"
          >
            <el-input :maxlength="40" v-model="form.city" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="15">
        <el-col :sm="12">
          <el-form-item
            :label="this.$t('basicsMenu.address.address') + ':'"
            prop="address"
          >
            <el-input :maxlength="100" v-model="form.address" />
          </el-form-item>
        </el-col>
        <el-col :sm="12">
          <el-form-item
            :label="this.$t('basicsMenu.address.zipcode') + ':'"
            prop="zipcode"
          >
            <el-input :maxlength="40" v-model="form.zipcode" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="15">
        <el-col :sm="12">
          <el-form-item
            :label="this.$t('basicsMenu.address.contacts') + ':'"
            prop="name"
          >
            <el-input :maxlength="40" v-model="form.name" />
          </el-form-item>
        </el-col>
        <el-col :sm="12">
          <el-form-item
            :label="this.$t('basicsMenu.address.tel') + ':'"
            prop="phone"
          >
            <el-input :maxlength="40" v-model="form.phone" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="15">
        <el-col :sm="10">
          <el-form-item
            :label="this.$t('basicsMenu.address.tape_color') + ':'"
            prop="name"
          >
            <el-input :maxlength="20" v-model="form.tape_color" />
          </el-form-item>
        </el-col>
        <el-col :sm="2">
          <el-color-picker v-model="form.tape_color"></el-color-picker>
        </el-col>
      </el-row>
    </el-form>
    <div slot="footer">
      <el-button @click="updateVisible(false)">
        {{ $t('basics.cancel') }}
      </el-button>
      <el-button type="primary" :loading="loading" @click="save">
        {{ $t('basics.save') }}
      </el-button>
    </div>
  </ele-modal>
</template>

<script>
  import { update, create } from '@/api/basics/address';
  import CountryCode from '@/components/CountryCode';
  import { hasPermission } from '@/utils/permission';

  const DEFAULT_FORM = {
    id: null,
    code: '',
    type: 1,
    country: '',
    city: '',
    address: '',
    zipcode: '',
    name: '',
    phone: '',
    addr_name: '',
    opens: '',
    tape_color: ''
  };

  export default {
    name: 'UserEdit',
    components: { CountryCode },
    props: {
      // 弹窗是否打开
      visible: Boolean,
      // 修改回显的数据
      data: Object
    },
    data() {
      return {
        // 表单数据
        form: { ...DEFAULT_FORM },
        // 表单验证规则
        rules: {
          code: [
            {
              required: true,
              message: this.$t('basics.pleaseInput'),
              trigger: 'blur'
            }
          ],
          addr_name: [
            {
              required: true,
              message: this.$t('basics.pleaseInput'),
              trigger: 'blur'
            }
          ],
          country: [
            {
              required: true,
              message: this.$t('basics.pleaseChoose'),
              trigger: ['blur', 'change']
            }
          ],
          type: [
            {
              required: true,
              message: this.$t('basics.pleaseChoose'),
              trigger: ['blur', 'change']
            }
          ],
          address: [
            {
              required: true,
              message: this.$t('basics.pleaseInput'),
              trigger: 'blur'
            }
          ]
        },
        // 提交状态
        loading: false,
        // 是否是修改
        isUpdate: false
      };
    },
    created() {
      if (!hasPermission('address:nouser')) {
        this.form.type = 1;
      }
    },
    methods: {
      /* 保存编辑 */
      save() {
        this.$refs['form'].validate((valid) => {
          if (!valid) {
            return false;
          }
          this.loading = true;
          const data = {
            ...this.form
          };
          const apiname = this.isUpdate ? update : create;
          apiname(data)
            .then(() => {
              this.loading = false;
              this.$message.success(this.$t('basics.success'));
              this.updateVisible(false);
              this.$emit('done');
            })
            .catch((e) => {
              this.loading = false;
              this.$message.error(e);
            });
        });
      },
      /* 更新visible */
      updateVisible(value) {
        this.$emit('update:visible', value);
      },
      /* 角色选择回调*/
      roleSelectCallback(value) {
        this.form.role_type = value.type;
      }
    },
    watch: {
      async visible(visible) {
        console.log(visible);
        if (visible) {
          let data = this.data;
          if (this.data) {
            this.$util.assignObject(this.form, {
              ...data
            });
            this.isUpdate = true;
          } else {
            this.isUpdate = false;
          }
        } else {
          this.$refs['form'].clearValidate();
          this.form = { ...DEFAULT_FORM };
        }
      }
    }
  };
</script>
