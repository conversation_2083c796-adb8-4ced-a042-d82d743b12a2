<!-- 编辑弹窗 -->
<template>
  <ele-modal
    width="1080px"
    :visible="visible"
    :close-on-click-modal="false"
    custom-class="ele-dialog-form"
    :title="this.$t('basics.financeSetting')"
    @update:visible="updateVisible"
  >
    <el-form ref="form" :model="form" label-width="117px">
      <el-row :gutter="15">
        <el-col :sm="12">
          <el-form-item
            :label="this.$t('basicsMenu.address.name') + ':'"
            prop="addr_name"
          >
            <el-input
              clearable
              :maxlength="50"
              show-word-limit
              v-model="form.addr_name"
              :disabled="true"
            />
          </el-form-item>
        </el-col>
        <el-col :sm="12">
          <el-form-item
            :label="this.$t('basicsMenu.address.name') + ':'"
            prop="code"
          >
            <el-input
              clearable
              :maxlength="50"
              show-word-limit
              v-model="form.code"
              :disabled="true"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="15">
        <el-col :sm="12">
          <el-form-item label="卸货费散货系数:" prop="truck_loading_loose">
            <el-input
              clearable
              :maxlength="50"
              show-word-limit
              v-model="form.truck_loading_loose"
              placeholder="请输入"
            />
          </el-form-item>
        </el-col>
        <el-col :sm="12">
          <el-form-item label="卸货费整板系数:" prop="truck_loading_uld">
            <el-input
              clearable
              :maxlength="50"
              show-word-limit
              v-model="form.truck_loading_uld"
              placeholder="请输入"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="15">
        <el-col :sm="12">
          <el-form-item label="托盘费:" prop="pallet">
            <el-input
              clearable
              :maxlength="50"
              show-word-limit
              v-model="form.pallet"
              placeholder="请输入"
            />
          </el-form-item>
        </el-col>
        <el-col :sm="12">
          <el-form-item label="放货费:" prop="customs_delivery">
            <el-input
              clearable
              :maxlength="50"
              show-word-limit
              v-model="form.customs_delivery"
              placeholder="请输入"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="15">
        <el-col :sm="12">
          <el-form-item label="能源附加费:" prop="energy_surcharges">
            <el-input
              clearable
              :maxlength="50"
              show-word-limit
              v-model="form.energy_surcharges"
              placeholder="请输入"
            />
          </el-form-item>
        </el-col>
        <el-col :sm="12">
          <el-form-item label="新冠附加费:" prop="covid">
            <el-input
              clearable
              :maxlength="50"
              show-word-limit
              v-model="form.covid"
              placeholder="请输入"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <!-- <el-row :gutter="15">
        <el-col :sm="12">
          <el-form-item label="T1代理:" prop="t1">
            <el-input
              clearable
              :maxlength="50"
              show-word-limit
              v-model="form.t1"
              :disabled="true"
            />
          </el-form-item>
        </el-col>
        <el-col :sm="12">
          <el-form-item label="航班港口:" prop="lgg">
            <el-input
              clearable
              :maxlength="50"
              show-word-limit
              v-model="form.lgg"
              :disabled="true"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="15">
        <el-col :sm="12">
          <el-form-item label="业务类型:" prop="b2b">
            <el-input
              clearable
              :maxlength="50"
              show-word-limit
              v-model="form.b2b"
              :disabled="true"
            />
          </el-form-item>
        </el-col>
        <el-col :sm="12">
          <el-form-item label="计费重量:" prop="gross_mass_kg">
            <el-input
              clearable
              :maxlength="50"
              show-word-limit
              v-model="form.gross_mass_kg"
              :disabled="true"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="15">
        <el-col :sm="12">
          <el-form-item label="换标数:" prop="exchange_flag">
            <el-input
              clearable
              :maxlength="50"
              show-word-limit
              v-model="form.exchange_flag"
              :disabled="true"
            />
          </el-form-item>
        </el-col>
        <el-col :sm="12">
          <el-form-item label="高价值数量:" prop="expensive">
            <el-input
              clearable
              :maxlength="50"
              show-word-limit
              v-model="form.expensive"
              :disabled="true"
            />
          </el-form-item>
        </el-col>
      </el-row> -->
    </el-form>
    <!-- <ele-pro-table
      ref="table"
      :initLoad="false"
      :columns="columns"
      :need-page="false"
      :datasource="datasource"
    >
    </ele-pro-table> -->
    <div slot="footer">
      <el-button @click="updateVisible(false)">
        {{ $t('basics.cancel') }}
      </el-button>
      <el-button type="primary" :loading="loading" @click="save">
        {{ $t('basics.save') }}
      </el-button>
    </div>
  </ele-modal>
</template>

<script>
  import { financeSetting } from '@/api/basics/address';

  const DEFAULT_FORM = {
    id: null,
    addr_name: '',
    code: '',
    truck_loading_uld: '',
    truck_loading_loose: '',
    pallet: '',
    energy_surcharges: '',
    customs_delivery: '',
    covid: ''
  };

  export default {
    name: 'EditAwb',
    components: {},
    props: {
      // 弹窗是否打开
      visible: Boolean,
      // 修改回显的数据
      data: Object
    },
    data() {
      return {
        // 表单数据
        form: { ...DEFAULT_FORM },
        columns: [
          {
            columnKey: 'index',
            type: 'index',
            width: 45,
            showOverflowTooltip: true,
            fixed: 'left'
          },
          {
            prop: 'name',
            label: '地址名称',
            showOverflowTooltip: true,
            minWidth: 110
          },
          {
            prop: 'num',
            label: '预报数',
            showOverflowTooltip: true,
            minWidth: 110
          },
          {
            prop: 'stock_in',
            label: '入库数',
            showOverflowTooltip: true,
            minWidth: 110
          },
          {
            prop: 'stock_out',
            label: '出库数',
            showOverflowTooltip: true,
            minWidth: 110
          }
        ],
        datasource: [],
        // 提交状态
        loading: false,
        // 是否是修改
        isUpdate: false
      };
    },
    async created() {},
    methods: {
      /* 保存编辑 */
      save() {
        this.$refs['form'].validate((valid) => {
          if (!valid) {
            return false;
          }
          this.loading = true;
          const data = {
            ...this.form
          };
          financeSetting(data)
            .then(() => {
              this.loading = false;
              this.$message.success(this.$t('basics.success'));
              this.updateVisible(false);
              this.$emit('done');
            })
            .catch((e) => {
              this.loading = false;
              this.$message.error(e);
            });
        });
      },
      /* 更新visible */
      updateVisible(value) {
        this.$emit('update:visible', value);
      },
      /* 角色选择回调*/
      roleSelectCallback(value) {
        this.form.role_type = value.type;
      }
    },
    watch: {
      async visible(visible) {
        if (visible) {
          // let a = await info(this.data)
          // console.log(a.result.address)
          // this.form = a.result
          // let data = this.data;
          if (this.data) {
            this.$util.assignObject(this.form, {
              ...this.data
            });
            // this.datasource = a.result.address
            // console.log(a.result.batch)
            console.log(this.form);
            this.isUpdate = true;
          } else {
            this.isUpdate = false;
          }
        } else {
          this.$refs['form'].clearValidate();
          this.form = { ...DEFAULT_FORM };
        }
      }
    }
  };
</script>
