<!-- 编辑弹窗 -->
<template>
  <ele-modal
    width="720px"
    :visible="visible"
    :close-on-click-modal="false"
    custom-class="ele-dialog-form"
    :title="this.$t('seal.seal_no')"
    @update:visible="updateVisible"
  >
    <el-form ref="form" :model="form" :rules="rules" label-width="130px">
      <el-row :gutter="15">
        <el-col :sm="12">
          <el-form-item :label="this.$t('seal.begin_num')+':'" prop="begin_num">
            <el-input
              :maxlength="100"
              v-model="form.begin_num"
            />
          </el-form-item>
        </el-col>
        <el-col :sm="12">
          <el-form-item :label="this.$t('seal.end_num')+':'" prop="end_num">
            <el-input
              :maxlength="100"
              v-model="form.end_num"
            />
          </el-form-item>
        </el-col>
        <el-col :sm="12">
          <el-form-item :label="this.$t('seal.pad_zero')+':'" prop="pad_zero">
            <el-input
              type="number"
              :maxlength="3"
              v-model="form.pad_zero"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div slot="footer">
      <el-button @click="updateVisible(false)">
        {{ $t('basics.cancel') }}
      </el-button>
      <el-button type="primary" :loading="loading" @click="save">
        {{ $t('basics.save') }}
      </el-button>
    </div>
  </ele-modal>
</template>

<script>
  import { create } from '@/api/basics/seal';

  const DEFAULT_FORM = {
    begin_num: '',
    end_num: '',
    pad_zero: ''
  };

  export default {
    name: 'UserEdit',
    components: { },
    props: {
      // 弹窗是否打开
      visible: Boolean,
      // 修改回显的数据
      data: Object
    },
    data() {
      return {
        // 表单数据
        form: { ...DEFAULT_FORM },
        // 表单验证规则
        rules: {
          begin_num: [
            {
              required: true,
              message: this.$t('basics.pleaseInput'),
              trigger: 'blur'
            }
          ],
          end_num: [
            {
              required: true,
              message: this.$t('basics.pleaseInput'),
              trigger: 'blur'
            }
          ]
        },
        // 提交状态
        loading: false,
        // 是否是修改
        isUpdate: false
      };
    },
    methods: {
      /* 保存编辑 */
      save() {
        this.$refs['form'].validate((valid) => {
          if (!valid) {
            return false;
          }
          this.loading = true;
          const data = {
            ...this.form
          };
          create(data)
            .then((msg) => {
              this.loading = false;
              this.$message.success(this.$t('basics.success'));
              this.updateVisible(false);
              this.$emit('done');
            })
            .catch((e) => {
              this.loading = false;
              this.$message.error(e);
            });
        });
      },
      /* 更新visible */
      updateVisible(value) {
        this.$emit('update:visible', value);
      },
      /* 角色选择回调*/
      roleSelectCallback(value) {
        this.form.role_type = value.type;
      }
    },
    watch: {
      async visible() {
        this.form = { ...DEFAULT_FORM };
      }
    }
  };
</script>
