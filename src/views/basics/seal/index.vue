<template>
  <div class="ele-body">
    <el-card shadow="never">
      <!-- 搜索表单 -->
      <search @search="reload" />
      <!-- 数据表格 -->
      <ele-pro-table
        ref="table"
        :initLoad="false"
        :columns="columns"
        :datasource="datasource"
        :selection.sync="selection"
      >
        <!-- 表头工具栏 -->
        <template slot="toolbar">
          <el-button
            v-permission="'seal:create'"
            size="small"
            type="primary"
            icon="el-icon-plus"
            class="ele-btn-icon"
            @click="openEdit()"
          >
            {{$t('basics.create')}}
          </el-button>
          <el-button
            size="small"
            icon="el-icon-download"
            class="ele-btn-icon"
            @click="exportXls"
            v-permission="'seal:export'"
          >
            {{ $t('basics.export') }}
          </el-button>
        </template>
      </ele-pro-table>
    </el-card>
    <port-edit :visible.sync="showEdit" :data="current" @done="reload" />
  </div>
</template>

<script>
  import { lists, expLists } from '@/api/basics/seal';
  import PortEdit from './components/edit';
  import Search from './components/outbound-fba-search';
  import {utils, writeFile} from "xlsx";

  export default {
    name: 'SystemUser',
    components: {
      Search,
      PortEdit
      // UserSearch,
      // UserEdit,
      // UserImport,
      // UserChangePassword
    },
    data() {
      return {
        // 表格列配置
        columns: [
          {
            columnKey: 'index',
            type: 'index',
            width: 45,
            showOverflowTooltip: true,
            fixed: 'left'
          },
          {
            prop: 'seal_no',
            label: this.$t('seal.seal_no'),
            showOverflowTooltip: true,
            minWidth: 110
          },
          {
            prop: 'cmr',
            label: 'CMR',
            showOverflowTooltip: true,
            minWidth: 110
          },
          {
            prop: 'status',
            label: this.$t('basics.status'),
            showOverflowTooltip: true,
            minWidth: 110,
            formatter: (row, column, cellValue) => {
              return this.$t(`seal.statusWords.s${cellValue}`);
            }
          },
          {
            prop: 'created_at',
            label: this.$t('basics.createTime'),
            showOverflowTooltip: true,
            minWidth: 110
          },
          {
            prop: 'use_at',
            label: this.$t('seal.use_at'),
            showOverflowTooltip: true,
            minWidth: 110
          }
        ],
        // 表格选中数据
        selection: [],
        // 当前编辑数据
        current: null,
        // 是否显示编辑弹窗
        showEdit: false,
        // 是否显示导入弹窗
        showImport: false,
        // 是否显修改密码
        userId: null,
        showChangePassword: false,
        lastWhere: {}
      };
    },
    created() {
      //或者登陆不显示货主名字其他登录显示名称
      if (
        this.$store.state.user.info.role_type === '2' ||
        this.$store.state.user.info.role_type === '3'
      ) {
        const index = this.columns.findIndex(function (key) {
          return key.prop === 'username';
        });
        this.columns.splice(index, index + 1);
      }
    },
    activated() {
      this.reload();
    },
    methods: {
      /* 表格数据源 */
      datasource({ page, limit, where, order }) {
        return lists({
          ...where,
          ...order,
          page: page,
          num: limit,
          // inbound_id: this.$route.query.inbound_id
        });
      },
      /* 刷新表格 */
      reload(where) {
        this.lastWhere = where;
        this.$refs.table.reload({ page: 1, where: where });
      },
      /* 导出SEAL */
      exportXls() {
        const array = [];
        array.push([
          this.$t('seal.seal_no'),
          'CMR',
          this.$t('basics.status')
          // this.$t('seal.use_at')
        ]);
        const status = ['', this.$t('seal.statusWords.s1'), this.$t('seal.statusWords.s2')];
        expLists({
          ...this.lastWhere
        }).then((res) => {
          res.result.forEach((d) => {
            array.push([
              d.seal_no,
              d.cmr_num,
              status[d.status]
              // d.use_at
            ]);
          });
          const sheetName = 'Sheet1';
          const workbook = {
            SheetNames: [sheetName],
            Sheets: {}
          };
          workbook.Sheets[sheetName] = utils.aoa_to_sheet(array);
          writeFile(workbook, 'SEAL.xlsx');
        });
      },
      /* 打开编辑弹窗 */
      openEdit(row) {
        this.current = row;
        this.showEdit = true;
      },
      /* 打开导入弹窗 */
      openImport() {
        this.showImport = true;
      },
      /* 删除 */
      cancel(row) {
        const loading = this.$loading({ lock: true });
        cancel(row.id)
          .then((msg) => {
            loading.close();
            this.$message.success(this.$t('basics.success'));
            this.reload();
          })
          .catch((e) => {
            loading.close();
            this.$message.error(e);
          });
      }
    }
  };
</script>
