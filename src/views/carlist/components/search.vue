<template>
  <el-card shadow="never" body-style="padding: 22px 22px 7px 22px;">
    <!-- 搜索表单 -->
    <el-form
      label-width="200px"
      class="ele-form-search"
      @keyup.enter.native="search"
      @submit.native.prevent
    >
      <el-row :gutter="15">
        <el-col :lg="8" :md="12">
          <el-form-item label="CMR:" prop="cmr">
            <el-input v-model="where.cmr" />
          </el-form-item>
        </el-col>
        <el-col :lg="8" :md="12">
          <el-form-item :label="`${$t('car.transfers')}:`">
            <el-input clearable show-word-limit v-model="where.logistics" />
          </el-form-item>
        </el-col>
        <el-col :lg="8" :md="12">
          <el-form-item :label="`${$t('car.type')}:`" prop="type">
            <el-select
              v-model="where.type"
              @change="clearWhere()"
              clearable
              class="ele-fluid"
            >
              <el-option :label="this.$t('car.in')" :value="1" />
              <el-option :label="this.$t('car.out')" :value="2" />
              <el-option :label="this.$t('car.air')" :value="3" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :lg="8" :md="12">
          <el-form-item :label="`${$t('car.cmrType')}:`" prop="cmr_type">
            <el-select v-model="where.cmr_type" clearable class="ele-fluid">
              <el-option :label="this.$t('car.cmrTypes.dispatch')" :value="1" />
              <el-option :label="this.$t('car.cmrTypes.self')" :value="2" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :lg="8" :md="12">
          <el-form-item
            :label="`${$t('basics.confirm')}:`"
            prop="finance_confirm"
          >
            <el-select
              v-model="where.finance_confirm"
              clearable
              class="ele-fluid"
            >
              <el-option :label="this.$t('basics.no')" :value="1" />
              <el-option :label="this.$t('basics.yes')" :value="2" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :lg="8" :md="12">
          <el-form-item
            :label="`${$t('car.uploadVehicleList')}:`"
            prop="finance_confirm"
          >
            <el-select v-model="where.is_cmr" clearable class="ele-fluid">
              <el-option :label="this.$t('basics.no')" :value="1" />
              <el-option :label="this.$t('basics.yes')" :value="2" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :lg="8" :md="12">
          <el-form-item
            :label="`${$t('car.uploadvoucher')}:`"
            prop="finance_confirm"
          >
            <el-select v-model="where.is_voucher" clearable class="ele-fluid">
              <el-option :label="this.$t('basics.no')" :value="1" />
              <el-option :label="this.$t('basics.yes')" :value="2" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :lg="8" :md="12">
          <el-form-item :label="`${$t('car.btn2')}:`" prop="finance_confirm">
            <el-select v-model="where.is_set_pay" clearable class="ele-fluid">
              <el-option :label="this.$t('basics.no')" :value="1" />
              <el-option :label="this.$t('basics.yes')" :value="2" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :lg="8" :md="12">
          <el-form-item :label="$t('car.timeWords.in3') + ':'" prop="time3">
            <el-date-picker
              :disabled="where.type != 1"
              v-model="where.time3"
              type="datetimerange"
              value-format="yyyy-MM-dd HH:mm:ss"
              :range-separator="$t('basics.to')"
              class="ele-fluid"
              :start-placeholder="$t('basics.beginTime')"
              :end-placeholder="$t('basics.endTime')"
            >
            </el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :lg="8" :md="12">
          <el-form-item :label="$t('car.timeWords.out2') + ':'" prop="time2">
            <el-date-picker
              :disabled="where.type != 2"
              v-model="where.time2"
              type="datetimerange"
              value-format="yyyy-MM-dd HH:mm:ss"
              :range-separator="$t('basics.to')"
              class="ele-fluid"
              :start-placeholder="$t('basics.beginTime')"
              :end-placeholder="$t('basics.endTime')"
            >
            </el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :lg="8" :md="12">
          <el-form-item :label="`${$t('carList.order_num')}:`">
            <el-input
              clearable
              show-word-limit
              v-model="where.order_num"
              :disabled="where.type == 3"
            />
          </el-form-item>
        </el-col>
        <el-col :lg="8" :md="12">
          <el-form-item :label="`${$t('batch.car.truck_order')}:`">
            <el-input
              :disabled="where.type != 2"
              clearable
              show-word-limit
              v-model="where.truck_order"
            />
          </el-form-item>
        </el-col>
        <el-col :lg="8" :md="12">
          <el-form-item
            :label="`${$t('batch.car.plate_number')}:`"
            prop="plate_number"
          >
            <el-input clearable show-word-limit v-model="where.plate_number" />
          </el-form-item>
        </el-col>
        <el-col :lg="8" :md="12">
          <el-form-item
            :label="`${$t('car.invoiceNo')}:`"
            prop="invoice_number"
          >
            <el-input
              clearable
              show-word-limit
              v-model="where.invoice_number"
            />
          </el-form-item>
        </el-col>
        <el-col :lg="8" :md="12">
          <el-form-item :label="`${$t('car.address')}:`">
            <!--<address-select v-model="where.address_code" />-->
            <el-select
              clearable
              multiple
              filterable
              v-model="where.address_codes"
              class="ele-fluid"
              @change="$forceUpdate()"
            >
              <el-option
                v-for="{ code, id, addr_name } in addressList"
                :key="id"
                :value="code"
                :label="addr_name"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :lg="8" :md="12">
          <div class="ele-form-actions">
            <el-button
              type="primary"
              icon="el-icon-search"
              class="ele-btn-icon"
              @click="search"
            >
              {{ $t('basics.query') }}
            </el-button>
            <el-button @click="reset">{{ $t('basics.reset') }}</el-button>
          </div>
        </el-col>


      </el-row>
     
    </el-form>
  </el-card>
</template>

<script>
  // import AddressSelect from '@/components/AddressSelect';
  import { lists } from '@/api/basics/address';
  // 默认表单数据

  const DEFAULT_WHERE = {
    cmr: '',
    address_codes: [],
    logistics: '',
    order_num: '',
    truck_order: '',
    invoice_number: '',
    type: null,
    finance_confirm: null,
    cmr_type: null,
    is_cmr: null,
    is_voucher: null,
    time2: null,
    time3: null,
    is_set_pay: null,
    plate_number: null
  };

  export default {
    name: 'outbound-fba-search',
    components: {},
    data() {
      return {
        // 表单数据
        where: { ...DEFAULT_WHERE },
        // 搜索表单是否展开
        searchExpand: false,
        goodsCategoryList: [],
        addressList: [],
        category_id: ''
      };
    },
    activated() {
      lists({
        page: 1,
        status: 2,
        num: 10000
      }).then((res) => {
        this.addressList = res.list;
      });
      this.where.cmr = this.$route.query.cmr ? this.$route.query.cmr : '';
      if (this.where.cmr.length > 0) {
        this.search();
      }
    },
    methods: {
      /* 搜索 */
      search() {
        //解决搜索 也搜索下级的数据
        this.$emit('search', this.where);
      },
      clearWhere() {
        this.where.order_num = '';
        this.where.time2 = null;
        this.where.time3 = null;
      },
      /* 重置搜索 */
      reset() {
        this.where = { ...DEFAULT_WHERE };
        this.search();
      },
      /* 搜索展开/收起 */
      toggleExpand() {
        this.searchExpand = !this.searchExpand;
        this.$emit('expand-change', this.searchExpand);
      },
      /* 搜索展开/收起 */
      goodsCategoryFilterListClick(e) {
        this.goodsCategoryList = e.data;
      }
    }
  };
</script>
