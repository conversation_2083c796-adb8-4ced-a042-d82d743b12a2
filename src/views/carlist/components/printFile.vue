<!-- 编辑弹窗 -->
<template>
  <ele-modal
    width="540px"
    :visible="visible"
    :close-on-click-modal="false"
    custom-class="ele-dialog-form"
    :title="this.$t('car.print')"
    @update:visible="updateVisible"
  >
    <el-form ref="form" :model="form" label-width="135px">
      <el-row :gutter="15">
        <el-col :sm="20">
          <el-form-item label="CMR:" prop="cmr">
            <el-input
              clearable
              :maxlength="50"
              show-word-limit
              v-model="form.cmr"
              :disabled="true"
            />
          </el-form-item>
        </el-col>
        <el-col :sm="20">
          <el-form-item :label="`${$t('car.addressOut')}:`" prop="address_code">
            <address-select v-model="form.address_code" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <div slot="footer">
      <el-button @click="save(1)">
        {{ $t('car.print_cmr') }}
      </el-button>
      <!-- <el-button @click="updateVisible(false)">
        {{ $t('basics.cancel') }}
      </el-button> -->
      <el-button
        type="primary"
        @click="save(2)"
      >
        {{ $t('car.print_pdf') }}
      </el-button>
    </div>
    <!-- <awb-info :visible.sync="showEditOut" :data="current" /> -->
  </ele-modal>
</template>

<script>
  import AddressSelect from '@/components/AddressSelect';
  import {API_BASE_URL} from "@/config/setting";
  const DEFAULT_FORM = {
    id: null,
    address_code: '',
    cmr: ''
  };
  export default {
    name: 'EditAwb',
    components: { AddressSelect},
    props: {
      // 弹窗是否打开
      visible: Boolean,
      // 修改回显的数据
      data: Object
    },
    data() {
      return {
        // 表单数据
        form: { ...DEFAULT_FORM },
        current: {},
        loading: false,
        // 是否是修改
        isUpdate: false
      };
    },
    methods: {
      /* 保存编辑 */
      save(val) {
        if (val == 1) {
          let url = API_BASE_URL + "printCmr?id="+this.form.id+"&address_code="+this.form.address_code;
          window.open(url);
        } else {
          let url = API_BASE_URL + "printPdf?id="+this.form.id+"&address_code="+this.form.address_code;
          window.open(url);
        }
      },
      /* 更新visible */
      updateVisible(value) {
        this.$emit('update:visible', value);
      }
    },
    watch: {
      async visible(visible) {
        // console.log(this.data)
        if (visible) {
          let data = JSON.parse(JSON.stringify(this.data));
          if (this.data) {
            this.$util.assignObject(this.form, {
              ...data
            });
          }
        } else {
          this.$refs['form'].clearValidate();
          this.form = { ...DEFAULT_FORM };
        }
      }
    }
  };
</script>
