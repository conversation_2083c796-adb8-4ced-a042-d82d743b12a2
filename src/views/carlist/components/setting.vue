<!-- 编辑弹窗 -->
<template>
  <ele-modal
    width="720px"
    :visible="visible"
    :close-on-click-modal="false"
    custom-class="ele-dialog-form"
    :title="this.$t('car.btn2')"
    @update:visible="updateVisible"
  >
    <el-form ref="form" :model="form" :rules="rules" label-width="135px">
      <el-row>
        <el-col :span="12">
          <el-form-item :label="`${$t('car.pincode_ref')}:`" prop="pincode_ref">
            <el-input v-model="form.pincode_ref" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item
            :label="`${$t('car.invoiceNo')}:`"
            prop="invoice_number"
          >
            <el-input v-model="form.invoice_number" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="`${$t('car.deliveryFee')}:`" prop="free">
            <el-input v-model="form.free" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item
            :label="`${$t('car.additionalCosts')}:`"
            prop="extra_charge"
          >
            <el-input v-model="form.extra_charge" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div slot="footer">
      <el-button @click="updateVisible(false)">
        {{ $t('basics.cancel') }}
      </el-button>
      <el-button type="primary" :loading="loading" @click="save">
        {{ $t('basics.save') }}
      </el-button>
    </div>
  </ele-modal>
</template>

<script>
  import { settingPay } from '@/api/list/carlist';

  const DEFAULT_FORM = {
    id: null,
    free: '',
    invoice_number: '',
    extra_charge: '',
    pincode_ref: ''
  };

  export default {
    name: 'UserEdit',
    components: {},
    props: {
      visible: Boolean,
      data: Array
    },
    data() {
      return {
        // 表单数据
        form: { ...DEFAULT_FORM },
        // 表单验证规则
        rules: {
          pincode_ref: [
            {
              required: true,
              message: this.$t('basics.pleaseInput'),
              trigger: 'blur'
            }
          ],
          invoice_number: [
            {
              required: true,
              message: this.$t('basics.pleaseInput'),
              trigger: 'blur'
            }
          ],
          free: [
            {
              required: true,
              message: this.$t('basics.pleaseInput'),
              trigger: 'blur'
            }
          ],
          extra_charge: [
            {
              required: true,
              message: this.$t('basics.pleaseInput'),
              trigger: 'blur'
            }
          ]
        },
        // 提交状态
        loading: false,
        // 是否是修改
        isUpdate: false
      };
    },
    methods: {
      /* 保存编辑 */
      save() {
        this.$refs['form'].validate((valid) => {
          if (!valid) {
            return false;
          }
          this.loading = true;
          let temp = [];
          for (let item of this.data) {
            temp.push(item.id);
          }
          const data = {
            cmr_id: temp,
            free: this.form.free,
            invoice_number: this.form.invoice_number,
            extra_charge: this.form.extra_charge,
            pincode_ref: this.form.pincode_ref
          };
          settingPay(data)
            .then((msg) => {
              this.loading = false;
              this.$message.success(this.$t('basics.success'));
              this.updateVisible(false);
              this.$emit('done');
            })
            .catch((e) => {
              this.loading = false;
              this.$message.error(e);
            });
        });
      },
      /* 更新visible */
      updateVisible(value) {
        this.$emit('update:visible', value);
      },
      /* 角色选择回调*/
      roleSelectCallback(value) {
        this.form.role_type = value.type;
      }
    },
    watch: {
      async visible(visible) {
        if (visible) {
          if (this.data) {
            this.isUpdate = true;
            // this.data 按照id排序
            let sort_data = this.data.slice().sort((a, b) => b.id - a.id);
            console.log('排序后', sort_data);
            if (sort_data.length > 0) {
              this.form.pincode_ref = sort_data[0].truck_order;
            }
          } else {
            this.isUpdate = false;
          }
        } else {
          this.$refs['form'].clearValidate();
          this.form = { ...DEFAULT_FORM };
        }
      }
    }
  };
</script>
