<!-- 编辑弹窗 -->
<template>
  <ele-modal
    width="1080px"
    :visible="visible"
    v-loading="modalLoading"
    :close-on-click-modal="false"
    custom-class="ele-dialog-form"
    :title="isUpdate ? this.$t('basics.detail') : this.$t('basics.edit')"
    @update:visible="updateVisible"
  >
    <el-form ref="form" :model="form" :rules="rules" label-width="220px">
      <el-row :gutter="15">
        <el-col :sm="12">
          <el-form-item label="CMR:" prop="cmr">
            <el-input
              clearable
              :disabled="isUpdate"
              :maxlength="50"
              show-word-limit
              v-model="form.cmr"
            />
          </el-form-item>
        </el-col>
        <el-col :sm="12">
          <el-form-item
            :label="this.$t('car.addressOut') + ':'"
            prop="address_code"
          >
            <address-select
              v-model="form.address_code"
              v-if="!isUpdate"/>
            <!-- @addressClick="addressChange" -->
            <el-input v-else v-model="form.address_code" :disabled="isUpdate" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="15">
        <el-col :sm="12">
          <el-form-item
            :label="this.$t('car.transfers') + ':'"
            prop="logistics"
          >
            <transfers-select v-model="form.logistics" v-if="!isUpdate" />
            <el-input v-else v-model="form.logistics" :disabled="isUpdate" />
          </el-form-item>
        </el-col>
        <el-col :sm="12">
          <el-form-item
            :label="this.$t('basics.createTime') + ':'"
            prop="created_at"
          >
            <el-input
              clearable
              :maxlength="50"
              show-word-limit
              :disabled="true"
              v-model="form.created_at"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="15">
        <el-col :sm="12">
          <el-form-item :label="`${$t('car.timeWords.air1')}:`" prop="time1">
            <el-date-picker
              :disabled="isUpdate"
              v-model="form.time1"
              type="datetime"
              :placeholder="this.$t('basics.placeholder.time')"
              value-format="yyyy-MM-dd HH:mm:ss"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :sm="12">
          <el-form-item :label="`${$t('car.timeWords.air2')}:`" prop="time2">
            <el-date-picker
              :disabled="isUpdate"
              v-model="form.time2"
              type="datetime"
              :placeholder="this.$t('basics.placeholder.time')"
              value-format="yyyy-MM-dd HH:mm:ss"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="15">
        <el-col :sm="12">
          <el-form-item :label="`${$t('car.timeWords.air3')}:`" prop="time3">
            <el-date-picker
              :disabled="isUpdate"
              v-model="form.time3"
              type="datetime"
              :placeholder="this.$t('basics.placeholder.time')"
              value-format="yyyy-MM-dd HH:mm:ss"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :sm="12">
          <el-form-item :label="`${$t('car.timeWords.air4')}:`" prop="time4">
            <el-date-picker
              :disabled="isUpdate"
              v-model="form.time4"
              type="datetime"
              :placeholder="this.$t('basics.placeholder.time')"
              value-format="yyyy-MM-dd HH:mm:ss"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="15">
        <el-col :sm="12">
          <el-form-item
            :label="`${$t('batch.car.plate_number')}:`"
            prop="plate_number"
          >
            <el-input
              clearable
              :maxlength="50"
              :disabled="isUpdate"
              show-word-limit
              v-model="form.plate_number"
            />
          </el-form-item>
        </el-col>
        <el-col :sm="12">
          <el-form-item :label="`${$t('car.cmrType')}:`" prop="cmr_type">
            <el-select
              v-model="form.cmr_type"
              class="ele-fluid"
              :disabled="isUpdate"
            >
              <el-option :label="this.$t('car.cmrTypes.dispatch')" :value="1" />
              <el-option :label="this.$t('car.cmrTypes.self')" :value="2" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="15">
        <el-col :sm="12">
          <el-form-item :label="`${$t('seal.seal_no')}:`" prop="cmr_type">
            <el-select
              clearable
              multiple
              filterable
              v-model="form.seal_no"
              class="ele-fluid"
              @change="$forceUpdate()"
              :disabled="isUpdate"
            >
              <el-option
                v-for="item in seals"
                :key="item.id"
                :value="item.id"
                :label="item.seal_no"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :sm="12" v-if="isUpdate && otherInfo.is_set_pay == 2">
          <el-form-item :label="`${$t('car.pincode_ref')}:`">
            <el-input
              clearable
              :maxlength="50"
              :disabled="isUpdate"
              show-word-limit
              v-model="otherInfo.pincode_ref"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="15" v-if="isUpdate && otherInfo.is_set_pay == 2">
        <el-col :sm="12">
          <el-form-item :label="`${$t('car.invoiceNo')}:`">
            <el-input
              clearable
              :maxlength="50"
              :disabled="isUpdate"
              show-word-limit
              v-model="otherInfo.invoice_number"
            />
          </el-form-item>
        </el-col>
        <el-col :sm="12">
          <el-form-item :label="`${$t('car.deliveryFee')}:`">
            <el-input
              clearable
              :maxlength="50"
              :disabled="isUpdate"
              show-word-limit
              :value="otherInfo.delivery_cost_show"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="15" v-if="isUpdate && otherInfo.is_set_pay == 2">
        <el-col :sm="12">
          <el-form-item :label="`${$t('car.additionalCosts')}:`">
            <el-input
              clearable
              :maxlength="50"
              :disabled="isUpdate"
              show-word-limit
              v-model="otherInfo.extra_charge_show"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <aviation-board
      v-if="!isUpdate"
      ref="aviationBoardRef"
      :addressCode="form.address_code"
      :cmr_id="form.id"
    ></aviation-board>
    <ele-pro-table
      v-else
      ref="table"
      :columns="columns"
      :need-page="false"
      :datasource="form.other"
    >
    </ele-pro-table>
    <div slot="footer">
      <!-- <el-button @click="updateVisible(false)">
        {{ $t('basics.cancel') }}
      </el-button> -->
      <el-button
        type="primary"
        :loading="loading"
        :disabled="isUpdate"
        @click="save"
      >
        {{ $t('basics.save') }}
      </el-button>
    </div>
    <!-- <awb-info :visible.sync="showEditOut" :data="current" /> -->
  </ele-modal>
</template>

<script>
  import { update } from '@/api/list/carlist';
  import AddressSelect from '@/components/AddressSelect';
  import TransfersSelect from '@/components/TransfersSelect';
  // import AirSelect from '@/components/AirSelect';
  import AviationBoard from '@/views/carlist/components/aviation-board';
  import {allLists} from "@/api/basics/seal";
  // import { AwbInfo } from './add'

  const DEFAULT_FORM = {
    id: null,
    address_code: '',
    cmr: '',
    created_at: '',
    logistics: '',
    time1: '',
    time2: '',
    time3: '',
    time4: '',
    box_count: null,
    finance_confirm: null,
    parcel_count: null,
    type: null,
    cmr_type: null,
    plate_number: null,
    other: []
  };
  const DEFAULT_OTH = {
    is_set_pay: 1,
    pincode_ref: '',
    invoice_number: '',
    delivery_cost: '',
    extra_charge: '',
    original_extra_charge: '',
    original_delivery_cost: '',
    delivery_cost_show: '',
    extra_charge_show: ''
  };
  export default {
    name: 'EditAwb',
    components: { AviationBoard, AddressSelect, TransfersSelect },
    props: {
      // 弹窗是否打开
      visible: Boolean,
      // 修改回显的数据
      data: Object
    },
    data() {
      return {
        columns: [
          {
            columnKey: 'air_no',
            prop: 'air_no',
            label: this.$t('car.airno'),
            minWidth: 220,
            align: 'center',
            resizable: false
          }
        ],
        datasource: [],
        words: {
          time1: '到达仓库时间:',
          time2: '离开仓库时间:',
          time3: '到达货站时间:',
          time4: '离开货站时间:'
        },
        // 表单数据
        form: { ...DEFAULT_FORM },
        otherInfo: { ...DEFAULT_OTH },
        seals: [],
        // 表单验证规则
        rules: {
          // time2: [
          //   {
          //     required: true,
          //     message: '请输入地址名称',
          //     trigger: 'blur'
          //   }
          // ],
          // time3: [
          //   {
          //     required: true,
          //     message: '请输入地址名称',
          //     trigger: 'blur'
          //   }
          // ],
        },
        current: {},
        showEditOut: false,
        // 提交状态
        loading: false,
        modalLoading: false,
        // 是否是修改
        isUpdate: false
      };
    },
    created() {
    },
    activated() {
    },
    methods: {
      async getSeals(cmr_id) {
        allLists({ cmr_id: cmr_id }).then((res) => {
          this.seals = res.result.list;
          this.form.seal_no = res.result.cmr_seal;
          console.log(this.seals);
        });
      },
      /* 保存编辑 */
      save() {
        this.$refs['form'].validate((valid) => {
          if (!valid) {
            return false;
          }
          const selectedData = this.$refs.aviationBoardRef.selectedData;
          // if(!this.datasource.length>0){
          //   this.$message.success('请填写完整提单信息')
          //   return
          // }
          console.log(selectedData);
          // if (selectedData.length > 0) {
            let result = [];
            selectedData.forEach((item) => {
              result = selectedData.filter((it) => it.air_no === item.air_no);
            });
            if (result.length > 1) {
              this.$message.success(this.$t('basics.err.tips2'));
              return;
            }
            // for(let item of this.datasource){
            //   let index = tempArr[item].address.findIndex(item => {
            //     return item.code == arr[i].code
            //   })
            // }
          /* } else {
            this.$message.success(this.$t('basics.err.tips3'));
            return;
          } */
          this.loading = true;
          const data = {
            ...this.form,
            params: selectedData.map((i) => i.air_no)
          };
          console.log(selectedData);
          update(data)
            .then(() => {
              this.loading = false;
              this.$message.success(this.$t('basics.success'));
              this.updateVisible(false);
              this.$emit('done');
            })
            .catch((e) => {
              this.loading = false;
              this.$message.error(e);
            });
        });
      },
      add() {
        let lastArr = this.datasource.slice(-1);
        if (lastArr.length > 0) {
          if (lastArr[0].awb == '') {
            this.$message.success(this.$t('basics.err.tips3'));
            return;
          }
        }
        this.datasource.push({
          awb: ''
        });
      },
      /**
       * 地址选择回调
       *
       * */
      addressChange(row) {
        console.log("addressChange");
        this.modalLoading = true;
        console.log(row);
        console.log(this.$refs);
        if (row == '' || !row) {
          this.datasource = [];
          this.modalLoading = false;
          this.$refs.aviationBoardRef.reload(null);
          return false;
        }
        this.$refs.aviationBoardRef.reload(row);
        // let { list } = await airlist({
        //   page: 1,
        //   num: 10000,
        //   status: 1,
        //   address_code: row
        // });
        // //渲染air_no
        // this.datasource = list.map((i) => {
        //   return {
        //     awb: i.air_no
        //   };
        // });
        this.modalLoading = false;
      },
      deleteAwb(row, index) {
        console.log(index);
        this.datasource.splice(index, 1);
      },
      famtter(arr) {
        let temp = [];
        for (let i = 0; i < arr.length; i++) {
          temp.push(arr[i].awb);
        }
        return temp;
      },
      /* 更新visible */
      updateVisible(value) {
        this.$emit('update:visible', value);
      },
      /* 角色选择回调*/
      roleSelectCallback(value) {
        this.form.role_type = value.type;
      }
    },
    watch: {
      async visible(visible) {
        console.log(this.data);
        if (visible) {
          let data = JSON.parse(JSON.stringify(this.data));
          this.getSeals(data.id);
          if (this.data) {
            console.log(data?.other);
            console.log(this.$refs);
            if (!this.data.isUpdate) {
              setTimeout(() => {
                this.$refs.aviationBoardRef.reload("",
                  data?.other?.map((i) => {
                    return { air_no: i.air_no };
                  }));
              }, 500);
            }
            this.$util.assignObject(this.form, {
              ...data
            });
            this.$util.assignObject(this.otherInfo, {
              ...data
            });
            this.otherInfo.delivery_cost_show =
              this.otherInfo.delivery_cost +
              ' / ' +
              this.otherInfo.original_delivery_cost;
            this.otherInfo.extra_charge_show =
              this.otherInfo.extra_charge +
              ' / ' +
              this.otherInfo.original_extra_charge;
            if (this.data.isUpdate) {
              this.isUpdate = true;
            } else {
              this.isUpdate = false;
            }
          }
        } else {
          this.$refs['form'].clearValidate();
          this.form = { ...DEFAULT_FORM };
        }
      }
    }
  };
</script>
