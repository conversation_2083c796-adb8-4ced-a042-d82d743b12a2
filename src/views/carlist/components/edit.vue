<!-- 编辑弹窗 -->
<template>
  <ele-modal
    width="1080px"
    :visible="visible"
    :close-on-click-modal="false"
    custom-class="ele-dialog-form"
    :title="isUpdate ? this.$t('basics.detail') : this.$t('basics.edit')"
    @update:visible="updateVisible"
  >
    <el-form ref="form" :model="form" :rules="rules" label-width="220px">
      <el-row :gutter="15">
        <el-col :sm="12">
          <el-form-item label="CMR:" prop="cmr">
            <el-input
              clearable
              :disabled="isUpdate"
              :maxlength="50"
              show-word-limit
              v-model="form.cmr"
            />
          </el-form-item>
        </el-col>
        <el-col :sm="12">
          <el-form-item
            :label="this.$t('car.addressIn') + ':'"
            prop="address_code"
          >
            <!-- <el-input
              clearable
              :maxlength="50"
              show-word-limit
              v-model="form.address_code"
              placeholder="请输入地址名称"
            /> -->
            <address-select
              v-model="form.address_code"
              v-if="!isUpdate"
              :searchType="2"
            />
            <el-input v-else v-model="form.address_code" :disabled="isUpdate" />
          </el-form-item>
        </el-col>
      </el-row>
      <!-- <el-row :gutter="15">
        <el-col :sm="12">
          <el-form-item label="包裹数:" prop="parcel_count">
            <el-input
              clearable
              show-word-limit
              :disabled="isUpdate"
              v-model="form.parcel_count"
              placeholder="请输入包裹数"
            />
          </el-form-item>
        </el-col>
        <el-col :sm="12">
          <el-form-item label="箱数:" prop="box_count">
            <el-input
              clearable
              show-word-limit
              :disabled="isUpdate"
              v-model="form.box_count"
              placeholder="请输入箱数"
            />
          </el-form-item>
        </el-col>
      </el-row> -->
      <el-row :gutter="15">
        <el-col :sm="12">
          <el-form-item
            :label="this.$t('car.transfers') + ':'"
            prop="logistics"
          >
            <!-- <el-input
              clearable
              :maxlength="100"
              show-word-limit
              :disabled="isUpdate"
              v-model="form.logistics"
              placeholder="请输入卡车公司"
            /> -->
            <transfers-select v-model="form.logistics" v-if="!isUpdate" />
            <el-input v-else v-model="form.logistics" :disabled="isUpdate" />
          </el-form-item>
        </el-col>
        <el-col :sm="12">
          <el-form-item
            :label="this.$t('basics.createTime') + ':'"
            prop="created_at"
          >
            <el-input
              clearable
              :maxlength="50"
              show-word-limit
              :disabled="true"
              v-model="form.created_at"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="15">
        <el-col :sm="12">
          <el-form-item :label="`${$t('car.timeWords.in1')}:`" prop="time1">
            <el-date-picker
              v-model="form.time1"
              type="datetime"
              :disabled="isUpdate"
              :placeholder="this.$t('basics.placeholder.time')"
              value-format="yyyy-MM-dd HH:mm:ss"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :sm="12">
          <el-form-item :label="`${$t('car.timeWords.in2')}:`" prop="time2">
            <el-date-picker
              v-model="form.time2"
              type="datetime"
              :disabled="isUpdate"
              :placeholder="this.$t('basics.placeholder.time')"
              value-format="yyyy-MM-dd HH:mm:ss"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="15">
        <el-col :sm="12">
          <el-form-item :label="`${$t('car.timeWords.in3')}:`" prop="time3">
            <el-date-picker
              v-model="form.time3"
              type="datetime"
              :disabled="isUpdate"
              :placeholder="this.$t('basics.placeholder.time')"
              value-format="yyyy-MM-dd HH:mm"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :sm="12">
          <el-form-item :label="`${$t('car.timeWords.in4')}:`" prop="time4">
            <el-date-picker
              v-model="form.time4"
              type="datetime"
              :disabled="isUpdate"
              :placeholder="this.$t('basics.placeholder.time')"
              value-format="yyyy-MM-dd HH:mm:ss"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="15">
        <el-col :sm="12">
          <el-form-item label="T1:" prop="t1">
            <el-input
              clearable
              :maxlength="50"
              :disabled="isUpdate"
              show-word-limit
              v-model="form.t1"
            />
          </el-form-item>
        </el-col>
        <el-col :sm="12">
          <el-form-item :label="`${$t('car.cmrType')}:`" prop="cmr_type">
            <el-select
              v-model="form.cmr_type"
              class="ele-fluid"
              :disabled="isUpdate"
            >
              <el-option :label="this.$t('car.cmrTypes.dispatch')" :value="1" />
              <el-option :label="this.$t('car.cmrTypes.self')" :value="2" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="15">
        <el-col :sm="12">
          <el-form-item
            :label="`${$t('batch.car.plate_number')}:`"
            prop="plate_number"
          >
            <el-input
              clearable
              :maxlength="50"
              :disabled="isUpdate"
              show-word-limit
              v-model="form.plate_number"
            />
          </el-form-item>
        </el-col>
        <el-col :sm="12">
          <el-form-item :label="`${$t('seal.seal_no')}:`" prop="cmr_type">
            <el-select
              clearable
              multiple
              filterable
              v-model="form.seal_no"
              class="ele-fluid"
              @change="$forceUpdate()"
              :disabled="isUpdate"
            >
              <el-option
                v-for="item in seals"
                :key="item.id"
                :value="item.id"
                :label="item.seal_no"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="15" v-if="isUpdate && otherInfo.is_set_pay == 2">
        <el-col :sm="12">
          <el-form-item :label="`${$t('car.pincode_ref')}:`">
            <el-input
              clearable
              :maxlength="50"
              :disabled="isUpdate"
              show-word-limit
              v-model="otherInfo.pincode_ref"
            />
          </el-form-item>
        </el-col>
        <el-col :sm="12">
          <el-form-item :label="`${$t('car.invoiceNo')}:`">
            <el-input
              clearable
              :maxlength="50"
              :disabled="isUpdate"
              show-word-limit
              v-model="otherInfo.invoice_number"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="15" v-if="isUpdate && otherInfo.is_set_pay == 2">
        <el-col :sm="12">
          <el-form-item :label="`${$t('car.deliveryFee')}:`">
            <el-input
              clearable
              :maxlength="50"
              :disabled="isUpdate"
              show-word-limit
              :value="otherInfo.delivery_cost_show"
            />
          </el-form-item>
        </el-col>
        <el-col :sm="12">
          <el-form-item :label="`${$t('car.additionalCosts')}:`">
            <el-input
              clearable
              :maxlength="50"
              :disabled="isUpdate"
              show-word-limit
              v-model="otherInfo.extra_charge_show"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="15">
        <el-col :sm="12">
          <el-form-item
            :label="`${$t('car.addressReturn')}:`"
            prop="return_address"
          >
            <address-select
              v-model="form.return_address"
              :searchType="2"
              v-if="!isUpdate"
            />
            <el-input
              v-else
              v-model="form.return_address"
              :disabled="isUpdate"
            />
            <!-- <el-input
              clearable
              :maxlength="150"
              v-model="form.return_address"
            /> -->
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <ele-pro-table
      ref="table"
      :columns="columns"
      :need-page="false"
      :datasource="datasource"
    >
      <template slot="awb" slot-scope="{ row }">
        <!-- <el-input v-model="row.awb" :disabled="isUpdate"/> -->
        <awb-select-no v-model="row.awb" v-if="!row.id" />
        <el-input v-else v-model="row.awb" :disabled="true" />
      </template>
      <template slot="code" slot-scope="{ row }">
        <!-- <el-input v-model="row.code"/> -->
        <!-- <address-select
          v-model="row.code"
          v-if="!isUpdate"
        />
        <el-input v-else v-model="row.code" :disabled="isUpdate"/> -->
      </template>
      <template slot="trag" slot-scope="{ row }">
        <el-input v-model="row.trag" :disabled="isUpdate" />
      </template>
      <!-- <template slot="t1" slot-scope="{ row }">
        <el-input v-model="row.t1" :disabled="isUpdate"/>
      </template> -->
      <template slot="type" slot-scope="{ row }">
        <!-- <el-input v-model="row.type"/> -->
        <mode-select
          v-model="row.type"
          v-if="!isUpdate"
          @callback="roleSelectCallback(row)"
        />
        <el-input v-else v-model="row.type" :disabled="isUpdate" />
      </template>
      <template slot="PMCnum" slot-scope="{ row, $index }">
        <!-- <el-input v-model="row.PMCnum" :disabled="isUpdate" placeholder="多个航空板号用';'隔开"/> -->
        <!-- <el-tag
          v-for="tag in row.PMCnum"
          :key="tag"
          closable>
          {{tag}}
        </el-tag> -->
        <template v-for="(tag, index) in row.PMCnum">
          <el-tag
            v-if="!editable[$index + '_' + index]"
            :key="tag"
            closable
            :disable-transitions="true"
            @close="handleClose(row.PMCnum, tag)"
            @click="showEditTagInput(tag, index, $index)"
          >
            {{ tag }}
          </el-tag>
          <el-input
            v-else
            class="input-new-tag"
            :key="index"
            v-model="row.PMCnum[index]"
            size="small"
            show-word-limit
            style="margin-right: 15px"
            :id="'editableInput' + $index + '_' + index"
            @blur="handleEditableInputBlur(tag, index, $index)"
            @change="handleEditableInputConfirm(tag, index, $index)"
            @keyup.enter.native="handleEditableInputConfirm(tag, index, $index)"
          ></el-input>
        </template>
        <el-input
          class="input-new-tag"
          id="add-new-tag"
          v-if="row.inputVisible"
          v-model="row.inputValue"
          ref="saveTagInput"
          size="small"
          @keyup.enter.native="handleInputConfirm(row)"
          @blur="handleInputConfirm(row)"
        >
        </el-input>
        <el-button
          v-else
          class="button-new-tag"
          size="small"
          @click="showInput(row)"
        >
          +
        </el-button>
      </template>
      <template slot="num" slot-scope="{ row }">
        <el-input v-model="row.num" :disabled="isUpdate" />
      </template>
      <template slot="action" slot-scope="{ row, $index }">
        <el-popconfirm
          class="ele-action"
          :title="$t('car.tips.tips2')"
          @confirm="deleteAwb(row, $index)"
        >
          <el-link
            type="danger"
            slot="reference"
            :underline="false"
            v-if="!isUpdate"
          >
            {{ $t('basics.delete') }}
          </el-link>
        </el-popconfirm>
      </template>
    </ele-pro-table>
    <ele-pro-table
      ref="table"
      :columns="columns2"
      :need-page="false"
      :datasource="datasource2"
    >
      <template slot="order_num" slot-scope="{ row }">
        <el-input v-model="row.order_num" :disabled="isUpdate" />
      </template>
      <template slot="num" slot-scope="{ row }">
        <el-input v-model="row.num" :disabled="isUpdate" />
      </template>
      <template slot="trag" slot-scope="{ row }">
        <el-input v-model="row.trag" :disabled="isUpdate" />
      </template>
      <template slot="action2" slot-scope="{ row, $index }">
        <el-popconfirm
          class="ele-action"
          :title="$t('car.tips.tips3')"
          @confirm="deleteOthNO($index)"
        >
          <el-link
            type="danger"
            slot="reference"
            v-if="!isUpdate"
            :underline="false"
          >
            {{ $t('basics.delete') }}
          </el-link>
        </el-popconfirm>
      </template>
    </ele-pro-table>
    <div slot="footer">
      <el-button :disabled="isUpdate" @click="add()">
        {{ $t('car.add1') }}
      </el-button>
      <el-button @click="add2()" :disabled="isUpdate">
        {{ $t('car.add2') }}
      </el-button>
      <!-- <el-button @click="updateVisible(false)">
        {{ $t('basics.cancel') }}
      </el-button> -->
      <el-button
        type="primary"
        :loading="loading"
        @click="save"
        :disabled="isUpdate"
      >
        {{ $t('basics.save') }}
      </el-button>
    </div>
    <!-- <awb-info :visible.sync="showEditOut" :data="current" /> -->
  </ele-modal>
</template>

<script>
  import { update, queryAirNo } from '@/api/list/carlist';
  import { allLists } from '@/api/basics/seal';
  import ModeSelect from './mode-select';
  import AddressSelect from '@/components/AddressSelect';
  import TransfersSelect from '@/components/TransfersSelect';
  import AwbSelectNo from '@/components/AwbSelectNo';
  // import { AwbInfo } from './add'

  const DEFAULT_FORM = {
    id: null,
    address_code: '',
    cmr: '',
    created_at: '',
    logistics: '',
    time1: '',
    time2: '',
    time3: '',
    time4: '',
    t1: '',
    box_count: null,
    finance_confirm: null,
    parcel_count: null,
    type: null,
    cmr_type: null,
    plate_number: null,
    return_address: ''
  };
  const DEFAULT_OTH = {
    is_set_pay: 1,
    pincode_ref: '',
    invoice_number: '',
    delivery_cost: '',
    extra_charge: '',
    original_extra_charge: '',
    original_delivery_cost: '',
    delivery_cost_show: '',
    extra_charge_show: ''
  };

  export default {
    name: 'EditAwb',
    components: { ModeSelect, AddressSelect, TransfersSelect, AwbSelectNo },
    props: {
      // 弹窗是否打开
      visible: Boolean,
      // 修改回显的数据
      data: Object
    },
    data() {
      return {
        columns: [
          {
            columnKey: 'awb',
            slot: 'awb',
            prop: 'awb',
            label: this.$t('awb.noa.awbno'),
            minWidth: 160,
            align: 'center',
            resizable: false
          },
          {
            columnKey: 'num',
            label: this.$t('awb.noa.boxCount'),
            prop: 'num',
            minWidth: 100,
            align: 'center',
            resizable: false,
            slot: 'num'
          },
          {
            columnKey: 'trag',
            label: this.$t('car.traynum'),
            prop: 'trag',
            minWidth: 100,
            align: 'center',
            resizable: false,
            slot: 'trag'
          },
          // {
          //   columnKey: 't1',
          //   label: 't1号码',
          //   prop:'t1',
          //   minWidth: 100,
          //   align: 'center',
          //   resizable: false,
          //   slot: 't1',
          // },
          {
            columnKey: 'type',
            label: this.$t('basicsMenu.loadmode.loadmode'),
            prop: 'type',
            minWidth: 100,
            align: 'center',
            resizable: false,
            slot: 'type'
          },
          // {
          //   columnKey: 'code',
          //   label: '地址名称',
          //   prop:'code',
          //   minWidth: 100,
          //   align: 'center',
          //   resizable: false,
          //   slot: 'code',
          // },
          {
            columnKey: 'PMCnum',
            label: this.$t('car.airno'),
            prop: 'PMCnum',
            minWidth: 240,
            align: 'center',
            resizable: false,
            slot: 'PMCnum'
          },
          // {
          //   columnKey: 'num',
          //   label: '箱数',
          //   prop:'num',
          //   minWidth: 100,
          //   align: 'center',
          //   resizable: false,
          //   slot: 'num',
          // },
          {
            columnKey: 'action',
            label: this.$t('basics.action'),
            width: 80,
            align: 'center',
            resizable: false,
            slot: 'action',
            hideInSetting: true
          }
        ],
        columns2: [
          {
            columnKey: 'order_num',
            slot: 'order_num',
            prop: 'order_num',
            label: this.$t('carList.order_num'),
            minWidth: 200,
            align: 'center',
            resizable: false
          },
          {
            columnKey: 'num',
            label: this.$t('batch.out.boxnum'),
            prop: 'num',
            minWidth: 160,
            align: 'center',
            resizable: false,
            slot: 'num'
          },
          {
            columnKey: 'trag',
            label: this.$t('car.traynum'),
            prop: 'trag',
            minWidth: 160,
            align: 'center',
            resizable: false,
            slot: 'trag'
          },
          {
            columnKey: 'action',
            label: this.$t('basics.action'),
            width: 150,
            align: 'center',
            resizable: false,
            slot: 'action2',
            hideInSetting: true
          }
        ],
        inputVisible: false,
        inputValue: '',
        datasource: [
          {
            awb: '',
            num: '',
            type: '',
            trag: '',
            code: ''
          }
        ],
        datasource2: [],
        words: {
          time1: '到达货站时间:',
          time2: '离开货站时间:',
          time3: '到达仓库时间:',
          time4: '离开仓库时间:'
        },
        // 表单数据
        form: { ...DEFAULT_FORM },
        otherInfo: { ...DEFAULT_OTH },
        seals: [],
        // 表单验证规则
        rules: {
          // time2: [
          //   {
          //     required: true,
          //     message: '请输入地址名称',
          //     trigger: 'blur'
          //   }
          // ],
          // time3: [
          //   {
          //     required: true,
          //     message: '请输入地址名称',
          //     trigger: 'blur'
          //   }
          // ],
        },
        current: {},
        showEditOut: false,
        // 提交状态
        loading: false,
        editable: [],
        // 是否是修改
        isUpdate: false
      };
    },
    methods: {
      /* 获取seal列表 */
      async getSeals(cmr_id) {
        allLists({ cmr_id: cmr_id }).then((res) => {
          this.seals = res.result.list;
          this.form.seal_no = res.result.cmr_seal;
          let cmr_other = res.result.cmr_other;
          for (let k in cmr_other) {
            cmr_other[k].amount = cmr_other[k].amount ?? 0;
            cmr_other[k].unit =
              cmr_other[k].unit != null ? cmr_other[k].unit : 'Chariot';
          }
          this.datasource2 = cmr_other;
        });
      },
      /* 保存编辑 */
      save() {
        this.$refs['form'].validate((valid) => {
          if (!valid) {
            return false;
          }
          if (this.datasource.length > 0) {
            let lastArr = this.datasource.slice(-1);
            if (
              lastArr.length == 0 ||
              lastArr[0].awb == '' ||
              lastArr[0].num == ''
            ) {
              this.$message.success(this.$t('basics.err.tips1'));
              return;
            }
          }
          let par = this.famtter(this.datasource);
          console.log(par);
          if (!par) return;
          this.loading = true;
          const data = {
            ...this.form,
            params: par,
            othersNo: this.datasource2
          };
          // console.log(this.datasource)
          update(data)
            .then(() => {
              this.loading = false;
              this.$message.success(this.$t('basics.success'));
              this.updateVisible(false);
              this.$emit('done');
            })
            .catch((e) => {
              this.loading = false;
              this.$message.error(e);
            });
        });
      },
      handleClose(row, tag) {
        if (this.isUpdate) return;
        this.queryAri([tag]).then((res) => {
          console.log(res);
          if (res) {
            row.splice(row.indexOf(tag), 1);
          }
        });
      },
      showEditTagInput(tag, index, pindex) {
        if (this.isUpdate) return;
        var _this = this;
        this.queryAri([tag]).then((res) => {
          if (res) {
            _this.$set(_this.editable, pindex + '_' + index, true);
            setTimeout(function () {
              document
                .getElementById('editableInput' + pindex + '_' + index)
                .focus();
            });
          }
        });
      },
      handleEditableInputBlur(item, index, pindex) {
        if (item) {
          this.$set(this.editable, pindex + '_' + index, false);
        }
      },
      handleEditableInputConfirm(item, index, pindex) {
        if (!item) {
          return this.$message.warning('ddd');
        }
        this.$set(this.editable, pindex + '_' + index, false);
      },
      showInput(row) {
        if (this.isUpdate) return;
        row.inputVisible = !row.inputVisible;
        if (row.inputVisible) {
          setTimeout(function () {
            document.getElementById('add-new-tag').focus();
          });
        }
        // console.log(row)
        // this.$nextTick(_ => {
        //   this.$refs.saveTagInput.$refs.input.focus();
        // });
      },
      handleInputConfirm(row) {
        if (this.isUpdate) return;
        let inputValue = row.inputValue;
        // console.log(row.PMCnum.indexOf(row.inputValue))
        if (row.PMCnum.indexOf(row.inputValue) != -1) return;
        if (inputValue) {
          row.PMCnum.push(inputValue);
        }
        row.inputVisible = false;
        row.inputValue = '';
      },
      async queryAri(arr) {
        let bool = false;
        await queryAirNo({ cmr_id: this.data.id, air_nos: arr }).then((res) => {
          if (res.result.length > 0) {
            this.$message.success(
              this.$t('car.tips.tips4') + res.result.join(',')
            );
            bool = false;
          } else {
            bool = true;
          }
        });
        return bool;
      },
      add() {
        // this.unique()
        // return
        let lastArr = this.datasource.slice(-1);
        if (lastArr.length > 0) {
          if (
            lastArr[0].awb == '' ||
            lastArr[0].num == '' ||
            lastArr[0].trag == ''
          ) {
            this.$message.success(this.$t('basics.err.tips1'));
            return;
          }
        }
        this.datasource.push({
          awb: '',
          num: '',
          type: '',
          trag: '',
          // code:'',
          // t1:'',
          inputVisible: false,
          PMCnum: []
        });
      },
      add2() {
        let lastArr = this.datasource2.slice(-1);
        if (lastArr.length > 0) {
          if (lastArr[0].order_num == '') {
            this.$message.success(this.$t('basics.err.tips1'));
            return;
          }
        }
        this.datasource2.push({
          order_num: '',
          num: '',
          trag: ''
        });
      },
      deleteAwb(row, index) {
        let that = this;
        console.log(index);
        this.queryAri(row.PMCnum).then((res) => {
          if (res) {
            that.datasource.splice(index, 1);
          }
        });
      },
      deleteOthNO(index) {
        this.datasource2.splice(index, 1);
      },
      famtter(arr) {
        let tempArr = [];
        for (let i = 0; i < arr.length; i++) {
          arr[i].address = [
            {
              type: arr[i].type,
              PMCnum: arr[i].PMCnum,
              num: arr[i].num,
              trag: arr[i].trag
            }
          ];
          let newArr = {
            awb: arr[i].awb,
            address: arr[i].address
          };
          if (tempArr.length == 0) {
            //第一次直接插入
            tempArr.push(newArr);
          } else {
            let index = tempArr.findIndex((item1) => {
              return item1.awb == arr[i].awb;
            });
            if (index == -1) {
              tempArr.push(newArr);
            } else {
              this.$message.success(this.$t('car.tips.tips5'));
              return false;
            }
          }
        }
        return tempArr;
      },
      // famtter(arr){
      //   let tempArr = []
      //   for(let i=0;i<arr.length;i++){
      //     arr[i].address = [{
      //       // code: arr[i].code,
      //       type: arr[i].type,
      //       PMCnum: arr[i].PMCnum,
      //       num: arr[i].num,
      //       trag: arr[i].trag,
      //       // t1: arr[i].t1,
      //     }]
      //     let newArr = {
      //       awb:arr[i].awb,
      //       address:arr[i].address,
      //     }
      //     if(tempArr.length == 0){//第一次直接插入
      //       tempArr.push(newArr)
      //     }else{
      //       for(let item in JSON.parse(JSON.stringify(tempArr))){
      //         if(tempArr[item].awb == arr[i].awb){
      //           let index = tempArr[item].address.findIndex(item => {
      //             return item.code == arr[i].code
      //           })
      //           console.log(1)
      //           if(index == -1){
      //             tempArr[item].address.push(arr[i].address[0])
      //           }else{
      //             this.$message.success('不要提交两条相同的提单和地址信息')
      //             return false
      //           }
      //         }else{
      //           tempArr.push(newArr)
      //         }
      //       }
      //     }
      //   }
      //   return tempArr
      // },
      /* 更新visible */
      updateVisible(value) {
        this.$emit('update:visible', value);
      },
      /* 角色选择回调*/
      roleSelectCallback(value) {
        this.form.role_type = value.type;
      },
      unique(arr) {
        var arr = [
          { id: 1, name: '张三' },
          { id: 2, name: '李四' },
          { id: 3, name: '王五' },
          { id: 2, name: '张三' },
          { id: 3, name: '王五' }
        ];
        const res = new Map();
        let aa = arr.filter((a) => {
          !res.has(a.id) &&
            res.set(a.id, 1) &&
            !res.has(a.name) &&
            res.set(a.name, 1);
        });
        // return arr.filter((a)=> !res.has(a.id) && res.set(a.id,1) && !res.has(a.name) && res.set(a.name,1))
        console.log(aa);
      }
    },
    watch: {
      async visible(visible) {
        if (visible) {
          let data = JSON.parse(JSON.stringify(this.data));
          this.getSeals(data.id);
          for (let item of data.cmrBols) {
            item.num = item.address[0].num;
            item.code = item.address[0].code;
            item.trag = item.address[0].trag;
            item.type = item.address[0].type;
            item.t1 = item.address[0].t1;
            item.PMCnum = item.address[0].PMCnum;
            item.inputVisible = false;
            // item.PMCnum = item.address[0].PMCnum.join(';')
          }
          this.datasource = data.cmrBols;
          if (this.data) {
            this.$util.assignObject(this.form, {
              ...data
            });
            this.$util.assignObject(this.otherInfo, {
              ...data
            });
            if (this.data.return_address.length <= 0) {
              this.form.return_address = this.data.address_code;
            }
            this.otherInfo.delivery_cost_show =
              this.otherInfo.delivery_cost +
              ' / ' +
              this.otherInfo.original_delivery_cost;
            this.otherInfo.extra_charge_show =
              this.otherInfo.extra_charge +
              ' / ' +
              this.otherInfo.original_extra_charge;
            if (this.data.isUpdate) {
              this.isUpdate = true;
            } else {
              this.isUpdate = false;
            }
          }
        } else {
          this.$refs['form'].clearValidate();
          this.form = { ...DEFAULT_FORM };
        }
      },
      datasource: {
        handler(newVal, oldVal) {
          // console.log('oldVal:', oldVal)
          // console.log('newVal:', newVal)
          // for(let item of newVal){
          //   console.log(item)
          // }
        },
        deep: true
      }
    }
  };
</script>
<style scoped>
  .el-table__cell {
    line-height: 38px;
  }
  .el-tag {
    margin: 6px 0;
    height: 32px;
    line-height: 32px;
  }
  .el-tag + .el-tag {
    margin-left: 10px;
  }
  .button-new-tag {
    margin: 6px 0;
    margin-left: 10px;
    height: 32px;
    line-height: 30px;
    padding-top: 0;
    padding-bottom: 0;
  }
  .input-new-tag {
    margin: 6px 0;
    width: 90px;
    margin-left: 10px;
    vertical-align: bottom;
  }
</style>
