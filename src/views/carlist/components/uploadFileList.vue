<!-- 用户导入弹窗 -->
<template>
  <ele-modal
    width="520px"
    :title="this.$t('car.uploadVehicleList')"
    :visible="visible"
    @update:visible="updateVisible"
  >
    <el-upload
      drag
      action=""
      class="ele-block"
      v-loading="loading"
      accept="image/*"
      :file-list="fileList"
      :before-upload="doUpload"
    >
      <i class="el-icon-upload"></i>
      <div class="el-upload__text" v-if="this.awbData.pdf == ''">{{
        $t('car.uploadvoucherlistmsg')
      }}</div>
      <div class="el-upload__text" v-else>{{ pdfName }}</div>
    </el-upload>

    <div slot="footer">
      <el-button @click="updateVisible(false)">
        {{ $t('basics.cancel') }}
      </el-button>
      <el-button type="primary" :loading="loading" @click="save">
        {{ $t('basics.save') }}
      </el-button>
    </div>
  </ele-modal>
</template>

<script>
import {uploadFile} from '@/api/system/file';

  export default {
    name: 'uploadFileObs',
    props: {
      // 是否打开弹窗
      visible: Boolean
    },
    data() {
      return {
        // 导入请求状态
        loading: false,
        // 导入模板下载地址
        url: 'https://cdn.eleadmin.com/20200610/用户导入模板.xlsx',
        manifest: '1',
        pdfName: '2',
        awbData: {
          manifest: '',
          pdf: '',
          user_id: ''
        },
        upData: {},
        fileList: []
      };
    },
    methods: {
      save() {
        this.fileList = [];
        this.$emit('done', this.upData);
      },
      /* 上传 */
      doUpload(file) {
        this.loading = true;
        console.log(file.name);
        this.fileList = [];
        uploadFile(file)
          .then((res) => {
            console.log(res);

            this.loading = false;
            this.upData = res;
            this.fileList.push({
              name: res.files,
              url: res.files
            });
            // this.$message.success(this.$t('basics.success'));
            // this.updateVisible(false);
          })
          .catch((e) => {
            this.loading = false;
            this.$message.error(e);
          });
        return false;
      },
      /* 更新visible */
      updateVisible(value) {
        this.$emit('update:visible', value);
      }
    }
  };
</script>

<style scoped>
  .ele-block ::v-deep .el-upload,
  .ele-block ::v-deep .el-upload-dragger {
    width: 100%;
  }
</style>
