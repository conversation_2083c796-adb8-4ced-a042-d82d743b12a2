<!-- 编辑弹窗 -->
<template>
  <ele-modal
    width="1080px"
    :visible="visible"
    :close-on-click-modal="false"
    custom-class="ele-dialog-form"
    :title="isUpdate ? this.$t('basics.detail') : this.$t('basics.edit')"
    @update:visible="updateVisible"
  >
    <el-form ref="form" :model="form" :rules="rules" label-width="220px">
      <el-row :gutter="15">
        <el-col :sm="12">
          <el-form-item label="CMR:" prop="cmr">
            <el-input
              clearable
              :maxlength="50"
              show-word-limit
              v-model="form.cmr"
              :disabled="isUpdate"
            />
          </el-form-item>
        </el-col>
        <el-col :sm="12">
          <el-form-item :label="`${$t('car.addressOut')}:`" prop="address_code">
            <address-select v-model="form.address_code" v-if="!isUpdate" />
            <el-input v-else v-model="form.address_code" :disabled="isUpdate" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="15">
        <el-col :sm="12">
          <el-form-item :label="`${$t('car.transfers')}:`" prop="logistics">
            <transfers-select v-model="form.logistics" v-if="!isUpdate" />
            <el-input v-else v-model="form.logistics" :disabled="isUpdate" />
          </el-form-item>
        </el-col>
        <el-col :sm="12">
          <el-form-item
            :label="`${$t('basics.createTime')}:`"
            prop="created_at"
          >
            <el-input
              clearable
              :maxlength="50"
              show-word-limit
              :disabled="true"
              v-model="form.created_at"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="15">
        <el-col :sm="12">
          <el-form-item :label="`${$t('car.timeWords.out1')}:`" prop="time1">
            <el-date-picker
              v-model="form.time1"
              type="datetime"
              :disabled="isUpdate"
              :placeholder="this.$t('basics.placeholder.time')"
              value-format="yyyy-MM-dd HH:mm:ss"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :sm="12">
          <el-form-item :label="`${$t('car.timeWords.out2')}:`" prop="time2">
            <el-date-picker
              v-model="form.time2"
              type="datetime"
              :disabled="isUpdate"
              :placeholder="this.$t('basics.placeholder.time')"
              value-format="yyyy-MM-dd HH:mm:ss"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="15">
        <el-col :sm="12">
          <el-form-item :label="`${$t('car.timeWords.out3')}:`" prop="time3">
            <el-date-picker
              v-model="form.time3"
              type="datetime"
              :disabled="isUpdate"
              :placeholder="this.$t('basics.placeholder.time')"
              value-format="yyyy-MM-dd HH:mm:ss"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :sm="12">
          <el-form-item :label="`${$t('car.timeWords.out4')}:`" prop="time4">
            <el-date-picker
              v-model="form.time4"
              type="datetime"
              :disabled="isUpdate"
              :placeholder="this.$t('basics.placeholder.time')"
              value-format="yyyy-MM-dd HH:mm:ss"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="15">
        <el-col :sm="12">
          <el-form-item label="T1:" prop="t1">
            <el-input
              clearable
              :maxlength="50"
              :disabled="isUpdate"
              show-word-limit
              v-model="form.t1"
            />
          </el-form-item>
        </el-col>
        <el-col :sm="12">
          <el-form-item :label="`${$t('car.cmrType')}:`" prop="cmr_type">
            <el-select v-model="form.cmr_type" class="ele-fluid" :disabled="isUpdate">
              <el-option :label="this.$t('car.cmrTypes.dispatch')" :value="1" />
              <el-option :label="this.$t('car.cmrTypes.self')" :value="2" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="15">
        <el-col :sm="12">
          <el-form-item :label="`${$t('batch.car.plate_number')}:`" prop="plate_number">
            <el-input
              clearable
              :maxlength="50"
              :disabled="isUpdate"
              show-word-limit
              v-model="form.plate_number"
            />
          </el-form-item>
        </el-col>
        <el-col :sm="12">
          <el-form-item :label="`${$t('seal.seal_no')}:`" prop="cmr_type">
            <el-select
              clearable
              multiple
              filterable
              v-model="form.seal_no"
              class="ele-fluid"
              @change="$forceUpdate()"
              :disabled="isUpdate"
            >
              <el-option
                v-for="item in seals"
                :key="item.id"
                :value="item.id"
                :label="item.seal_no"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="15" v-if="isUpdate && otherInfo.is_set_pay == 2">
        <el-col :sm="12">
          <el-form-item :label="`${$t('car.pincode_ref')}:`">
            <el-input
              clearable
              :maxlength="50"
              :disabled="isUpdate"
              show-word-limit
              v-model="otherInfo.pincode_ref"
            />
          </el-form-item>
        </el-col>
        <el-col :sm="12">
          <el-form-item :label="`${$t('car.invoiceNo')}:`">
            <el-input
              clearable
              :maxlength="50"
              :disabled="isUpdate"
              show-word-limit
              v-model="otherInfo.invoice_number"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="15" v-if="isUpdate && otherInfo.is_set_pay == 2">
        <el-col :sm="12">
          <el-form-item :label="`${$t('car.deliveryFee')}:`">
            <el-input
              clearable
              :maxlength="50"
              :disabled="isUpdate"
              show-word-limit
              :value="otherInfo.delivery_cost_show"
            />
          </el-form-item>
        </el-col>
        <el-col :sm="12">
          <el-form-item :label="`${$t('car.additionalCosts')}:`">
            <el-input
              clearable
              :maxlength="50"
              :disabled="isUpdate"
              show-word-limit
              v-model="otherInfo.extra_charge_show"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="15">
        <el-col :sm="12">
          <el-form-item :label="`${$t('car.truck_order')}:`" prop="truck_order">
            <el-input
              clearable
              :disabled="isUpdate"
              :maxlength="50"
              show-word-limit
              v-model="form.truck_order"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <ele-pro-table
      ref="table"
      :columns="columns"
      :need-page="false"
      :datasource="datasource"
    >
      <template slot="awb" slot-scope="{ row }">
        <!-- <el-input v-model="row.awb" :disabled="isUpdate"/> -->
        <awb-select-no v-model="row.awb" v-if="!isUpdate" />
        <el-input v-else v-model="row.awb" :disabled="isUpdate" />
      </template>
      <template slot="num" slot-scope="{ row }">
        <el-input v-model="row.num" :disabled="isUpdate" />
      </template>
      <template slot="trag" slot-scope="{ row }">
        <el-input v-model="row.trag" :disabled="isUpdate" />
      </template>
      <!--
      <template slot="amount" slot-scope="{ row }">
        <el-input type="number" min="0" oninput ="value=value.replace(/[^\d]|^[0]/g, '')"
                  v-model="row.amount" :disabled="isUpdate" />
      </template>
      <template slot="unit" slot-scope="{ row }">
        <el-input v-model="row.unit" clearable :disabled="isUpdate" />
      </template>
      -->
      <template slot="action" slot-scope="{ row, $index }">
        <el-popconfirm
          class="ele-action"
          :title="$t('car.tips.tips3')"
          @confirm="deleteAwb($index)"
        >
          <el-link
            type="danger"
            slot="reference"
            v-if="!isUpdate"
            :underline="false"
          >
            {{ $t('basics.delete') }}
          </el-link>
        </el-popconfirm>
      </template>
    </ele-pro-table>

    <ele-pro-table
      ref="table"
      :columns="columns2"
      :need-page="false"
      :datasource="datasource2"
    >
      <template slot="order_num" slot-scope="{ row }">
        <el-input v-model="row.order_num" :disabled="isUpdate" />
      </template>
      <template slot="num" slot-scope="{ row }">
        <el-input v-model="row.num" :disabled="isUpdate" />
      </template>
      <template slot="trag" slot-scope="{ row }">
        <el-input v-model="row.trag" :disabled="isUpdate" />
      </template>
      <!--
      <template slot="amount" slot-scope="{ row }">
        <el-input type="number" min="0" oninput ="value=value.replace(/[^\d]|^[0]/g, '')"
                  v-model="row.amount" :disabled="isUpdate" />
      </template>
      <template slot="unit" slot-scope="{ row }">
        <el-input v-model="row.unit" clearable :disabled="isUpdate" />
      </template>
      -->
      <template slot="action2" slot-scope="{ row, $index }">
        <el-popconfirm
          class="ele-action"
          :title="$t('car.tips.tips3')"
          @confirm="deleteOthNO($index)"
        >
          <el-link
            type="danger"
            slot="reference"
            v-if="!isUpdate"
            :underline="false"
          >
            {{ $t('basics.delete') }}
          </el-link>
        </el-popconfirm>
      </template>
    </ele-pro-table>

    <div slot="footer">
      <el-button @click="add()" :disabled="isUpdate">
        {{ $t('car.add1') }}
      </el-button>
      <el-button @click="add2()" :disabled="isUpdate">
        {{ $t('car.add2') }}
      </el-button>
      <!-- <el-button @click="updateVisible(false)">
        {{ $t('basics.cancel') }}
      </el-button> -->
      <el-button
        type="primary"
        :loading="loading"
        :disabled="isUpdate"
        @click="save"
      >
        {{ $t('basics.save') }}
      </el-button>
    </div>
    <!-- <awb-info :visible.sync="showEditOut" :data="current" /> -->
  </ele-modal>
</template>

<script>
  import { update } from '@/api/list/carlist';
  import { allLists } from "@/api/basics/seal";
  import AddressSelect from '@/components/AddressSelect';
  import TransfersSelect from '@/components/TransfersSelect';
  import AwbSelectNo from '@/components/AwbSelectNo';

  // import { AwbInfo } from './add'

  const DEFAULT_FORM = {
    id: null,
    address_code: '',
    cmr: '',
    created_at: '',
    logistics: '',
    time1: '',
    time2: '',
    time3: '',
    time4: '',
    truck_order: '',
    box_count: null,
    finance_confirm: null,
    parcel_count: null,
    type: null,
    cmr_type: null,
    t1: '',
    plate_number: null
  };
  const DEFAULT_OTH = {
    is_set_pay: 1,
    pincode_ref: '',
    invoice_number: '',
    delivery_cost: '',
    extra_charge: '',
    original_extra_charge: '',
    original_delivery_cost: '',
    delivery_cost_show: '',
    extra_charge_show: ''
  };
  export default {
    name: 'EditAwb',
    components: { AddressSelect, TransfersSelect, AwbSelectNo },
    props: {
      // 弹窗是否打开
      visible: Boolean,
      // 修改回显的数据
      data: Object
    },
    data() {
      return {
        columns: [
          {
            columnKey: 'awb',
            slot: 'awb',
            prop: 'awb',
            label: this.$t('awb.noa.awbno'),
            minWidth: 200,
            align: 'center',
            resizable: false
          },
          {
            columnKey: 'num',
            label: this.$t('batch.out.boxnum'),
            prop: 'num',
            minWidth: 160,
            align: 'center',
            resizable: false,
            slot: 'num'
          },
          {
            columnKey: 'trag',
            label: this.$t('car.traynum'),
            prop: 'trag',
            minWidth: 160,
            align: 'center',
            resizable: false,
            slot: 'trag'
          },
          // {
          //   columnKey: 'amount',
          //   label: this.$t('car.amount'),
          //   prop: 'amount',
          //   minWidth: 130,
          //   align: 'center',
          //   resizable: false,
          //   slot: 'amount'
          // },
          // {
          //   columnKey: 'unit',
          //   label: this.$t('car.unit'),
          //   prop: 'unit',
          //   minWidth: 130,
          //   align: 'center',
          //   resizable: false,
          //   slot: 'unit'
          // },
          {
            columnKey: 'action',
            label: this.$t('basics.action'),
            width: 150,
            align: 'center',
            resizable: false,
            slot: 'action',
            hideInSetting: true
          }
        ],
        columns2: [
          {
            columnKey: 'order_num',
            slot: 'order_num',
            prop: 'order_num',
            label: this.$t('carList.order_num'),
            minWidth: 200,
            align: 'center',
            resizable: false
          },
          {
            columnKey: 'num',
            label: this.$t('batch.out.boxnum'),
            prop: 'num',
            minWidth: 160,
            align: 'center',
            resizable: false,
            slot: 'num'
          },
          {
            columnKey: 'trag',
            label: this.$t('car.traynum'),
            prop: 'trag',
            minWidth: 160,
            align: 'center',
            resizable: false,
            slot: 'trag'
          },
          // {
          //   columnKey: 'amount',
          //   label: this.$t('car.amount'),
          //   prop: 'amount',
          //   minWidth: 130,
          //   align: 'center',
          //   resizable: false,
          //   slot: 'amount'
          // },
          // {
          //   columnKey: 'unit',
          //   label: this.$t('car.unit'),
          //   prop: 'unit',
          //   minWidth: 130,
          //   align: 'center',
          //   resizable: false,
          //   slot: 'unit'
          // },
          {
            columnKey: 'action',
            label: this.$t('basics.action'),
            width: 150,
            align: 'center',
            resizable: false,
            slot: 'action2',
            hideInSetting: true
          }
        ],
        datasource: [
          {
            awb: '',
            num: ''
          }
        ],
        datasource2: [],
        words: {
          time1: '到达仓库时间:',
          time2: '离开仓库时间:',
          time3: '到配送地时间:',
          time4: '离开配送时间:'
        },
        // 表单数据
        form: { ...DEFAULT_FORM },
        otherInfo: { ...DEFAULT_OTH },
        seals: [],
        // 表单验证规则
        rules: {
          // time2: [
          //   {
          //     required: true,
          //     message: '请输入地址名称',
          //     trigger: 'blur'
          //   }
          // ],
          // time3: [
          //   {
          //     required: true,
          //     message: '请输入地址名称',
          //     trigger: 'blur'
          //   }
          // ],
        },
        current: {},
        showEditOut: false,
        // 提交状态
        loading: false,
        // 是否是修改
        isUpdate: false
      };
    },
    methods: {
      /* 获取seal列表 */
      async getSeals(cmr_id) {
        allLists({ cmr_id: cmr_id }).then((res) => {
          this.seals = res.result.list;
          this.form.seal_no = res.result.cmr_seal;

          let cmr_other = res.result.cmr_other;
          for (let k in cmr_other) {
            cmr_other[k].amount = cmr_other[k].amount ?? 0;
            cmr_other[k].unit = cmr_other[k].unit != null ? cmr_other[k].unit : "Chariot";
          }
          this.datasource2 = cmr_other;
        });
      },
      /* 保存编辑 */
      save() {
        this.$refs['form'].validate((valid) => {
          if (!valid) {
            return false;
          }
          if (this.datasource.length > 0) {
            let lastArr = this.datasource.slice(-1);
            if (
              lastArr.length == 0 ||
              lastArr[0].awb == '' ||
              lastArr[0].num == ''
            ) {
              this.$message.success(this.$t('basics.err.tips1'));
              return;
            }
          }
          let par = this.famtter(this.datasource);
          if (!par) return;
          this.loading = true;
          const data = {
            ...this.form,
            params: this.famtter(this.datasource),
            othersNo: this.datasource2
          };
          console.log(this.datasource);
          update(data)
            .then((msg) => {
              this.loading = false;
              this.$message.success(this.$t('basics.success'));
              this.updateVisible(false);
              this.$emit('done');
            })
            .catch((e) => {
              this.loading = false;
              this.$message.error(e);
            });
        });
      },
      add() {
        let lastArr = this.datasource.slice(-1);
        if (lastArr.length > 0) {
          if (lastArr[0].awb == '' || lastArr[0].num == '') {
            this.$message.success(this.$t('basics.err.tips1'));
            return;
          }
        }
        this.datasource.push({
          awb: '',
          num: '',
          trag: ''
        });
      },
      add2() {
        let lastArr = this.datasource2.slice(-1);
        if (lastArr.length > 0) {
          if (lastArr[0].order_num == '') {
            this.$message.success(this.$t('basics.err.tips1'));
            return;
          }
        }
        this.datasource2.push({
          order_num: '',
          num: '',
          trag: ''
        });
      },
      deleteAwb(index) {
        this.datasource.splice(index, 1);
      },
      deleteOthNO(index) {
        this.datasource2.splice(index, 1);
      },
      famtter(arr) {
        let tempArr = [];
        for (let i = 0; i < arr.length; i++) {
          arr[i].address = [];
          arr[i].address.push({
            num: arr[i].num,
            trag: arr[i].trag
          });
          if (tempArr.length == 0) {
            //第一次直接插入
            tempArr.push(arr[i].awb);
          } else {
            let index = tempArr.findIndex((item1) => {
              return item1 == arr[i].awb;
            });
            if (index == -1) {
              tempArr.push(arr[i].awb);
            } else {
              this.$message.success(this.$t('car.tips.tips5'));
              return false;
            }
          }
        }
        return arr;
      },
      /* 更新visible */
      updateVisible(value) {
        this.$emit('update:visible', value);
      },
      /* 角色选择回调*/
      roleSelectCallback(value) {
        this.form.role_type = value.type;
      }
    },
    watch: {
      async visible(visible) {
        console.log(this.data);
        if (visible) {
          let data = JSON.parse(JSON.stringify(this.data));
          this.getSeals(data.id);
          for (let item of data.cmrBols) {
            item.num = item.address[0].num;
            item.trag = item.address[0].trag;
            item.amount = item.amount ?? 0;
            item.unit = item.unit != null ? item.unit : "Chariot";
          }
          this.datasource = data.cmrBols;
          console.log(data);
          if (this.data) {
            this.$util.assignObject(this.form, { ...data });
            this.$util.assignObject(this.otherInfo, { ...data });
            this.otherInfo.delivery_cost_show =
              this.otherInfo.delivery_cost + ' / ' + this.otherInfo.original_delivery_cost;
            this.otherInfo.extra_charge_show =
              this.otherInfo.extra_charge + ' / ' + this.otherInfo.original_extra_charge;
            this.isUpdate = !!this.data.isUpdate;
          }
        } else {
          this.$refs['form'].clearValidate();
          this.form = { ...DEFAULT_FORM };
        }
      }
    }
  };
</script>
