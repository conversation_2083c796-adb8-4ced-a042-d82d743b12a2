<template>
  <el-dialog
    :visible.sync="visible_"
    width="1080px"
    :before-close="onClose"
    append-to-body
    @open="onOpen"
  >
    <div class="batch-view-container">
      <ele-pro-table
        ref="table"
        :pageSize="10"
        :columns="columns"
        :datasource="datasource"
      >
      </ele-pro-table>
    </div>
  </el-dialog>
</template>

<script>
  import { getCmrParcel } from '@/api/list/carlist';

  export default {
    name: 'BatchView',
    components: {},
    props: {
      visible: {
        type: Boolean,
        default: false
      },
      cmr_id: {
        type: Number,
        default: 0
      }
    },
    data() {
      return {
        selection: [],
        visible_: this.visible,
        batchList: [],
        selectedRows: [],
        loading: false,
        columns: [
          {
            prop: 'track_no',
            label: this.$t('awb.package.no'),
            showOverflowTooltip: true,
            minWidth: 110
          },
          {
            prop: 'package_reference',
            label: this.$t('order.package.packageReference'),
            showOverflowTooltip: true,
            minWidth: 110
          },

          {
            prop: 'out_time',
            label: this.$t('awb.box.stock_out_time'),
            showOverflowTooltip: true,
            minWidth: 150
          }
        ],
        allBatchList: []
      };
    },
    watch: {
      visible(val) {
        this.visible_ = val;
      }
    },
    methods: {
      datasource({ page, limit }) {
        console.log('分页参数:', { page, limit });
        return getCmrParcel({
          cmr_id: this.cmr_id,
          page: page,
          num: limit
        }).then((res) => {
          console.log('API 返回数据:', res);
          return res;
        });
      },
      onOpen() {
        this.$refs.table.reload();
      },
      onClose() {
        this.visible_ = false;
        this.loading = false;
        // 通知父组件更新 visible 状态
        this.$emit('update:visible', false);
      }
    }
  };
</script>

<style scoped>
  .batch-view-container {
    margin-bottom: 20px;
  }
  .table-toolbar {
    margin-bottom: 10px;
    text-align: right;
  }
  .empty-data {
    text-align: center;
    padding: 20px 0;
    color: #909399;
  }
</style>
