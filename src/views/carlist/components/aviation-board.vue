<template>
  <div class="ele-body" v-loading="loading">
    <el-card shadow="never">
      <el-row :gutter="15">
        <el-col :md="12">
          <!-- 未选择的班级数据表格 -->
          <ele-pro-table
            :datasource="allData"
            ref="datasourceTable"
            :columns="columnsAll"
            height="300px"
            :needPage="false"
            :toolkit="[]"
            layout="total, prev, pager, next, jumper"
          >
            <template slot="toolbar">
              <el-row :gutter="15">
                <el-col :sm="18">
                  <el-input
                    v-model="input"
                    size="mini"
                    @clear="inputClick"
                    :placeholder="$t('basics.pleaseInput')"
                    clearable
                  >
                    <el-button
                      slot="append"
                      @click="inputClick"
                      icon="el-icon-search"
                    ></el-button>
                  </el-input>
                </el-col>
              </el-row>
            </template>
            <template slot="toolkit">
              <el-button
                size="mini"
                type="danger"
                plain
                class="ele-btn-icon"
                @click="removeAll"
              >
                {{ $t('carList.aviationBoard.removeAll') }}
              </el-button>
              <el-button size="mini" class="ele-btn-icon" @click="addAll">
                {{ $t('carList.aviationBoard.addAll') }}
              </el-button>
            </template>
            <template slot="action" slot-scope="{ row }">
              <el-button v-if="!isAdd(row)" size="mini" @click="add(row)">{{
                $t('carList.aviationBoard.add')
              }}</el-button>
              <el-button
                v-else
                type="danger"
                plain
                size="mini"
                @click="remove(row)"
              >
                {{ $t('carList.aviationBoard.remove') }}
              </el-button>
            </template>
          </ele-pro-table>
        </el-col>
        <el-col :md="12">
          <!-- 已选择的班级数据表格 -->
          <ele-pro-table
            :datasource="selectedData"
            :columns="columns"
            :needPage="false"
            :sub-title="$t('carList.aviationBoard.selectedSubTitle')"
            height="300px"
            :emptyText="$t('carList.aviationBoard.selectedEmptyText')"
            :toolkit="[]"
            layout="total, prev, pager, next, jumper"
          >
            <template slot="toolkit"
              ><div style="height: 28px"> </div>
            </template>
            <template slot="action" slot-scope="{ row }">
              <el-button type="danger" plain size="mini" @click="remove(row)">
                {{ $t('carList.aviationBoard.remove') }}
              </el-button>
            </template>
          </ele-pro-table>
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script>
  import { airlist } from '@/api/list/carlist';

  export default {
    name: 'ExampleChoose',
    props: {
      // 修改回显的数据
      addressCode: String,
      cmr_id: Number
    },
    data() {
      return {
        // 加载状态
        loading: false,
        address_code: this.addressCode,
        input: '',
        // 表单数据
        allData: [],
        classes: [],
        // 已选择的班级数据
        selectedData: [],
        // 表格列配置
        columnsAll: [
          {
            prop: 'air_no',
            label: this.$t('car.airno'),
            showOverflowTooltip: true,
            minWidth: 110,
            height: '44px'
          },
          {
            columnKey: 'action',
            label: this.$t('basics.action'),
            width: 100,
            align: 'center',
            slot: 'action'
          }
        ],
        columns: [
          {
            prop: 'air_no',
            label: this.$t('car.airno'),
            showOverflowTooltip: true,
            minWidth: 110
          }
        ]
      };
    },
    created() {
      this.datasource({});
    },
    activated() {
      this.datasource({});
    },
    methods: {
      /* 表格数据源 */
      datasource({ where }) {
        const data = airlist({
          ...where,
          page: 1,
          num: 10000,
          status: 1,
          cmr_id: this.cmr_id,
          // address_code: this.address_code
        });
        data.then(({ list }) => {
          this.allData = list;
        });
        return data;
      },

      /* 刷新表格 */
      reload(address_code, selectedData) {
        this.allData = [];
        this.selectedData = selectedData ?? [];
        this.address_code = address_code;
        if (address_code !== null) {
          this.datasource({});
        }
      },
      /* 添加 */
      isAdd(row) {
        return !!this.selectedData.find((i) => i.air_no === row.air_no);
      },
      add(row) {
        this.selectedData.push({ air_no: row.air_no });
        console.log(this.selectedData);

        console.log(
          this.selectedData.findIndex((i) => i.air_no === row.air_no)
        );
      },
      /* 移除 */
      remove(row) {
        console.log(this.selectedData);
        console.log(
          this.selectedData.findIndex((i) => i.air_no === row.air_no)
        );
        this.selectedData.splice(
          this.selectedData.findIndex((i) => i.air_no === row.air_no),
          1
        );
      },
      /* 添加全部 */
      addAll() {
        this.selectedData = [];
        this.allData.forEach((d) => {
          this.selectedData.push({ air_no: d.air_no });
        });
      },
      /* 移除所有 */
      removeAll() {
        this.selectedData = [];
      },
      /* 搜索事件 */
      inputClick() {
        this.datasource({
          where: {
            air_no: this.input
          }
        });
      }
    }
  };
</script>
