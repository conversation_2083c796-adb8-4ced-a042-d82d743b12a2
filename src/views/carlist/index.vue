<template>
  <div class="ele-body">
    <el-card shadow="never">
      <!-- 搜索表单 -->
      <outbound-fba-search @search="reload" @exportFn="exportXls" />

      <!-- 数据表格 -->
      <ele-pro-table
        ref="table"
        row-key="id"
        :initLoad="false"
        :columns="columns"
        :datasource="datasource"
        :selection.sync="selection"
        @expand-change="expandChange"
        @selection-change="handleSelectionChange"
      >
        <!-- 表头工具栏 -->
        <template slot="toolbar">
          <el-button
            size="small"
            type="primary"
            icon="el-icon-plus"
            class="ele-btn-icon"
            @click="paySetting()"
            v-permission="'car:pay'"
          >
            {{ $t('car.btn1') }}
          </el-button>
          <el-button
            size="small"
            type="primary"
            icon="el-icon-plus"
            class="ele-btn-icon"
            @click="cancelPaySetting()"
            v-permission="'car:cancel_pay'"
          >
            {{ $t('car.btn3') }}
          </el-button>
          <el-button
            v-permission="'car:getAll'"
            @click="exportXls"
            icon="el-icon-download"
            >{{ $t('basics.export') }}</el-button
          >
          <el-button
            v-permission="'car:getAllCost'"
            @click="exportXls2"
            icon="el-icon-download"
            v-if="where.type != 3"
            >{{ $t('car.exportFlatExpenses') }}</el-button
          >

          <el-button
            v-permission="'car:batch:setting:cost'"
            @click="batchUploadCostClick()"
            icon="el-icon-upload"
            >{{ $t('car.batchUploadCost') }}</el-button
          >
        </template>
        <template v-slot:expand="{ row }">
          <el-form
            label-width="200px"
            label-position="left"
            size="mini"
            class="ele-form-detail"
          >
            <el-form-item :label="timeWordName(row.type, 1)"
              >{{ row.time1 }}
            </el-form-item>
            <el-form-item :label="timeWordName(row.type, 2)"
              >{{ row.time2 }}
            </el-form-item>
            <el-form-item :label="timeWordName(row.type, 3)"
              >{{ row.time3 }}
            </el-form-item>
            <el-form-item :label="timeWordName(row.type, 4)"
              >{{ row.time4 }}
            </el-form-item>
          </el-form>
        </template>
        <template slot="cmr" slot-scope="{ row }">
          <div @click="openDetail(row, 1)">
            {{ row.cmr }}
          </div>
        </template>

        <!-- 操作列 -->
        <template slot="action" slot-scope="{ row }">
          <el-link
            v-permission="'car:edit'"
            v-if="row.finance_confirm == '1' && row.lock_status == 1"
            @click="openEdit(row)"
            type="primary"
            :underline="false"
          >
            {{ $t('basics.edit') }}
          </el-link>
          <el-link
            @click="openDetail(row, 1)"
            type="primary"
            :underline="false"
          >
            {{ $t('basics.detail') }}
          </el-link>
          <el-link @click="download(row)" type="primary" :underline="false">
            {{ $t('car.viewVehicleList') }}
          </el-link>
          <el-link
            type="danger"
            slot="reference"
            :underline="false"
            v-permission="'car:uploadVehicleList'"
            v-if="row.finance_confirm == '1' && row.lock_status == 1"
            @click="[(thisRow = row), (uploadFileList = true)]"
            >{{ $t('car.uploadVehicleList') }}
          </el-link>
          <el-popconfirm
            v-permission="'car:financeC'"
            v-show="row.finance_confirm == '1' && row.lock_status == 1"
            v-if="row.type != 1"
            class="ele-action"
            :title="$t('car.tips.tips1')"
            @confirm="showfinanceConfirmFn(row)"
          >
            <el-link type="danger" slot="reference" :underline="false">
              {{ $t('basics.confirm') }}
            </el-link>
          </el-popconfirm>
          <el-popconfirm
            v-permission="'car:financeC'"
            v-show="row.finance_confirm == '1' && row.lock_status == 1"
            v-if="row.type == 1"
            class="ele-action"
            :title="$t('car.tips.tips1')"
            @confirm="financeConfirm2(row)"
          >
            <el-link type="danger" slot="reference" :underline="false">
              {{ $t('basics.confirm') }}
            </el-link>
          </el-popconfirm>
          <el-link
            type="danger"
            slot="reference"
            :underline="false"
            v-permission="'car:uploadVoucher'"
            v-if="
              row.type != 1 &&
              row.finance_confirm == '1' &&
              row.lock_status == 1
            "
            @click="[(thisRow = row), (uploadFileShow = true)]"
            >{{ $t('car.uploadvoucher') }} </el-link
          ><el-link
            type="danger"
            slot="reference"
            :underline="false"
            v-if="
              row.voucher_url && row.voucher_url != 'undefined' && row.type != 1
            "
            @click="openUrl(row)"
          >
            {{ $t('car.viewvoucher') }}
          </el-link>
          <el-link
            type="danger"
            slot="reference"
            :underline="false"
            v-if="row.type == 2"
            @click="[(current4 = row), (printShow = true)]"
            >{{ $t('car.print') }}
          </el-link>
          <el-popconfirm
            v-permission="'car:del'"
            v-show="row.finance_confirm == '1' && row.lock_status == 1"
            class="ele-action"
            :title="$t('car.tips.tips2')"
            @confirm="getCmrDetele(row)"
          >
            <el-link type="danger" slot="reference" :underline="false">
              {{ $t('basics.delete') }}
            </el-link>
          </el-popconfirm>
          <el-link
            v-if="row.lock_status == 1"
            v-permission="'car:lockStatus'"
            @click="lockStatus(row)"
            type="primary"
            :underline="false"
          >
            {{ $t('car.lock_btn') }}
          </el-link>
          <el-link
            v-if="row.lock_status != 1"
            v-permission="'car:lockStatus'"
            @click="lockStatus(row)"
            type="primary"
            :underline="false"
          >
            {{ $t('car.un_lock_btn') }}
          </el-link>
          <el-link
            v-if="row.cmr_parcel_count > 0"
            v-permission="'car:cmrParcelOut'"
            @click="openCmrParcelOut(row)"
            type="primary"
            :underline="false"
          >
            {{ $t('carList.parcel_out_btn') }}
          </el-link>
        </template>
      </ele-pro-table>
    </el-card>
    <edit-info :visible.sync="showEdit" :data="current1" @done="reload" />
    <edit-info-out
      :visible.sync="showEditOut"
      :data="current2"
      @done="reload"
    />
    <edit-info-air
      :visible.sync="showEditAir"
      :data="current3"
      @done="reload"
    />
    <setting-pay :visible.sync="showEditPay" :data="current" @done="reload" />

    <el-image-viewer
      ref="elImageViewer"
      v-if="showViewer"
      :on-close="closeViewer"
      :url-list="srcList"
    />
    <upload-file :visible.sync="uploadFileShow" @done="financeConfirm" />
    <print-file :visible.sync="printShow" :data="current4" @done="reload" />
    <upload-list :visible.sync="uploadFileList" @done="financeListConfirm" />
    <batch-upload-cost :visible.sync="batchUploadCostShow" @done="reload" />
    <parcel-view
      :visible.sync="cmrParcelOutBtn"
      :cmr_id="cmr_id"
      @done="reload"
    />
  </div>
</template>

<script>
  import {
    deleteCmr,
    financeCon,
    getBase,
    list,
    getCmrsAll,
    getCmrsAllCost,
    uploadVoucher,
    uploadVehicleList,
    lockStatus,
    cancelPaySetting
  } from '@/api/list/carlist';
  import OutboundFbaSearch from './components/search';
  import EditInfo from './components/edit';
  import EditInfoOut from './components/out';
  import EditInfoAir from './components/air';
  import SettingPay from './components/setting'; // 导入组件
  import ElImageViewer from 'element-ui/packages/image/src/image-viewer';
  import uploadFile from './components/uploadFile';
  import uploadList from './components/uploadFileList';
  import printFile from './components/printFile';
  import batchUploadCost from './components/batchUploadCost';
  import parcelView from './components/parcel-view.vue';
  // 导入组件
  import { utils, writeFile } from 'xlsx';
  export default {
    name: 'SystemUser',
    components: {
      parcelView,
      ElImageViewer,
      OutboundFbaSearch,
      EditInfo,
      EditInfoOut,
      EditInfoAir,
      SettingPay,
      uploadFile,
      printFile,
      uploadList,
      batchUploadCost
      // UserSearch,
      // UserEdit,
      // UserImport,
      // UserChangePassword
    },
    data() {
      return {
        cmr_id: 0,
        // 表格列配置
        columns: [
          // {
          //   columnKey: 'index',
          //   type: 'index',
          //   width: 45,
          //   showOverflowTooltip: true,
          //   fixed: 'left'
          // },
          {
            width: 45,
            type: 'selection',
            columnKey: 'selection',
            align: 'center'
          },
          {
            width: 45,
            type: 'expand',
            columnKey: 'expand',
            align: 'center',
            slot: 'expand'
          },
          {
            prop: 'cmr',
            label: 'CMR',
            showOverflowTooltip: true,
            minWidth: 110,
            slot: 'cmr'
          },
          {
            prop: 'address',
            label: this.$t('car.address'),
            showOverflowTooltip: true,
            minWidth: 220
          },
          {
            prop: 'logistics',
            label: this.$t('car.transfers'),
            showOverflowTooltip: true,
            minWidth: 110
          },
          {
            prop: 'plate_number',
            label: this.$t('batch.car.plate_number'),
            showOverflowTooltip: true,
            minWidth: 110
          },
          {
            prop: 'truck_order',
            label: this.$t('batch.car.truck_order'),
            showOverflowTooltip: true,
            minWidth: 110
          },
          {
            prop: 'type',
            label: this.$t('car.type'),
            showOverflowTooltip: true,
            minWidth: 110,
            formatter: (row, column, cellValue) => {
              return [
                this.$t('car.in'),
                this.$t('car.out'),
                this.$t('car.air')
              ][cellValue - 1];
            }
          },
          {
            prop: 'cmr_type',
            label: this.$t('car.cmrType'),
            showOverflowTooltip: true,
            minWidth: 110,
            formatter: (row, column, cellValue) => {
              return [
                this.$t('car.cmrTypes.dispatch'),
                this.$t('car.cmrTypes.self')
              ][cellValue - 1];
            }
          },
          {
            prop: 'finance_confirm',
            label: this.$t('car.finance'),
            showOverflowTooltip: true,
            minWidth: 110,
            formatter: (row, column, cellValue) => {
              return [this.$t('car.no'), this.$t('car.yes')][cellValue - 1];
            }
          },
          {
            prop: 'created_at',
            label: this.$t('basics.createTime'),
            showOverflowTooltip: true,
            minWidth: 110
          },
          {
            columnKey: 'action',
            label: this.$t('basics.action'),
            width: 220,
            align: 'center',
            resizable: false,
            slot: 'action',
            hideInSetting: true,
            fixed: 'right'
          }
        ],
        // 表格选中数据
        selection: [],
        // 当前编辑数据
        current: null,
        current1: null,
        current2: null,
        current3: null,
        current4: null,
        // 是否显示编辑弹窗
        showEdit: false,
        showEditOut: false,
        showEditAir: false,
        showEditPay: false,
        baseUrl: this.$store.getters.user.info.web_url,
        // 是否显示导入弹窗
        showImport: false,
        // 是否显修改密码
        userId: null,
        showChangePassword: false,
        timeWord: {
          time1: '',
          time2: '',
          time3: '',
          time4: ''
        },
        multipleSelection: [],
        showViewer: false,
        srcList: [],
        where: {
          address_code: '',
          cmr: '',
          finance_confirm: null,
          logistics: '',
          type: null
        },
        uploadFileShow: false,
        printShow: false,
        uploadFileList: false,
        thisRow: {},
        batchUploadCostShow: false,
        cmrParcelOutBtn: false
      };
    },
    created() {
      //或者登陆不显示货主名字其他登录显示名称
      if (
        this.$store.state.user.info.role_type === '2' ||
        this.$store.state.user.info.role_type === '3'
      ) {
        const index = this.columns.findIndex(function (key) {
          return key.prop === 'username';
        });
        this.columns.splice(index, index + 1);
      }
    },
    activated() {
      this.reload();
    },
    methods: {
      openCmrParcelOut(row) {
        this.cmr_id = row.id;
        this.cmrParcelOutBtn = true;
      },
      batchUploadCostClick() {
        this.batchUploadCostShow = true;
      },
      openUrl(row) {
        let url = row.voucher_url;
        window.open(url);
      },
      exportXls() {
        if (!this.where.type) {
          this.$message.warning(this.$t('car.tips2'));
          return false;
        }
        // 检查除了type之外是否还有其他搜索条件
        // const searchKeys = Object.keys(this.where).filter(key => key !== 'type');
        // const hasOtherCondition = searchKeys.some(key => {
        //   const value = this.where[key];
        //   if (Array.isArray(value)) {
        //     return value.length > 0;
        //   }
        //   return value !== null && value !== '' && value !== undefined;
        // });
        // // 导出条件必须至少有两个条件
        // if (!hasOtherCondition) {
        //   this.$message.warning(this.$t('car.tips5'));
        //   return false;
        // }
        // this.where

        const array = [];
        let typeText =
          this.where.type == 1
            ? this.$t('car.in')
            : this.where.type == 2
            ? this.$t('car.out')
            : this.where.type == 3
            ? this.$t('car.air')
            : '';
        if (this.where.type == 1) {
          array.push([
            'CMR',
            this.$t('car.type'),
            this.$t('car.transfers'),
            this.$t('car.deliveryAddress'),
            this.$t('car.timeWords.air3'),
            this.$t('car.timeWords.air4'),
            this.$t('car.timeWords.air1'),
            this.$t('car.timeWords.air2'),
            this.$t('batch.car.plate_number'), //车牌号
            this.$t('car.pincode_ref'), //成本编码
            this.$t('car.invoiceNo'),
            this.$t('car.deliveryFee'),
            this.$t('car.additionalCosts'),
            this.$t('car.totalExpenses')
          ]);
        }

        if (this.where.type == 2) {
          array.push([
            'CMR',
            this.$t('car.type'),
            this.$t('car.transfers'),
            this.$t('car.deliveryAddress2'),
            this.$t('car.timeWords.out1'),
            this.$t('car.timeWords.out2'),
            this.$t('car.timeWords.out3'),
            this.$t('car.timeWords.out4'),
            this.$t('batch.car.plate_number'), //车牌号
            this.$t('car.pincode_ref'), //成本编码
            this.$t('car.invoiceNo'),
            this.$t('car.deliveryFee'),
            this.$t('car.additionalCosts'),
            this.$t('car.totalExpenses'),
            this.$t('batch.car.truck_order')
          ]);
        }

        if (this.where.type == 3) {
          array.push([
            'CMR',
            this.$t('car.type'),
            this.$t('car.transfers'),
            this.$t('car.deliveryAddress2'),
            this.$t('car.timeWords.out1'),
            this.$t('car.timeWords.out2'),
            this.$t('car.timeWords.out3'),
            this.$t('car.timeWords.out4'),
            this.$t('batch.car.plate_number'), //车牌号
            this.$t('car.pincode_ref'), //成本编码
            this.$t('car.invoiceNo'),
            this.$t('car.deliveryFee'),
            this.$t('car.additionalCosts'),
            this.$t('car.totalExpenses')
          ]);
        }

        getCmrsAll({
          ...this.where
        }).then((res) => {
          console.log(res);
          res.forEach((d) => {
            let item = [
              d.cmr,
              typeText,
              d.logistics,
              d.address_code,
              d.time1,
              d.time2,
              d.time3,
              d.time4,
              d.plate_number, //车牌号
              d.pincode_ref, //成本编码
              d.invoice_number, //发票号
              d.delivery_cost, //派送费用
              d.extra_charge, //额外费用
              d.total_cost
            ];
            if (this.where.type == 2) {
              item.push(d.truck_order);
            }
            array.push(item);
          });
          const sheetName = 'Sheet1';
          const workbook = {
            SheetNames: [sheetName],
            Sheets: {}
          };
          workbook.Sheets[sheetName] = utils.aoa_to_sheet(array);
          writeFile(workbook, 'CMR.xlsx');
        });
      },
      exportXls2() {
        if (!this.where.type) {
          this.$message.warning(this.$t('car.tips2'));
          return false;
        }
        const array = [];

        let typeText =
          this.where.type == 1
            ? this.$t('car.in')
            : this.where.type == 2
            ? this.$t('car.out')
            : this.where.type == 3
            ? this.$t('car.air')
            : '';
        if (this.where.type == 1) {
          array.push([
            'CMR', //cmr
            this.$t('awb.noa.awbno'), //提单号
            this.$t('car.type'), //车单类型
            this.$t('car.transfers'), //运输公司
            this.$t('car.deliveryAddress'), //提货地址
            this.$t('awb.noa.time'), //NOA时间'
            this.$t('car.timeWords.air3'), //到达货站时间
            this.$t('car.timeWords.air4'), //离开货站时间
            this.$t('car.timeWords.in3'), //到达仓库时间
            this.$t('car.timeWords.in4'), //离开仓库时间
            this.$t('car.userName'), //用户名称
            this.$t('car.grossweight2'), //毛重
            this.$t('awb.awb.col6'), //计费重量
            this.$t('awb.awb.col3'), //预报箱数
            this.$t('car.airno'), //航空板号
            this.$t('car.airType'), //装载模式
            this.$t('car.trayNum'), //托盘数量
            this.$t('car.boxNum'), //箱数
            this.$t('car.actual_weight'), //实际重量
            this.$t('batch.car.plate_number'), //车牌号
            this.$t('car.pincode_ref'), //成本编码
            this.$t('car.invoiceNo'), //发票号
            this.$t('car.deliveryFee'), //派送费用
            this.$t('car.additionalCosts'), //额外费用
            this.$t('car.amortizedExpenses') //平摊费用
          ]);
        } else if (this.where.type == 2) {
          array.push([
            'CMR', //CMR
            this.$t('awb.noa.awbno'), //提单号
            this.$t('car.type'), //车单类型
            this.$t('car.transfers'), //运输公司
            this.$t('car.deliveryAddress2'), //派送地址
            this.$t('car.timeWords.out1'), //到达仓库时间
            this.$t('car.timeWords.out2'), //离开仓库时间
            this.$t('car.timeWords.out3'), //到达目的地时间
            this.$t('car.timeWords.out4'), //离开目的地时间
            this.$t('car.userName'), //用户账号
            this.$t('car.grossweight'), //毛重
            this.$t('awb.awb.col6'), //计费重量
            this.$t('awb.awb.col3'), //预报箱数
            this.$t('car.trayNum'), //托盘数量
            this.$t('car.boxNum'), //箱数
            this.$t('car.actual_weight'), //实际重量
            this.$t('batch.car.plate_number'), //车牌号
            this.$t('car.pincode_ref'), //成本编码
            this.$t('car.invoiceNo'), //发票号
            this.$t('car.deliveryFee'), //派送费用
            this.$t('car.additionalCosts'), //额外费用
            this.$t('car.amortizedExpenses'), //平摊费用
            this.$t('batch.car.truck_order') // 卡车订单号
          ]);
        }
        getCmrsAllCost({
          ...this.where
        }).then((res) => {
          console.log(res);
          const arrayCmr = [];
          if (this.where.type == 1) {
            res.forEach((d) => {
              if (arrayCmr.indexOf(d.cmr) === -1) {
                d.delivery_cost = d.delivery_cost1;
                d.extra_charge = d.extra_charge1;
                arrayCmr.push(d.cmr);
              }
              array.push([
                d.cmr, //CMR
                d.awb, //提单号
                typeText, //车单类型
                d.logistics, //运输公司
                d.address_code, //提货地址
                d.noa_time, //NOA时间
                d.time1, //到达货站时间
                d.time2, //离开货站时间
                d.time3, //到达仓库时间
                d.time4, //离开仓库时间
                d.user_id, //用户名称
                d.gross_weight, //毛重
                d.gross_mass_kg, //计费重量
                d.box_count, //预报箱数
                d.PMCnum.join(';'), //航空板号
                d.uld_type, //装载模式
                d.trag, //托盘数量
                d.num, //箱数
                ((d.num / d.box_count) * d.gross_mass_kg).toFixed(3),
                d.plate_number, //车牌号
                d.pincode_ref, //成本编码
                d.invoice_number, //发票号
                d.delivery_cost, //派送费用
                d.extra_charge, //额外费用
                d.delivery_cost + d.extra_charge //平摊费用
              ]);
            });
          } else {
            res.forEach((d) => {
              if (arrayCmr.indexOf(d.cmr) === -1) {
                d.delivery_cost = d.delivery_cost1;
                d.extra_charge = d.extra_charge1;
                arrayCmr.push(d.cmr);
              }
              let item = [
                d.cmr, //CMR
                d.awb, //awbno
                typeText, //车单类型
                d.logistics, //运输公司
                d.address_code, //派送地址
                d.time1, //到达仓库时间
                d.time2, //离开仓库时间
                d.time3, //到达目的地时间
                d.time4, //离开目的地时间
                d.user_id, //用户账号
                d.gross_weight, //毛重
                d.gross_mass_kg, //计费重量
                d.box_count, //预报箱数
                d.trag, //托盘数量
                d.num, //箱数
                ((d.num / d.box_count) * d.gross_mass_kg).toFixed(3),
                d.plate_number, //车牌号
                d.pincode_ref, //成本编码
                d.invoice_number, //发票号
                d.delivery_cost, //派送费用
                d.extra_charge, //额外费用
                d.delivery_cost + d.extra_charge //平摊费用
              ];

              if (this.where.type == 2) {
                item.push(d.truck_order);
              }

              array.push(item);
            });
          }
          const sheetName = 'Sheet1';
          const workbook = {
            SheetNames: [sheetName],
            Sheets: {}
          };
          workbook.Sheets[sheetName] = utils.aoa_to_sheet(array);
          writeFile(workbook, 'CMR.xlsx');
        });
      },
      // 查看图片
      showImage(path) {
        this.srcList = path;
        this.showViewer = true;
        setTimeout(() => {
          this.$refs.elImageViewer.handleActions('clocelise');
        }, 20);
      },
      // 关闭查看器
      closeViewer() {
        this.showViewer = false;
      },
      /* 表格数据源 */
      datasource({ page, limit, where, order }) {
        return list({
          ...where,
          ...order,
          page: page,
          num: limit
          // inbound_id: this.$route.query.inbound_id
        });
      },
      download({ cmr_path }) {
        getBase({ file_path: cmr_path })
          .then(({ result }) => {
            const { img_base_64 } = result;
            this.showImage([img_base_64]);
          })
          .catch((res) => {
            this.$message.error(res);
          });
      },
      downloadByBlob(url) {
        var image = new Image();
        image.src = url + '?t=' + new Date().getTime(); //图片流
        image.crossOrigin = '*'; // 支持跨域图片
        image.onload = function () {
          var canvas = document.createElement('canvas');
          canvas.width = image.width;
          canvas.height = image.height;
          var ctx = canvas.getContext('2d');
          ctx.drawImage(image, 0, 0, image.width, image.height);
          var base64 = canvas.toDataURL('image/png');
          var elink = document.createElement('a');
          elink.style.display = 'none';
          elink.download = '';
          elink.href = base64;
          document.body.appendChild(elink);
          elink.click();
          URL.revokeObjectURL(elink.href);
          document.body.removeChild(elink);
        };
      },
      /* 刷新表格 */
      reload(where) {
        if (!where) {
          this.where = {
            address_code: '',
            cmr: '',
            finance_confirm: null,
            logistics: '',
            type: null
          };
        } else {
          this.where = where;
        }
        this.$refs.table.reload({ page: 1, where: where });
      },
      lockStatus(data) {
        const loading = this.$loading({ lock: true });
        lockStatus(data)
          .then(() => {
            loading.close();
            this.$message.success(this.$t('basics.success'));
            this.reload();
          })
          .catch((e) => {
            loading.close();
            this.$message.error(e);
          });
      },
      getCmrDetele(data) {
        const loading = this.$loading({ lock: true });
        deleteCmr(data)
          .then(() => {
            loading.close();
            this.$message.success(this.$t('basics.success'));
            this.reload();
          })
          .catch((e) => {
            loading.close();
            this.$message.error(e);
          });
      },
      /* 打开编辑弹窗 */
      openEdit(row) {
        switch (row.type) {
          case 1: //ru
            this.current1 = row;
            this.showEdit = true;
            break;

          case 2: //chu
            this.current2 = row;
            this.showEditOut = true;
            break;

          case 3: //huan
            this.current3 = row;
            this.showEditAir = true;
            break;

          default:
            break;
        }
      },
      openDetail(row) {
        switch (row.type) {
          case 1: //ru
            this.current1 = {
              ...row,
              isUpdate: true
            };
            this.showEdit = true;
            break;

          case 2: //chu
            this.current2 = {
              ...row,
              isUpdate: true
            };
            this.showEditOut = true;
            break;

          case 3: //huan
            this.current3 = {
              ...row,
              isUpdate: true
            };
            this.showEditAir = true;
            break;

          default:
            break;
        }
      },
      /* 打开导入弹窗 */
      openImport() {
        this.showImport = true;
      },
      handleSelectionChange(val) {
        this.multipleSelection = val;
        console.log(this.multipleSelection);
      },
      showfinanceConfirmFn(row) {
        financeCon(row).then(
          (res) => {
            console.log('res', res);
            // this.$message.success(res.status);
            this.reload();
          },
          (error) => {
            this.$message.error(error);
            console.log(error);
          }
        );
        // this.uploadFileShow = true;
        // this.thisRow = row;
      },
      financeListConfirm(res) {
        if (!res.files) {
          this.$message.warning(this.$t('car.tips4'));
          return false;
        }
        this.uploadFileList = false;
        let row = {
          ...this.thisRow,
          cmr_path: res.files
        };
        console.log(row);
        uploadVehicleList(row).then(
          (res) => {
            console.log('res', res);
            // this.$message.success(res.status);
            this.reload();
          },
          (error) => {
            this.$message.error(error);
            console.log(error);
          }
        );
      },
      financeConfirm(res) {
        if (!res.objectUrl) {
          this.$message.warning(this.$t('car.tips3'));
          return false;
        }
        this.uploadFileShow = false;
        let row = {
          ...this.thisRow,
          voucher_url: res.objectUrl
        };
        console.log(row);
        uploadVoucher(row).then(
          (res) => {
            console.log('res', res);
            // this.$message.success(res.status);
            this.reload();
          },
          (error) => {
            this.$message.error(error);
            console.log(error);
          }
        );
      },
      financeConfirm2(row) {
        financeCon(row).then(
          (res) => {
            console.log('res', res);
            // this.$message.success(res.status);
            this.reload();
          },
          (error) => {
            this.$message.error(error);
            console.log(error);
          }
        );
      },
      paySetting() {
        this.current = this.multipleSelection;
        if (this.multipleSelection.length == 0) {
          this.$message.success(this.$t('car.tips.err1'));
          return;
        }
        this.showEditPay = true;
      },
      cancelPaySetting() {
        if (this.multipleSelection.length == 0) {
          this.$message.success(this.$t('car.tips.err1'));
          return;
        }
        const loading = this.$loading({ lock: true });
        let temp = [];
        for (let item of this.multipleSelection) {
          temp.push(item.id);
        }
        const postData = {
          cmr_id: temp
        };
        cancelPaySetting(postData)
          .then((msg) => {
            loading.close();
            this.$message.success(this.$t('basics.success'));
            this.reload();
          })
          .catch((e) => {
            loading.close();
            this.$message.error(e);
          });
      },
      timeWordName(type, name) {
        let tName = '';
        switch (type) {
          case 1: //ru
            tName = 'car.timeWords.in';
            break;
          case 2: //ru
            tName = 'car.timeWords.out';
            break;
          case 3: //ru
          default:
            // eslint-disable-next-line no-unused-vars
            tName = 'car.timeWords.air';
            break;
        }
        return this.$t(tName + name);
      },
      expandChange(row) {
        switch (row.type) {
          case 1: //ru
            row.timeWord = {
              time1: this.$t('car.timeWords.in1'),
              time2: this.$t('car.timeWords.in2'),
              time3: this.$t('car.timeWords.in3'),
              time4: this.$t('car.timeWords.in4')
            };
            break;
          case 2: //chu
            row.timeWord = {
              time1: this.$t('car.timeWords.out1'),
              time2: this.$t('car.timeWords.out2'),
              time3: this.$t('car.timeWords.out3'),
              time4: this.$t('car.timeWords.out4')
            };
            break;
          case 3: //huan
            row.timeWord = {
              time1: this.$t('car.timeWords.air1'),
              time2: this.$t('car.timeWords.air2'),
              time3: this.$t('car.timeWords.air3'),
              time4: this.$t('car.timeWords.air4')
            };
            break;

          default:
            break;
        }
      }
    }
  };
</script>
<style>
  .ele-form-detail {
    font-size: 0;
  }

  .ele-form-detail label {
    width: 90px;
    color: #99a9bf;
  }

  .ele-form-detail .el-form-item {
    margin-right: 0;
    margin-bottom: 0;
    margin-left: 50px;
    /* width: 50%; */
  }
</style>
