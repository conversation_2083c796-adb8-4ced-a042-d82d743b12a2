<template>
  <div class="ele-body">
    <el-card shadow="never">
      <!-- 搜索表单 -->
      <outbound-fba-search @search="reload" />
      <!-- 数据表格 -->
      <ele-pro-table
        ref="table"
        :initLoad="false"
        :columns="columns"
        :pagerCount="5"
        layout="total, sizes, prev, pager, next, jumper"
        :datasource="datasource"
        :selection.sync="selection"
      >
        <template slot="toolbar">
          <el-popconfirm
            v-permission="'box:edit'"
            class="ele-action"
            :title="$t('awb.box.tips.t1')"
            @confirm="batchEditAdd()"
          >
            <el-button
              slot="reference"
              type="primary"
              class="ele-btn-icon"
              size="small"
              >{{ $t('awb.box.editAdd') }}</el-button
            >
          </el-popconfirm>
          <el-button
            size="small"
            icon="el-icon-download"
            class="ele-btn-icon"
            @click="exportXls"
            v-permission="'box:export'"
          >
            {{ $t('basics.export') }}
          </el-button>
        </template>
        <template slot="action" slot-scope="{ row }">
          <el-popconfirm
            v-show="row.status < 4"
            v-permission="'box:edit'"
            class="ele-action"
            :title="$t('awb.box.tips.t1')"
            @confirm="editAdd(row)"
          >
            <el-link type="danger" slot="reference" :underline="false">
              {{ $t('awb.box.editAdd') }}
            </el-link>
          </el-popconfirm>
        </template>
      </ele-pro-table>
      <add-edit
        :visible.sync="showEdit"
        :data="current"
        @done="reload"
      ></add-edit>
    </el-card>
  </div>
</template>

<script>
  import { lists } from '@/api/order/box';
  import OutboundFbaSearch from './components/outbound-fba-search';
  import AddEdit from './components/edit';
  import { utils, writeFile } from 'xlsx';

  export default {
    name: 'SystemUser',
    components: {
      OutboundFbaSearch,
      AddEdit
      // UserSearch,
      // UserEdit,
      // UserImport,
      // UserChangePassword
    },
    data() {
      return {
        // 表格列配置
        columnsData: [
          {
            width: 45,
            type: 'selection',
            columnKey: 'selection',
            // noRole: 'sonOwner', //什么角色下不显示
            align: 'center'
          },
          // {
          //   columnKey: 'index',
          //   type: 'index',
          //   width: 80,
          //   role: 'sonOwner', //什么角色下不显示
          //   showOverflowTooltip: true,
          //   fixed: 'left'
          // },
          {
            prop: 'bol_no',
            label: this.$t('awb.noa.awbno'),
            showOverflowTooltip: true,
            minWidth: 110
          },
          {
            prop: 'box_no',
            label: this.$t('awb.box.box'),
            showOverflowTooltip: true,
            minWidth: 180
          },
          {
            prop: 'address',
            label: this.$t('car.addressOut'),
            showOverflowTooltip: true,
            minWidth: 110
          },
          {
            prop: 'user_address_code',
            label: this.$t('userAddress.user_addresses'),
            showOverflowTooltip: true,
            minWidth: 110
          },
          {
            prop: 'user_id',
            noRole: 'sonOwner', //什么角色下不显示
            label: this.$t('awb.awb.col9'),
            showOverflowTooltip: true,
            minWidth: 80
          },
          {
            prop: 'status',
            label: this.$t('basics.status'),
            showOverflowTooltip: true,
            minWidth: 100,
            formatter: (row, column, cellValue) => {
              return [
                this.$t('awb.box.status.s1'),
                this.$t('awb.box.status.s2'),
                this.$t('awb.box.status.s3'),
                this.$t('awb.box.status.s4')
              ][cellValue - 1];
            }
          },
          {
            prop: 'clearance',
            label: this.$t('awb.awb.clearStatus'),
            showOverflowTooltip: true,
            minWidth: 100,
            formatter: (row, column, cellValue) => {
              // return cellValue;
              return this.$t(`basics.statusWords.s${cellValue}`);
            }
          },
          {
            prop: 'box_count',
            label: this.$t('awb.awb.col3'),
            showOverflowTooltip: true,
            minWidth: 110
          },
          {
            prop: 'gross_weight',
            label: this.$t('order.awb.grossWeight') + '(kg)',
            showOverflowTooltip: true,
            minWidth: 110
          },
          {
            prop: 'created_at',
            label: this.$t('basics.createTime'),
            showOverflowTooltip: true,
            minWidth: 110
          },
          {
            prop: 'stock_in_time',
            label: this.$t('awb.box.stock_in_time'),
            showOverflowTooltip: true,
            noRole: 'sonOwner', //什么角色下不显示
            minWidth: 110
          },
          {
            prop: 'stock_shelt_time',
            label: this.$t('awb.box.stock_shelt_time'),
            showOverflowTooltip: true,
            noRole: 'sonOwner', //什么角色下不显示
            minWidth: 110
          },
          {
            prop: 'stock_out_time',
            label: this.$t('awb.box.stock_out_time'),
            showOverflowTooltip: true,
            noRole: 'sonOwner', //什么角色下不显示
            minWidth: 110
          },
          {
            prop: 'storage_name',
            label: this.$t('storages.storage_name'),
            showOverflowTooltip: true,
            noRole: 'sonOwner', //什么角色下不显示
            minWidth: 110
          },
          {
            noRole: 'sonOwner', //什么角色下不显示
            columnKey: 'action',
            label: this.$t('basics.action'),
            width: 140,
            align: 'center',
            resizable: false,
            slot: 'action',
            hideInSetting: true,
            fixed: 'right'
          }
        ],
        // 表格选中数据
        selection: [],
        // 当前编辑数据
        current: null,
        // 是否显示编辑弹窗
        showEdit: false,
        // 是否显示导入弹窗
        showImport: false,
        // 是否显修改密码
        userId: null,
        showChangePassword: false,
        lastWhere: {},
        pageNum: 20000,
        pageAll: 1,
        loading: null
      };
    },
    activated() {
      this.reload();
    },
    computed: {
      // 通知标题
      columns() {
        return this.columnsData.filter((i) => {
          return (
            (i.noRole ? !this.$hasRole(i.noRole) : true) &&
            (i.role ? this.$hasRole(i.role) : true)
          );
        });
      }
    },
    methods: {
      /* 表格数据源 */
      datasource({ page, limit, where, order }) {
        return lists({
          ...where,
          ...order,
          page: page,
          num: limit
          // inbound_id: this.$route.query.inbound_id
        });
      },
      /* 刷新表格 */
      reload(where) {
        this.lastWhere = where;
        this.$refs.table.reload({ page: 1, where: where });
      },
      /* 打开编辑弹窗 */
      editAdd(row) {
        this.current = {
          addresses_users: row.user_address_code,
          box_id_arr: [row.id]
        };
        this.showEdit = true;
      },
      /* 打开导入弹窗 */
      openImport() {
        this.showImport = true;
      },
      exportXlsOth(pages, array) {
        var _this = this;
        let roleUser = this.$hasRole('sonOwner');
        const status = [
          '',
          this.$t('awb.box.status.s1'),
          this.$t('awb.box.status.s2'),
          this.$t('awb.box.status.s3'),
          this.$t('awb.box.status.s4')
        ];
        const clearStatus = [
          '',
          this.$t('basics.statusWords.s1'),
          this.$t('basics.statusWords.s2'),
          this.$t('basics.statusWords.s3')
        ];
        lists({
          ...this.lastWhere,
          page: pages,
          num: this.pageNum
        }).then((res) => {
          res.list.forEach((d) => {
            if (roleUser) {
              array.push([
                d.bol_no,
                d.box_no,
                d.address,
                d.user_address_code,
                status[d.status],
                clearStatus[d.clearance],
                d.created_at,
                d.storage_name
              ]);
            } else {
              array.push([
                d.bol_no,
                d.box_no,
                d.address,
                d.user_address_code,
                d.user_id,
                status[d.status],
                clearStatus[d.clearance],
                d.created_at,
                d.stock_in_time,
                d.stock_out_time,
                d.storage_name,
                d.box_count,
                d.gross_weight
              ]);
            }
          });
          if (pages >= _this.pageAll) {
            _this.loading.close();
            const sheetName = 'Sheet1';
            const workbook = {
              SheetNames: [sheetName],
              Sheets: {}
            };
            workbook.Sheets[sheetName] = utils.aoa_to_sheet(array);
            writeFile(workbook, 'BOX.xlsx');
          } else {
            _this.exportXlsOth(Number(pages) + 1, array);
          }
        });
      },
      exportXls() {
        const array = [];
        let roleUser = this.$hasRole('sonOwner');
        if (roleUser) {
          array.push([
            this.$t('awb.noa.awbno'),
            this.$t('awb.box.box'),
            this.$t('car.addressOut'),
            this.$t('userAddress.user_addresses'),
            this.$t('basics.status'),
            this.$t('awb.awb.clearStatus'),
            this.$t('basics.createTime'),
            this.$t('storages.storage_name')
          ]);
        } else {
          array.push([
            this.$t('awb.noa.awbno'),
            this.$t('awb.box.box'),
            this.$t('car.addressOut'),
            this.$t('userAddress.user_addresses'),
            this.$t('awb.awb.col9'),
            this.$t('basics.status'),
            this.$t('awb.awb.clearStatus'),
            this.$t('basics.createTime'),
            this.$t('awb.box.stock_in_time'),
            this.$t('awb.box.stock_out_time'),
            this.$t('storages.storage_name'),
            this.$t('awb.awb.col3'),
            this.$t('order.awb.grossWeight') + '(kg)'
          ]);
        }
        const status = [
          '',
          this.$t('awb.box.status.s1'),
          this.$t('awb.box.status.s2'),
          this.$t('awb.box.status.s3'),
          this.$t('awb.box.status.s4')
        ];
        const clearStatus = [
          '',
          this.$t('basics.statusWords.s1'),
          this.$t('basics.statusWords.s2'),
          this.$t('basics.statusWords.s3')
        ];
        var _this = this;
        _this.loading = this.$loading({ lock: true });
        lists({
          ...this.lastWhere,
          page: 1,
          num: this.pageNum
        }).then((res) => {
          _this.pageAll = Math.ceil(res.count / this.pageNum);
          res.list.forEach((d) => {
            if (roleUser) {
              array.push([
                d.bol_no,
                d.box_no,
                d.address,
                d.user_address_code,
                status[d.status],
                clearStatus[d.clearance],
                d.created_at,
                d.storage_name
              ]);
            } else {
              array.push([
                d.bol_no,
                d.box_no,
                d.address,
                d.user_address_code,
                d.user_id,
                status[d.status],
                clearStatus[d.clearance],
                d.created_at,
                d.stock_in_time,
                d.stock_out_time,
                d.storage_name,
                d.box_count,
                d.gross_weight
              ]);
            }
          });

          if (_this.pageAll == 1) {
            _this.loading.close();
            const sheetName = 'Sheet1';
            const workbook = {
              SheetNames: [sheetName],
              Sheets: {}
            };
            workbook.Sheets[sheetName] = utils.aoa_to_sheet(array);
            writeFile(workbook, 'BOX.xlsx');
          } else {
            _this.exportXlsOth(2, array);
          }
        });
      },
      batchEditAdd() {
        if (this.selection.find((i) => i.status == 4))
          return this.$message.error(this.$t('orderBoxBatchEdit'));
        console.log(this.selection.map((i) => i.id));
        this.current = {
          addresses_users: null,
          box_id_arr: this.selection.map((i) => i.id)
        };
        this.showEdit = true;
      }
    }
  };
</script>

<style lang="scss" scoped>
  :deep(.el-pager li.el-icon.more ~ li.number) {
    display: none !important;
  }
</style>
