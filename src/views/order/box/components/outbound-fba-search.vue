<template>
  <el-card shadow="never" body-style="padding: 22px 22px 7px 22px;">
    <!-- 搜索表单 -->
    <el-form
      label-width="145px"
      class="ele-form-search"
      @keyup.enter.native="search"
      @submit.native.prevent
    >
      <el-row :gutter="15">
        <el-col :lg="8" :md="12">
          <el-form-item :label="this.$t('awb.noa.awbno') + ':'" prop="bol_no">
            <el-input
              v-model="where.bol_no"
              :placeholder="this.$t('basics.pleaseInput')"
            />
          </el-form-item>
        </el-col>
        <el-col :lg="8" :md="12">
          <el-form-item :label="this.$t('awb.box.box') + ':'">
            <el-input
              clearable
              show-word-limit
              v-model="where.box_no"
              :placeholder="this.$t('basics.pleaseInput')"
            />
          </el-form-item>
        </el-col>
        <el-col :lg="8" :md="12">
          <el-form-item :label="`${$t('awb.package.no')}:`">
            <el-input
              clearable
              show-word-limit
              v-model="where.track_no"
              :placeholder="this.$t('basics.pleaseInput')"
            />
          </el-form-item>
        </el-col>
        <el-col :lg="8" :md="12">
          <el-form-item
            :label="$t('order.package.packageReference') + ':'"
            prop="bol_no"
          >
            <el-input
              v-model="where.package_reference"
              :placeholder="this.$t('basics.pleaseInput')"
            />
          </el-form-item>
        </el-col>
        <el-col :lg="8" :md="12">
          <el-form-item :label="`${$t('car.addressOut')}:`">
            <address-select v-model="where.address_code" />
          </el-form-item>
        </el-col>

        <el-col :lg="8" :md="12">
          <el-form-item :label="this.$t('userAddress.user_addresses') + ':'">
            <user-address-select v-model="where.user_address_code" />
          </el-form-item>
        </el-col>
        <el-col :lg="8" :md="12">
          <el-form-item :label="this.$t('basics.status') + ':'" prop="status">
            <el-select
              v-model="where.status"
              :placeholder="this.$t('basics.pleaseChoose')"
              clearable
              class="ele-fluid"
            >
              <el-option :label="this.$t('awb.box.status.s1')" :value="1" />
              <el-option :label="this.$t('awb.box.status.s3')" :value="3" />
              <!-- <el-option label="已约车" :value="2" /> -->
              <el-option :label="this.$t('awb.box.status.s4')" :value="4" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :lg="8" :md="12">
          <el-form-item
            :label="this.$t('awb.awb.clearStatus') + ':'"
            prop="clearance"
          >
            <el-select
              v-model="where.clearance"
              :placeholder="this.$t('basics.pleaseChoose')"
              clearable
              class="ele-fluid"
            >
              <el-option :label="this.$t('basics.statusWords.s1')" :value="1" />
              <el-option :label="this.$t('basics.statusWords.s2')" :value="2" />
              <el-option :label="this.$t('basics.statusWords.s3')" :value="3" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :lg="8" :md="12" v-permission="'awb:nouserSearch'">
          <el-form-item
            :label="this.$t('awb.box.is_one_parcel') + ':'"
            prop="clearance"
          >
            <el-select
              v-model="where.is_one_parcel"
              :placeholder="this.$t('basics.pleaseChoose')"
              clearable
              class="ele-fluid"
            >
              <el-option :label="this.$t('basics.yes')" :value="1" />
              <el-option :label="this.$t('basics.no')" :value="2" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :lg="8" :md="12">
          <el-form-item
            :label="this.$t('basics.createTime') + ':'"
            prop="created_at"
          >
            <!-- <el-date-picker
              v-model="where.created_at"
              type="datetime"
              :placeholder="this.$t('basics.placeholder.time')"
              value-format="yyyy-MM-dd HH:mm:ss"
              style="width: 100%"
            /> -->
            <el-date-picker
              v-model="where.created_at"
              type="datetimerange"
              value-format="yyyy-MM-dd HH:mm:ss"
              :range-separator="this.$t('basics.to')"
              class="ele-fluid"
              :start-placeholder="this.$t('basics.beginTime')"
              :end-placeholder="this.$t('basics.endTime')"
            >
            </el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :lg="8" :md="12" v-permission="'awb:nouserSearch'">
          <el-form-item
            :label="$t('awb.box.stock_in_time') + ':'"
            prop="stock_in_time"
          >
            <el-date-picker
              v-model="where.stock_in_time"
              type="datetimerange"
              value-format="yyyy-MM-dd HH:mm:ss"
              :range-separator="this.$t('basics.to')"
              class="ele-fluid"
              :start-placeholder="this.$t('basics.beginTime')"
              :end-placeholder="this.$t('basics.endTime')"
            >
            </el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :lg="8" :md="12" v-permission="'awb:nouserSearch'">
          <el-form-item
            :label="$t('awb.box.stock_out_time') + ':'"
            prop="stock_out_time"
          >
            <el-date-picker
              v-model="where.stock_out_time"
              type="datetimerange"
              value-format="yyyy-MM-dd HH:mm:ss"
              :range-separator="this.$t('basics.to')"
              class="ele-fluid"
              :start-placeholder="this.$t('basics.beginTime')"
              :end-placeholder="this.$t('basics.endTime')"
            >
            </el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :lg="8" :md="12" v-permission="'awb:nouserSearch'">
          <el-form-item :label="$t('storages.storage_name') + ':'" prop="storage_name">
            <el-input v-model="where.storage_name" :placeholder="this.$t('basics.pleaseInput')" />
          </el-form-item>
        </el-col>
        <el-col :lg="8" :md="12">
          <div class="ele-form-actions">
            <el-button
              type="primary"
              icon="el-icon-search"
              class="ele-btn-icon"
              @click="search"
            >
              {{ $t('basics.query') }}
            </el-button>
            <el-button @click="reset">{{ $t('basics.reset') }}</el-button>
          </div>
        </el-col>
      </el-row>
    </el-form>
  </el-card>
</template>

<script>
  import AddressSelect from '@/components/AddressSelect';
  import UserAddressSelect from '@/components/UserAddressSelect';
  // 默认表单数据

  const DEFAULT_WHERE = {
    bol_no: '',
    box_no: '',
    track_no: '',
    package_reference: '',
    address_code: '',
    status: null,
    clearance: null,
    user_address_code: null,
    storage_name: ""
  };

  export default {
    name: 'outbound-fba-search',
    components: { UserAddressSelect, AddressSelect },
    data() {
      return {
        // 表单数据
        where: { ...DEFAULT_WHERE },
        // 搜索表单是否展开
        searchExpand: false,
        goodsCategoryList: [],
        category_id: ''
      };
    },
    methods: {
      /* 搜索 */
      search() {
        //解决搜索 也搜索下级的数据
        this.$emit('search', this.where);
      },
      /* 重置搜索 */
      reset() {
        this.where = { ...DEFAULT_WHERE };
        this.search();
      },
      /* 搜索展开/收起 */
      toggleExpand() {
        this.searchExpand = !this.searchExpand;
        this.$emit('expand-change', this.searchExpand);
      },
      /* 搜索展开/收起 */
      goodsCategoryFilterListClick(e) {
        this.goodsCategoryList = e.data;
      }
    }
  };
</script>
