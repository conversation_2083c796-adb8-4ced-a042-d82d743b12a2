<template>
  <el-card shadow="never" body-style="padding: 22px 22px 7px 22px;">
    <!-- 搜索表单 -->
    <el-form
      label-width="205px"
      class="ele-form-search"
      @keyup.enter.native="search"
      @submit.native.prevent
    >
      <el-row :gutter="15">
        <el-col :lg="8" :md="12">
          <el-form-item :label="this.$t('awb.noa.awbno') + ':'" prop="bol_no">
            <el-input
              v-model="where.bol_no"
              :placeholder="this.$t('basics.pleaseInput')"
            />
          </el-form-item>
        </el-col>
        <el-col :lg="8" :md="12">
          <el-form-item :label="this.$t('awb.box.box') + ':'">
            <el-input
              clearable
              show-word-limit
              v-model="where.box_no"
              :placeholder="this.$t('basics.pleaseInput')"
            />
          </el-form-item>
        </el-col>

        <el-col :lg="8" :md="12">
          <el-form-item :label="this.$t('awb.package.no') + ':'">
            <el-input
              clearable
              show-word-limit
              v-model="where.track_no"
              :placeholder="this.$t('basics.pleaseInput')"
            />
          </el-form-item>
        </el-col>
        <el-col :lg="8" :md="12">
          <el-form-item
            :label="$t('order.package.packageReference') + ':'"
            prop="bol_no"
          >
            <el-input
              v-model="where.package_reference"
              :placeholder="this.$t('basics.pleaseInput')"
            />
          </el-form-item>
        </el-col>
        <el-col :lg="8" :md="12">
          <el-form-item
            :label="this.$t('order.package.boxChecked') + ':'"
            prop="views"
          >
            <el-select
              v-model="where.box_check"
              :placeholder="this.$t('basics.pleaseChoose')"
              clearable
              class="ele-fluid"
            >
              <el-option :label="this.$t('basics.yes')" :value="2" />
              <el-option :label="this.$t('basics.no')" :value="1" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :lg="8" :md="12">
          <el-form-item
            :label="this.$t('order.package.scanStatus') + ':'"
            prop="views"
          >
            <el-select
              v-model="where.views"
              :placeholder="this.$t('basics.pleaseChoose')"
              clearable
              class="ele-fluid"
            >
              <el-option :label="this.$t('awb.package.status.s2')" :value="2" />
              <el-option :label="this.$t('awb.package.status.s3')" :value="3" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :lg="8" :md="12">
          <el-form-item
            :label="this.$t('order.package.customsClearanceResults') + ':'"
            prop="check"
          >
            <el-select
              v-model="where.check"
              :placeholder="this.$t('basics.pleaseChoose')"
              clearable
              class="ele-fluid"
            >
              <el-option :label="this.$t('basics.inspection')" :value="2" />
              <el-option :label="this.$t('basics.release')" :value="1" />
              <el-option
                :label="$t('order.package.failedToClearCustoms')"
                :value="4"
              />
              <el-option :label="$t('basics.pendingClearance')" :value="5" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :lg="8" :md="12">
          <el-form-item
            :label="$t('order.package.failedToClearCustoms') + ':'"
            prop="take_delivery"
          >
            <el-select
              :disabled="where.check !== 4"
              v-model="where.take_delivery"
              :placeholder="this.$t('basics.pleaseChoose')"
              clearable
              class="ele-fluid"
            >
              <el-option
                :label="$t('order.package.failedToClearCustomsTakeDelivery')"
                :value="2"
              />
              <el-option
                :label="
                  $t(
                    'order.package.failedToClearCustomsTakeDeliveryWarehouseRetention'
                  )
                "
                :value="3"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <!-- v-permission="'awb:nouserSearch'" -->
        <el-col :lg="8" :md="12">
          <el-form-item :label="this.$t('basicsMenu.address.name') + ':'">
            <address-select v-model="where.address_code" />
          </el-form-item>
        </el-col>
        <!-- <el-col :lg="8" :md="12">
          <el-form-item :label="this.$t('awb.package.address_code') + ':'">
            <el-input
              clearable
              show-word-limit
              v-model="where.address_code"
              :placeholder="this.$t('basics.pleaseInput')"
            />
          </el-form-item>
        </el-col> -->
        <!-- v-permission="'awb:mrnSearch'" -->
        <el-col :lg="8" :md="12">
          <el-form-item :label="this.$t('awb.package.mrn') + ':'">
            <el-input
              clearable
              show-word-limit
              v-model="where.mrn"
              :placeholder="this.$t('basics.pleaseInput')"
            />
          </el-form-item>
        </el-col>
        <el-col :lg="8" :md="12">
          <el-form-item
            :label="$t('awb.package.control_mail_date') + ':'"
            prop="created_at"
          >
            <el-date-picker
              v-model="where.control_mail_date"
              type="datetimerange"
              value-format="yyyy-MM-dd HH:mm:ss"
              :range-separator="$t('basics.to')"
              class="ele-fluid"
              :start-placeholder="$t('basics.beginTime')"
              :end-placeholder="$t('basics.endTime')"
            >
            </el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :lg="8" :md="12" v-permission="'awb:nouserSearch'">
          <el-form-item :label="$t('awb.awb.col9')" prop="user_id">
            <el-select
              clearable
              filterable
              :value="where.user_id"
              class="ele-block"
              :placeholder="this.$t('basics.pleaseChoose')"
              @change="userChange"
            >
              <el-option
                v-for="{ nickname, id } in user_list"
                :key="id"
                :value="id"
                :label="nickname"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :lg="8" :md="12">
          <div class="ele-form-actions">
            <el-button
              type="primary"
              icon="el-icon-search"
              class="ele-btn-icon"
              @click="search"
            >
              {{ $t('basics.query') }}
            </el-button>
            <el-button @click="reset">{{ $t('basics.reset') }}</el-button>
          </div>
        </el-col>
      </el-row>
    </el-form>
  </el-card>
</template>

<script>
  import AddressSelect from '@/components/AddressSelect/index.vue';
  import { user } from '@/api/basics/user';

  const DEFAULT_WHERE = {
    bol_no: '',
    box_no: '',
    track_no: '',
    package_reference: '',
    address_code: '',
    access_warehouse: '',
    check: null,
    box_check: '',
    user_address_code: null,
    take_delivery: '',
    views: '',
    mrn: '',
    control_mail_date: '',
    user_id: ''
  };

  export default {
    name: 'outbound-fba-search',
    components: { AddressSelect },
    data() {
      return {
        // 表单数据
        where: { ...DEFAULT_WHERE },
        // 搜索表单是否展开
        searchExpand: false,
        goodsCategoryList: [],
        category_id: '',
        user_list: []
      };
    },
    activated() {
      this.where.bol_no = this.$route.query.bol_no
        ? this.$route.query.bol_no
        : '';
      this.where.address_code = this.$route.query.address_code
        ? this.$route.query.address_code
        : '';
      this.where.box_check = this.$route.query.box_check
        ? Number(this.$route.query.box_check)
        : '';
      if (this.where.bol_no.length > 0) {
        this.search();
      }
      console.log(this.$route.query.bol_no);
    },
    created() {
      user()
        .then((res) => {
          console.log(res);
          this.user_list = res.result;
        })
        .catch((err) => {
          console.log(err);
        });
    },
    methods: {
      /* 搜索 */
      search() {
        //解决搜索 也搜索下级的数据
        this.$emit('search', this.where);
      },
      /* 重置搜索 */
      reset() {
        this.where = { ...DEFAULT_WHERE };
        this.search();
      },
      /* 搜索展开/收起 */
      toggleExpand() {
        this.searchExpand = !this.searchExpand;
        this.$emit('expand-change', this.searchExpand);
      },
      /* 搜索展开/收起 */
      goodsCategoryFilterListClick(e) {
        this.goodsCategoryList = e.data;
      },
      userChange(value) {
        this.where.user_id = value;
      }
    }
  };
</script>
