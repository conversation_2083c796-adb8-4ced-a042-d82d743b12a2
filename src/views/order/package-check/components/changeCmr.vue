<!-- 编辑弹窗 -->
<template>
  <ele-modal
    width="720px"
    :visible="visible"
    :close-on-click-modal="false"
    custom-class="ele-dialog-form"
    :title="this.$t('awb.package.out')"
    @update:visible="updateVisible"
  >
    <el-form ref="form" :model="form" label-width="100px">
      <el-row type="flex">
        <el-col :span="12">
          <el-form-item :label="this.$t('platform.pickup.cmr') + ':'">
            <el-select
              v-model="form.cmr"
              :placeholder="$t('basics.pleaseChoose')"
              style="width: 100%"
              filterable
              remote
              :remote-method="handleSearch"
              :loading="loading"
              reserve-keyword
              clearable
              @clear="handleClear"
            >
              <el-option
                v-for="item in cmr_list"
                :key="item.id"
                :label="item.cmr"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div slot="footer">
      <el-button @click="updateVisible(false)">
        {{ $t('basics.cancel') }}
      </el-button>
      <el-button type="primary" :loading="loading" @click="save">
        {{ $t('basics.save') }}
      </el-button>
    </div>
  </ele-modal>
</template>

<script>
  const DEFAULT_FORM = {
    ids: [],
    date: '',
    cmr: null
  };
  import { list } from '@/api/list/carlist';
  import { changeCmr } from '@/api/order/package';

  export default {
    name: 'UserEdit',
    components: {},
    props: {
      visible: Boolean,
      data: Array
    },
    data() {
      return {
        cmr_list: [],
        cmr_list_backup: [], // 备份初始数据
        // 表单数据
        form: { ...DEFAULT_FORM },
        // 表单验证规则

        // 提交状态
        loading: false,
        // 是否是修改
        isUpdate: false
      };
    },
    methods: {
      /* 保存编辑 */
      save() {
        // 从数组中提取 id 字段
        const parcel_ids = this.data.map((item) => item.id);

        changeCmr({
          cmr: this.form.cmr,
          parcel_ids: parcel_ids
        })
          .then(() => {
            this.$message.success(this.$t('basics.success'));
            this.updateVisible(false);
            this.$emit('done');
          })
          .catch((e) => {
            this.loading = false;
            this.$message.error(e);
          });
      },
      /* 更新visible */
      updateVisible(value) {
        this.$emit('update:visible', value);
      },
      /* 角色选择回调*/
      roleSelectCallback(value) {
        this.form.role_type = value.type;
      },
      getCmrList(cmr_no) {
        let data = {
          type: 2,
          page: 1,
          num: 50,
          cmr: cmr_no,
          finance_confirm: 1
        };
        list(data)
          .then((res) => {
            const dataList = res.list || [];
            this.cmr_list = dataList;

            // 如果是初始加载（cmr_no 为空），则备份数据
            if (!cmr_no) {
              this.cmr_list_backup = [...dataList];
            }
            this.loading = false;
          })
          .catch((err) => {
            console.error('获取 CMR 列表失败:', err);
            this.cmr_list = [];
            this.loading = false;
          });
      },
      // 处理搜索
      handleSearch(query) {
        if (query !== '') {
          // 先从备份数据中搜索
          const filteredData = this.cmr_list_backup.filter(
            (item) =>
              item.cmr && item.cmr.toLowerCase().includes(query.toLowerCase())
          );

          // 如果备份数据中有匹配的结果，直接使用
          if (filteredData.length > 0) {
            this.cmr_list = filteredData;
          } else {
            // 备份数据中没有匹配结果，请求接口
            this.loading = true;
            this.getCmrList(query);
          }
        } else {
          // 当搜索框为空时，使用备份的初始数据
          this.cmr_list = [...this.cmr_list_backup];
        }
      },
      // 处理清空
      handleClear() {
        // 清空选中内容后，恢复显示备份的初始数据
        this.cmr_list = [...this.cmr_list_backup];
      }
    },
    watch: {
      async visible(visible) {
        this.getCmrList('');
        if (visible) {
          // 无论是否有 data，都加载 CMR 列表
          this.loading = true;
          if (this.data) {
            this.isUpdate = true;
          } else {
            this.isUpdate = false;
          }
        } else {
          this.form = { ...DEFAULT_FORM };
        }
      }
    }
  };
</script>
