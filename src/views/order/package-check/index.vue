<template>
  <div class="ele-body">
    <el-card shadow="never">
      <!-- 搜索表单 -->
      <outbound-fba-search @search="reload" />
      <!-- 数据表格 -->
      <ele-pro-table
        ref="table"
        :initLoad="false"
        :columns="columns"
        :datasource="datasource"
        :selection.sync="selection"
        @selection-change="handleSelectionChange"
      >
        <template slot="toolbar">
          <el-button
            size="small"
            type="primary"
            icon="el-icon-plus"
            class="ele-btn-icon"
            @click="packSetting()"
            v-permission="'package:update'"
          >
            {{ $t('awb.package.btn1') }}
          </el-button>
          <el-button
            v-permission="'parcels:multiParcelOut'"
            size="small"
            type="primary"
            icon="el-icon-plus"
            class="ele-btn-icon"
            @click="openOutDialog()"
          >
            {{ $t('awb.package.out') }}
          </el-button>

          <el-button
            v-permission="'parcel:updatePendingCmr'"
            size="small"
            type="primary"
            icon="el-icon-plus"
            class="ele-btn-icon"
            @click="openChangeCmrDialog()"
          >
            {{ $t('awb.package.changeCmr') }}
          </el-button>

          <el-button
            size="small"
            icon="el-icon-download"
            class="ele-btn-icon"
            @click="exportXls"
            v-permission="'parcels:export'"
          >
            {{ $t('basics.export') }}
          </el-button>
        </template>
        <template slot="action" slot-scope="{ row }">
          <el-link
            @click="showDetailFn(row)"
            type="primary"
            icon="el-icon-s-operation"
            :underline="false"
          >
            {{ $t('awb.awb.see') }}
          </el-link>
        </template>
        <template slot="out_time" slot-scope="{ row }">
          {{
            row.access_warehouse == 3
              ? row.cmr_pending_time || row.box_out_time || ''
              : ''
          }}
        </template>
      </ele-pro-table>
    </el-card>
    <setting-pack :visible.sync="showEditPack" :data="current" @done="reload" />
    <Detail :visible.sync="showDetail" :data="currentRow" />
    <out-dialog :visible.sync="showDialog" :data="current" @done="reload" />
    <change-cmr-dialog
      :visible.sync="showChangeCmrDialog"
      :data="current"
      @done="reload"
    />
  </div>
</template>

<script>
  import { cancel, lists, settingPack } from '@/api/order/package';
  import { utils, writeFile } from 'xlsx';
  import { hasPermission } from '@/utils/permission';
  import OutboundFbaSearch from './components/outbound-fba-search';
  import SettingPack from './components/setting.vue';
  import Detail from './components/detail.vue';
  import OutDialog from './components/out';
  import ChangeCmrDialog from './components/changeCmr';
  export default {
    name: 'SystemUser',
    components: {
      Detail,
      SettingPack,
      OutboundFbaSearch,
      OutDialog,
      ChangeCmrDialog
    },
    data() {
      return {
        // 表格列配置
        columns: [
          {
            width: 45,
            type: 'selection',
            columnKey: 'selection',
            align: 'center'
          },
          /* {
          columnKey: 'index',
          type: 'index',
          width: 45,
          align: 'center',
          showOverflowTooltip: true,
          fixed: 'left'
        }, */
          {
            prop: 'bol_no',
            label: this.$t('awb.noa.awbno'),
            showOverflowTooltip: true,
            minWidth: 110
          },
          {
            prop: 'box_no',
            label: this.$t('awb.box.box'),
            showOverflowTooltip: true,
            minWidth: 110
          },
          {
            prop: 'track_no',
            label: this.$t('awb.package.no'),
            showOverflowTooltip: true,
            minWidth: 110
          },
          {
            prop: 'package_reference',
            label: this.$t('order.package.packageReference'),
            showOverflowTooltip: true,
            minWidth: 180
          },
          {
            prop: 'address',
            label: this.$t('basicsMenu.address.name'),
            showOverflowTooltip: true,
            minWidth: 120
          },
          {
            prop: 'mrn',
            label: this.$t('awb.package.mrn'),
            showOverflowTooltip: true,
            minWidth: 85
          },
          {
            prop: 'batch_no[0].batch_no',
            label: this.$t('awb.package.bol_batch'),
            showOverflowTooltip: true,
            minWidth: 85
          },
          {
            prop: 'user_id',
            label: this.$t('awb.awb.col9'),
            showOverflowTooltip: true,
            minWidth: 85
          },
          {
            prop: 'control_mail_date',
            label: this.$t('awb.package.control_mail_date'),
            showOverflowTooltip: true,
            minWidth: 110
          },
          {
            prop: 'views',
            label: this.$t('order.package.scanStatus'),
            showOverflowTooltip: true,
            minWidth: 60,
            formatter: (row, column, cellValue) => {
              return [
                '',
                '',
                this.$t('awb.package.status.s2'),
                this.$t('awb.package.status.s3')
              ][cellValue];
            }
          },
          {
            prop: 'check',
            label: this.$t('order.package.customsClearanceResults'),
            showOverflowTooltip: true,
            minWidth: 80,
            formatter: (row, column, cellValue) => {
              return [
                '',
                this.$t('basics.release'),
                this.$t('basics.inspection'),
                this.$t('basics.unknown'),
                this.$t('order.package.failedToClearCustoms'),
                this.$t('basics.pendingClearance')
              ][cellValue];
            }
          },
          {
            prop: 'permit_through_date',
            label: this.$t('awb.package.permit_through_date'),
            showOverflowTooltip: true,
            minWidth: 110
          },
          {
            columnKey: 'out_time',
            label: this.$t('batch.car.out_time'),
            showOverflowTooltip: true,
            minWidth: 110,
            slot: 'out_time'
          },
          {
            columnKey: 'action',
            label: this.$t('basics.action'),
            width: 100,
            align: 'center',
            resizable: false,
            slot: 'action',
            hideInSetting: true,
            fixed: 'right'
          }
        ],
        baseUrl: this.$store.getters.user.info.web_url,
        // 表格选中数据
        selection: [],
        // 当前编辑数据
        current: null,
        currentRow: null,
        // 是否显示编辑弹窗
        showEdit: false,
        showEditPack: false,
        // 是否显示导入弹窗
        showImport: false,
        // 是否显修改密码
        userId: null,
        showChangePassword: false,
        multipleSelection: [],
        showDetail: false,
        lastWhere: {},
        pageNum: 5000,
        pageAll: 1,
        loading: null,
        showDialog: false,
        showChangeCmrDialog: false
      };
    },

    watch: {
      async selection() {
        console.log(this.selection);
      }
    },
    created() {
      //或者登陆不显示货主名字其他登录显示名称
      let user_info = this.$store.state.user.info;
      if (user_info.role_type === '2' || user_info.role_type === '3') {
        const index = this.columns.findIndex(function (key) {
          return key.prop === 'username';
        });
        this.columns.splice(index, index + 1);
      }
      if (!hasPermission('awb:nouserSearch')) {
        let columns = this.columns;
        for (let x of ['batch_no[0].batch_no', 'user_id']) {
          const index = columns.findIndex(function (k) {
            return k.prop === x;
          });
          columns.splice(index, 1);
          this.columns = columns;
        }
      }
    },
    activated() {
      this.reload();
    },
    methods: {
      openChangeCmrDialog() {
        if (this.multipleSelection.length <= 0) {
          this.$message(this.$t('basics.pleaseChoose'));
          return;
        }

        //cmr_pending_time 用户选择的数据 cmr_pending_time不能为空，如果是空提示不能更换
        const emptyList = this.multipleSelection.filter(
          (item) => !item.cmr_pending_time
        );

        if (emptyList.length > 0) {
          // 拼接所有有问题的编号
          const ids = emptyList.map((item) => item.track_no).join('，');
          this.$message.warning(
            this.$t('awb.package.changeCmr_err') + ':' + ids
          );
          return;
        }

        this.current = this.multipleSelection;
        this.showChangeCmrDialog = true;
      },
      openOutDialog() {
        if (this.multipleSelection.length <= 0) {
          this.$message(this.$t('basics.pleaseChoose'));
          return;
        }

        // 检查选中的项目是否都满足条件
        let hasInvalidItem = false;
        let track_id_Arr = [];
        this.multipleSelection.forEach((item) => {
          if (item.check !== 1 || item.access_warehouse != 2) {
            hasInvalidItem = true;
            track_id_Arr.push(item.track_no);
          }
        });

        if (hasInvalidItem) {
          // 拼接包裹号，多个用逗号分隔，单个不显示逗号
          let trackNumbers = track_id_Arr.join(',');
          this.$message.error(
            trackNumbers + this.$t('awb.package.parcel_status_err')
          );
          return;
        }

        this.current = this.multipleSelection;
        this.showDialog = true;
      },
      showDetailFn(row) {
        this.currentRow = row;
        this.showDetail = true;
      },
      /* 表格数据源 */
      datasource({ page, limit, where, order }) {
        return lists({
          ...where,
          ...order,
          page: page,
          num: limit,
          check_operate: 2
        });
      },
      doubleCheck() {
        this.loading = true;
        if (this.multipleSelection.length == 0) {
          return;
        }
        let temp = [];
        for (let item of this.multipleSelection) {
          temp.push(item.track_no);
        }
        const data = {
          track_no: temp,
          type: 1,
          value: 2
        };
        settingPack(data)
          .then(() => {
            this.loading = false;
            this.$message.success(this.$t('basics.success'));
          })
          .catch((e) => {
            this.loading = false;
            this.$message.error(e);
          });
      },
      packSetting() {
        if (this.multipleSelection.length > 0) {
          this.current = this.multipleSelection;
          this.showEditPack = true;
        } else {
          this.$message.error(this.$t('clearanceError.selectedEmpty'));
        }
      },
      handleSelectionChange(val) {
        this.multipleSelection = val;
        console.log(this.multipleSelection);
      },
      /* 刷新表格 */
      reload(where) {
        this.$refs.table.reload({ page: 1, where: where });
        if (where) {
          this.lastWhere = where;
        } else {
          this.lastWhere.bol_no = this.$route.query.bol_no
            ? this.$route.query.bol_no
            : '';
          this.lastWhere.box_check = this.$route.query.box_check
            ? Number(this.$route.query.box_check)
            : '';
        }
      },
      /* 打开编辑弹窗 */
      showlogs(row) {
        this.currentRow = row;
        this.showEdit = true;
      },
      /* 打开导入弹窗 */
      openImport() {
        this.showImport = true;
      },
      exportXlsOth(pages, array) {
        console.log('exportXls');
        var _this = this;
        const checks = [
          '',
          this.$t('basics.release'),
          this.$t('basics.inspection'),
          this.$t('basics.unknown'),
          this.$t('order.package.failedToClearCustoms')
        ];
        const scanStatus = [
          '',
          '',
          this.$t('awb.package.status.s2'),
          this.$t('awb.package.status.s3')
        ];
        lists({
          ...this.lastWhere,
          page: pages,
          num: this.pageNum,
          check_operate: 2
        }).then((res) => {
          res.list.forEach((d) => {
            array.push([
              d.bol_no,
              d.box_no,
              d.track_no,
              d.package_reference,
              d.control_mail_date,
              scanStatus[d.views],
              checks[d.check]
            ]);
          });
          if (pages >= _this.pageAll) {
            _this.loading.close();
            const sheetName = 'Sheet1';
            const workbook = {
              SheetNames: [sheetName],
              Sheets: {}
            };
            workbook.Sheets[sheetName] = utils.aoa_to_sheet(array);
            writeFile(workbook, 'parcels.xlsx');
          } else {
            _this.exportXlsOth(Number(pages) + 1, array);
          }
        });
      },
      exportXls() {
        const array = [];
        array.push([
          this.$t('awb.noa.awbno'),
          this.$t('awb.box.box'),
          this.$t('awb.package.no'),
          this.$t('order.package.packageReference'),
          this.$t('awb.package.mrn'),
          this.$t('awb.awb.col9'),
          this.$t('awb.package.control_mail_date'),
          this.$t('order.package.scanStatus'),
          this.$t('order.package.customsClearanceResults')
        ]);
        const checks = [
          '',
          this.$t('basics.release'),
          this.$t('basics.inspection'),
          this.$t('basics.unknown'),
          this.$t('order.package.failedToClearCustoms')
        ];
        const scanStatus = [
          '',
          '',
          this.$t('awb.package.status.s2'),
          this.$t('awb.package.status.s3')
        ];
        var _this = this;
        _this.loading = this.$loading({ lock: true });
        lists({
          ...this.lastWhere,
          page: 1,
          num: this.pageNum,
          check_operate: 2
        }).then((res) => {
          _this.pageAll = Math.ceil(res.count / this.pageNum);
          res.list.forEach((d) => {
            array.push([
              d.bol_no,
              d.box_no,
              d.track_no,
              d.package_reference,
              d.mrn,
              d.user_id,
              d.control_mail_date,
              scanStatus[d.views],
              checks[d.check]
            ]);
          });

          if (_this.pageAll == 1) {
            _this.loading.close();
            const sheetName = 'Sheet1';
            const workbook = {
              SheetNames: [sheetName],
              Sheets: {}
            };
            workbook.Sheets[sheetName] = utils.aoa_to_sheet(array);
            writeFile(workbook, 'parcels.xlsx');
          } else {
            _this.exportXlsOth(2, array);
          }
        });
      },
      /* 删除 */
      cancel(row) {
        const loading = this.$loading({ lock: true });
        cancel(row.id)
          .then(() => {
            loading.close();
            this.$message.success(this.$t('basics.success'));
            this.reload();
          })
          .catch((e) => {
            loading.close();
            this.$message.error(e);
          });
      }
    }
  };
</script>
