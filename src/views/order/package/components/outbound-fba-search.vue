<template>
  <el-card shadow="never" body-style="padding: 22px 22px 7px 22px;">
    <!-- 搜索表单 -->
    <el-form
      label-width="205px"
      class="ele-form-search"
      @keyup.enter.native="search"
      @submit.native.prevent
    >
      <el-row :gutter="15">
        <el-col :lg="8" :md="12">
          <el-form-item :label="this.$t('awb.noa.awbno') + ':'" prop="bol_no">
            <el-input
              v-model="where.bol_no"
              :placeholder="this.$t('basics.pleaseInput')"
            />
          </el-form-item>
        </el-col>
        <el-col :lg="8" :md="12">
          <el-form-item :label="this.$t('awb.box.box') + ':'">
            <el-input
              clearable
              show-word-limit
              v-model="where.box_no"
              :placeholder="this.$t('basics.pleaseInput')"
            />
          </el-form-item>
        </el-col>
        <el-col :lg="8" :md="12">
          <el-form-item :label="this.$t('awb.package.no') + ':'">
            <el-input
              clearable
              show-word-limit
              v-model="where.track_no"
              :placeholder="this.$t('basics.pleaseInput')"
            />
          </el-form-item>
        </el-col>
        <el-col :lg="8" :md="12">
          <el-form-item
            :label="$t('order.package.packageReference') + ':'"
            prop="bol_no"
          >
            <el-input
              v-model="where.package_reference"
              :placeholder="this.$t('basics.pleaseInput')"
            />
          </el-form-item>
        </el-col>
        <el-col :lg="8" :md="12" v-permission="'awb:nouserSearch'">
          <el-form-item :label="this.$t('basicsMenu.address.name') + ':'">
            <!-- <el-input
              clearable
              show-word-limit
              v-model="where.address_code"
              placeholder="请输入地址代码"
            /> -->
            <address-select v-model="where.address_code" />
          </el-form-item>
        </el-col>
        <el-col :lg="8" :md="12">
          <el-form-item :label="this.$t('userAddress.user_addresses') + ':'">
            <user-address-select v-model="where.user_address_code" />
          </el-form-item>
        </el-col>
        <el-col :lg="8" :md="12">
          <el-form-item
            :label="this.$t('awb.package.access_warehouse') + ':'"
            prop="views"
          >
            <el-select
              v-model="where.access_warehouse"
              :placeholder="this.$t('awb.package.access_warehouse')"
              clearable
              class="ele-fluid"
            >
              <el-option
                :label="this.$t('awb.package.access_warehouse_status.1')"
                :value="1"
              />
              <el-option
                :label="this.$t('awb.package.access_warehouse_status.2')"
                :value="2"
              />
              <el-option
                :label="this.$t('awb.package.access_warehouse_status.3')"
                :value="3"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :lg="8" :md="12">
          <el-form-item
            :label="`${$t('order.package.historicalExamination')}:`"
            prop="check_operate"
          >
            <el-select
              v-model="where.check_operate"
              :placeholder="this.$t('basics.pleaseChoose')"
              clearable
              @blur="where.views = ''"
              class="ele-fluid"
            >
              <el-option :label="this.$t('basics.no')" :value="1" />
              <el-option :label="this.$t('basics.yes')" :value="2" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :lg="8" :md="12">
          <el-form-item
            :label="this.$t('order.package.scanStatus') + ':'"
            prop="views"
          >
            <el-select
              :disabled="!where.check_operate || where.check_operate == 1"
              v-model="where.views"
              :placeholder="this.$t('basics.pleaseChoose')"
              clearable
              class="ele-fluid"
            >
              <el-option :label="this.$t('awb.package.status.s2')" :value="2" />
              <el-option :label="this.$t('awb.package.status.s3')" :value="3" />
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :lg="8" :md="12">
          <el-form-item
            :label="this.$t('order.package.customsClearanceResults') + ':'"
            prop="check"
          >
            <el-select
              v-model="where.check"
              :placeholder="this.$t('basics.pleaseChoose')"
              clearable
              @blur="where.take_delivery = ''"
              class="ele-fluid"
            >
              <el-option
                :label="this.$t('order.package.uncleared')"
                :value="3"
              />
              <el-option :label="this.$t('basics.inspection')" :value="2" />
              <el-option :label="this.$t('basics.release')" :value="1" />
              <el-option
                :label="$t('order.package.failedToClearCustoms')"
                :value="4"
              />
              <el-option :label="$t('basics.pendingClearance')" :value="5" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :lg="8" :md="12" v-permission="'awb:nouserSearch'">
          <el-form-item
            :label="this.$t('awb.package.double_check') + ':'"
            prop="double_check"
          >
            <el-select
              v-model="where.double_check"
              :placeholder="this.$t('basics.pleaseChoose')"
              clearable
              class="ele-fluid"
            >
              <el-option :label="this.$t('basics.no')" :value="1" />
              <el-option :label="this.$t('basics.yes')" :value="2" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :lg="8" :md="12" v-permission="'awb:nouserSearch'">
          <el-form-item
            :label="this.$t('awb.package.notice_custom') + ':'"
            prop="notice_custom"
          >
            <el-select
              v-model="where.notice_custom"
              :placeholder="this.$t('basics.pleaseChoose')"
              clearable
              class="ele-fluid"
            >
              <el-option :label="this.$t('basics.no')" :value="1" />
              <el-option :label="this.$t('basics.yes')" :value="2" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :lg="8" :md="12">
          <el-form-item
            :label="$t('order.package.failedToClearCustoms') + ':'"
            prop="take_delivery"
          >
            <el-select
              :disabled="where.check !== 4"
              v-model="where.take_delivery"
              :placeholder="this.$t('basics.pleaseChoose')"
              clearable
              class="ele-fluid"
            >
              <el-option
                :label="$t('order.package.failedToClearCustomsTakeDelivery')"
                :value="2"
              />
              <el-option
                :label="
                  $t(
                    'order.package.failedToClearCustomsTakeDeliveryWarehouseRetention'
                  )
                "
                :value="3"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :lg="8" :md="12">
          <el-form-item
            :label="`${$t('basics.createTime')}:`"
            prop="created_at"
          >
            <!-- <el-date-picker
              v-model="where.created_at"
              type="datetime"
              :placeholder="this.$t('basics.placeholder.time')"
              value-format="yyyy-MM-dd HH:mm:ss"
              style="width: 100%"
            /> -->
            <el-date-picker
              v-model="where.created_at"
              type="datetimerange"
              value-format="yyyy-MM-dd HH:mm:ss"
              :range-separator="this.$t('basics.to')"
              class="ele-fluid"
              :start-placeholder="this.$t('basics.beginTime')"
              :end-placeholder="this.$t('basics.endTime')"
            >
            </el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :lg="8" :md="12">
          <div class="ele-form-actions">
            <el-button
              type="primary"
              icon="el-icon-search"
              class="ele-btn-icon"
              @click="search"
            >
              {{ $t('basics.query') }}
            </el-button>
            <el-button @click="reset">{{ $t('basics.reset') }}</el-button>
          </div>
        </el-col>
      </el-row>
    </el-form>
  </el-card>
</template>

<script>
  import AddressSelect from '@/components/AddressSelect';
  import UserAddressSelect from '@/components/UserAddressSelect';
  // 默认表单数据

  const DEFAULT_WHERE = {
    bol_no: '',
    package_reference: '',
    box_no: '',
    track_no: '',
    address_code: '',
    access_warehouse: '',
    check: null,
    user_address_code: null,
    take_delivery: '',
    check_operate: '',
    views: ''
  };

  export default {
    name: 'outbound-fba-search',
    components: { UserAddressSelect, AddressSelect },
    data() {
      return {
        // 表单数据
        where: { ...DEFAULT_WHERE },
        // 搜索表单是否展开
        searchExpand: false,
        goodsCategoryList: [],
        category_id: ''
      };
    },
    methods: {
      /* 搜索 */
      search() {
        //解决搜索 也搜索下级的数据
        this.$emit('search', this.where);
      },
      /* 重置搜索 */
      reset() {
        this.where = { ...DEFAULT_WHERE };
        this.search();
      },
      /* 搜索展开/收起 */
      toggleExpand() {
        this.searchExpand = !this.searchExpand;
        this.$emit('expand-change', this.searchExpand);
      },
      /* 搜索展开/收起 */
      goodsCategoryFilterListClick(e) {
        this.goodsCategoryList = e.data;
      }
    }
  };
</script>
