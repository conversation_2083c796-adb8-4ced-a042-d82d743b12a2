<!-- 编辑弹窗 -->
<template>
  <ele-modal
    width="900px"
    :visible="visible"
    :close-on-click-modal="false"
    custom-class="ele-dialog-form"
    :title="this.$t('basics.detail')"
    @update:visible="updateVisible"
  >
    <el-form ref="form" :model="form" label-width="180px">
      <el-row :gutter="15">
        <el-col :sm="12">
          <el-form-item
            :label="this.$t('awb.package.declaration') + ':'"
            prop="declaration"
          >
            <el-input v-model="form.declaration" :disabled="true"></el-input>
          </el-form-item>
        </el-col>
        <el-col :sm="12">
          <el-form-item
            :label="this.$t('awb.package.mission') + ':'"
            prop="mission"
          >
            <el-input v-model="form.mission" :disabled="true"></el-input>
          </el-form-item>
        </el-col>
        <el-col :sm="12">
          <el-form-item label="LRN:" prop="lrn">
            <el-input v-model="form.lrn" :disabled="true"></el-input>
          </el-form-item>
        </el-col>
        <el-col :sm="12">
          <el-form-item label="MRN:" prop="mrn">
            <el-input v-model="form.mrn" :disabled="true"></el-input>
          </el-form-item>
        </el-col>
        <!-- <el-col :sm="12">
          <el-form-item label="任务编号:" prop="mission">
            <el-input v-model="form.mission" :disabled="true"></el-input>
          </el-form-item>
        </el-col> -->
        <el-col :sm="12">
          <el-form-item
            :label="this.$t('order.package.customsClearanceResults') + ':'"
            prop="check"
          >
            <el-select
              v-model="form.check"
              clearable
              :disabled="true"
              class="ele-fluid"
            >
              <el-option :label="this.$t('basics.release')" :value="1" />
              <el-option :label="this.$t('basics.inspection')" :value="2" />
              <el-option :label="this.$t('basics.unknown')" :value="3" />
              <el-option
                :label="this.$t('order.package.failedToClearCustoms')"
                :value="4"
              />
              <el-option
                :label="this.$t('basics.pendingClearance')"
                :value="5"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :sm="12">
          <el-form-item
            :label="this.$t('awb.package.double_check') + ':'"
            prop="double_check"
          >
            <el-select
              v-model="form.double_check"
              clearable
              :disabled="true"
              class="ele-fluid"
            >
              <el-option :label="this.$t('basics.no')" :value="1" />
              <el-option :label="this.$t('basics.yes')" :value="2" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :sm="12">
          <el-form-item
            :label="this.$t('awb.package.notice_custom') + ':'"
            prop="notice_custom"
          >
            <el-select
              v-model="form.notice_custom"
              clearable
              :disabled="true"
              class="ele-fluid"
            >
              <el-option :label="this.$t('basics.no')" :value="1" />
              <el-option :label="this.$t('basics.yes')" :value="2" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :sm="12" v-if="form.permit_through_date">
          <el-form-item
            :label="this.$t('awb.package.permit_through_date') + ':'"
            prop="permit_through_date"
          >
            <el-input
              clearable
              :maxlength="50"
              show-word-limit
              v-model="form.permit_through_date"
              :disabled="true"
            />
          </el-form-item>
        </el-col>
        <el-col :sm="12" v-if="form.notice_custom_date">
          <el-form-item
            :label="this.$t('awb.package.notice_custom_date') + ':'"
            prop="notice_custom_date"
          >
            <el-input
              clearable
              :maxlength="50"
              show-word-limit
              v-model="form.notice_custom_date"
              :disabled="true"
            />
          </el-form-item>
        </el-col>
        <el-col
          :sm="12"
          v-if="form.take_delivery_date && form.take_delivery !== 1"
        >
          <el-form-item
            :label="this.$t('order.package.failedToClearCustomsData') + ':'"
            prop="take_delivery_date"
          >
            <el-input
              clearable
              :maxlength="50"
              show-word-limit
              v-model="form.take_delivery_date"
              :disabled="true"
            />
          </el-form-item>
        </el-col>
        <el-col :sm="12" v-if="form.physical_controlled_date">
          <el-form-item
            :label="this.$t('awb.package.physical_controlled_date') + ':'"
            prop="physical_controlled_date"
          >
            <el-input
              clearable
              :maxlength="50"
              show-word-limit
              v-model="form.physical_controlled_date"
              :disabled="true"
            />
          </el-form-item>
        </el-col>
        <el-col :sm="12" v-if="form.control_mail_date">
          <el-form-item
            :label="this.$t('awb.package.control_mail_date') + ':'"
            prop="control_mail_date"
          >
            <el-input
              clearable
              :maxlength="50"
              show-word-limit
              v-model="form.control_mail_date"
              :disabled="true"
            />
          </el-form-item>
        </el-col>
        <el-col :sm="12" v-if="form.customs_inspection_data">
          <el-form-item
            :label="this.$t('awb.package.customs_inspection_data') + ':'"
            prop="customs_inspection_data"
          >
            <el-input
              clearable
              :maxlength="50"
              show-word-limit
              v-model="form.customs_inspection_data"
              :disabled="true"
            />
          </el-form-item>
        </el-col>
        <el-col
          :sm="12"
          v-if="form.take_delivery == 2 || form.take_delivery == 3"
        >
          <el-form-item
            :label="`${$t('order.package.failedToClearCustoms')}:`"
            prop="views"
          >
            <el-input
              clearable
              :maxlength="50"
              show-word-limit
              :value="
                [
                  '',
                  $t('basics.no'),
                  $t('order.package.failedToClearCustomsTakeDelivery'),
                  $t(
                    'order.package.failedToClearCustomsTakeDeliveryWarehouseRetention'
                  )
                ][form.take_delivery]
              "
              :disabled="true"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div slot="footer">
      <!-- <el-button @click="updateVisible(false)">
        {{ $t('basics.cancel') }}
      </el-button>
      <el-button type="primary" :loading="loading" @click="save">
        {{ $t('basics.save') }}
      </el-button> -->
    </div>
  </ele-modal>
</template>

<script>
  import { info } from '@/api/order/package';

  const DEFAULT_FORM = {
    id: null,
    declaration: '',
    control_mail_date: '',
    physical_controlled_date: '',
    take_delivery_date: '',
    notice_custom_date: '',
    mission: '',
    mrn: '',
    lrn: '',
    check: null,
    note: null,
    permit_through: null,
    double_check: null,
    notice_custom: null,
    customs_inspection: null,
    customs_inspection_data: null,
    take_delivery: ''
  };

  export default {
    name: 'EditAwb',
    components: {},
    props: {
      // 弹窗是否打开
      visible: Boolean,
      // 修改回显的数据
      data: Object
    },
    data() {
      return {
        // 表单数据
        form: { ...DEFAULT_FORM },
        // 表单验证规则
        // 提交状态
        loading: false,
        // 是否是修改
        isUpdate: false
      };
    },
    created() {
      console.log(this.data);
    },
    methods: {
      getInfo() {
        info(this.data).then((res) => {
          this.$util.assignObject(this.form, {
            ...res?.result?.parcel
          });
          console.log(this.form);
        });
      },
      /* 更新visible */
      updateVisible(value) {
        this.$emit('update:visible', value);
      },
      /* 角色选择回调*/
      roleSelectCallback(value) {
        this.form.role_type = value.type;
      }
    },
    watch: {
      async visible(visible) {
        console.log(this.data);
        if (visible) {
          this.getInfo();
          let data = this.data;
          if (this.data) {
            this.$util.assignObject(this.form, {
              ...data
            });
            this.isUpdate = true;
          } else {
            this.isUpdate = false;
          }
        } else {
          this.$refs['form'].clearValidate();
          this.form = { ...DEFAULT_FORM };
        }
      }
    }
  };
</script>
