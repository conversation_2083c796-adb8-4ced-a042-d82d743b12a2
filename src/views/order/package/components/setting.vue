<!-- 编辑弹窗 -->
<template>
  <ele-modal
    width="720px"
    :visible="visible"
    :close-on-click-modal="false"
    custom-class="ele-dialog-form"
    :title="this.$t('awb.package.btn1')"
    @update:visible="updateVisible"
  >
    <el-form ref="form" :model="form" :rules="rules" label-width="100px">
      <el-row type="flex">
        <el-col :span="12">
          <el-form-item :label="this.$t('basics.status') + ':'" prop="type">
            <el-select v-model="form.type" clearable class="ele-fluid">
              <!-- 通知海关  -->
              <el-option
                :label="this.$t('awb.package.notice_custom')"
                :value="2"
              />
              <!-- 海关查验  -->
              <el-option
                :label="this.$t('awb.package.customs_inspection')"
                :value="5"
              />
              <!-- 放行  -->
              <el-option
                :label="this.$t('awb.package.permit_through')"
                :value="3"
              />
              <!-- 清关失败（海关取走）-->
              <el-option
                :label="$t('order.package.failedToClearCustomsTakeDelivery')"
                :value="4"
              />
              <!-- 清关失败（留仓）-->
              <el-option
                :label="
                  $t(
                    'order.package.failedToClearCustomsTakeDeliveryWarehouseRetention'
                  )
                "
                :value="6"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="this.$t('basics.time') + ':'" prop="date">
            <el-date-picker
              v-model="form.date"
              :placeholder="$t('basics.pleaseChoose')"
              type="datetime"
              value-format="yyyy-MM-dd HH:mm:ss"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row v-show="[4, 6].indexOf(this.form.type) > -1">
        <el-col :sm="14">
          <el-form-item :label="`${$t('clearanceError.error_value')}:`" prop="error_value" label-width="130px">
            <el-select
              clearable
              multiple
              filterable
              v-model="clearance_error_select"
              class="ele-fluid">
              <el-option
                v-for="item in clearance_error"
                :key="item.value" :value="item.value"
                :label="item.label">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div slot="footer">
      <el-button @click="updateVisible(false)">
        {{ $t('basics.cancel') }}
      </el-button>
      <el-button type="primary" :loading="loading" @click="save">
        {{ $t('basics.save') }}
      </el-button>
    </div>
  </ele-modal>
</template>

<script>
  import { settingPack } from '@/api/order/package';
  import { lists } from "@/api/basics/clearanceError";

  const DEFAULT_FORM = {
    id: null,
    date: '',
    type: null
  };

  export default {
    name: 'UserEdit',
    components: {},
    props: {
      visible: Boolean,
      data: Array
    },
    data() {
      return {
        clearance_error: [],
        clearance_error_select: [],
        // 表单数据
        form: { ...DEFAULT_FORM },
        // 表单验证规则
        rules: {
          date: [
            {
              required: true,
              message: this.$t('basics.pleaseChoose'),
              trigger: ['blur', 'change']
            }
          ]
        },
        // 提交状态
        loading: false,
        // 是否是修改
        isUpdate: false
      };
    },
    methods: {
      /* 保存编辑 */
      save() {
        this.$refs['form'].validate((valid) => {
          if (!this.form.type) {
            return false;
          }
          if (!valid) {
            return false;
          }
          this.loading = true;
          let temp = [];
          for (let item of this.data) {
            temp.push(item.id);
          }
          let value = 2;
          if (this.form.type == 6) {
            this.form.type = 4;
            value = 3;
          }

          let error_value = [];
          for (let ces of this.clearance_error_select)
            for (let ce of this.clearance_error)
              if (ces == ce.value) error_value.push(ce);

          const data = {
            track_id_Arr: temp,
            type: this.form.type,
            date: this.form.date,
            value: value,
            error_value: error_value
          };
          settingPack(data)
            .then(() => {
              this.loading = false;
              this.$message.success(this.$t('basics.success'));
              this.updateVisible(false);
              this.$emit('done');
            })
            .catch((e) => {
              this.loading = false;
              this.$message.error(e);
            });
        });
      },
      /* 更新visible */
      updateVisible(value) {
        this.$emit('update:visible', value);
      },
      /* 角色选择回调*/
      roleSelectCallback(value) {
        this.form.role_type = value.type;
      },
      clearanceErrorList() {
        lists()
          .then(({ count, list }) => {
            let temp_list = [];
            for (let x of list) {
              temp_list.push({
                key: x.id,
                value: x.error_value,
                label: x.error_value
              });
            }
            this.clearance_error = temp_list;
          })
          .catch((err) => {
            console.log(err);
          });
      }
    },
    watch: {
      async visible(visible) {
        this.clearanceErrorList();
        if (visible) {
          if (this.data) {
            this.isUpdate = true;
          } else {
            this.isUpdate = false;
          }
        } else {
          this.$refs['form'].clearValidate();
          this.form = { ...DEFAULT_FORM };
        }
      }
    }
  };
</script>
