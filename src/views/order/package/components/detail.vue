<!-- 编辑弹窗 -->
<template>
  <ele-modal
    width="1080px"
    :visible="visible"
    :close-on-click-modal="false"
    custom-class="ele-dialog-form"
    :title="$t('basics.detail')"
    @update:visible="updateVisible"
  >
    <el-form ref="form" :model="form" label-width="170px">
      <el-row>
        <el-col :sm="22">
          <el-form-item
            :label="$t('awb.awb.menu.m1')"
            prop="bol_no"
            class="el-form-item-time"
          >
            <div class="steps">
              <div class="items" v-for="(item, index) in timeline" :key="index">
                <div class="bor" v-if="item && item.state">
                  <el-tooltip
                    class="item"
                    effect="dark"
                    :content="item.time"
                    placement="top-start"
                  >
                    <span class="iconfont" style="color: #ee4737">{{
                      timelineImg[index].icoName
                    }}</span>
                  </el-tooltip>
                  <div
                    style="
                      width: 80px;
                      margin-top: 8px;
                      margin-left: -23px;
                      text-align: center;
                    "
                  >
                    {{ timelineImg[index].label }}</div
                  >
                </div>
                <div class="bor" v-else>
                  <span class="iconfont">{{ timelineImg[index].icoName }}</span>
                  <div
                    style="
                      width: 80px;
                      margin-top: 8px;
                      margin-left: -23px;
                      text-align: center;
                    "
                  >
                    {{ timelineImg[index].label }}</div
                  >
                </div>
                <div class="hr"></div>
              </div>
            </div>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <el-divider></el-divider>
    <el-form
      ref="form"
      :model="form"
      label-width="205px"
      style="margin-top: 40px"
    >
      <el-row :gutter="15">
        <el-col :sm="12">
          <el-form-item :label="`${$t('awb.noa.awbno')}:`" prop="bol_no">
            <el-input
              clearable
              :maxlength="50"
              show-word-limit
              v-model="form.bol_no"
              :disabled="true"
            />
          </el-form-item>
        </el-col>
        <el-col :sm="12">
          <el-form-item
            :label="`${$t('order.package.packageReference')}:`"
            prop="packageReference"
          >
            <el-input
              clearable
              :maxlength="50"
              show-word-limit
              v-model="form.package_reference"
              :disabled="true"
            />
          </el-form-item>
        </el-col>
        <el-col :sm="12">
          <el-form-item :label="`${$t('awb.box.box')}:`" prop="box_no">
            <el-input
              clearable
              :maxlength="50"
              show-word-limit
              v-model="form.box_no"
              :disabled="true"
            />
          </el-form-item>
        </el-col>
        <el-col :sm="12">
          <el-form-item :label="`${$t('awb.package.no')}:`" prop="track_no">
            <el-input
              clearable
              :maxlength="50"
              show-word-limit
              v-model="form.track_no"
              :disabled="true"
            />
          </el-form-item>
        </el-col>
        <el-col :sm="12">
          <el-form-item
            :label="`${$t('userAddress.user_addresses')}:`"
            prop="user_address_code"
          >
            <el-input
              clearable
              :maxlength="50"
              show-word-limit
              v-model="form.user_address_code"
              :disabled="true"
            />
          </el-form-item>
        </el-col>
        <el-col :sm="12">
          <el-form-item
            :label="`${$t('order.package.customsClearanceResults')}:`"
            prop="check"
          >
            <el-input
              clearable
              :maxlength="50"
              show-word-limit
              v-model="form.checkText"
              :disabled="true"
            />
          </el-form-item>
        </el-col>
        <el-col :sm="12">
          <el-form-item
            :label="`${$t('awb.package.access_warehouse')}:`"
            prop="access_warehouse"
          >
            <el-input
              clearable
              :maxlength="50"
              show-word-limit
              v-model="form.viewsText"
              :disabled="true"
            />
          </el-form-item>
        </el-col>
        <el-col :sm="12" v-if="form.views == 2 || form.views == 3">
          <el-form-item
            :label="this.$t('order.package.scanStatus') + ':'"
            prop="views"
          >
            <el-input
              clearable
              :maxlength="50"
              show-word-limit
              :value="
                [
                  '',
                  '',
                  $t('awb.package.status.s2'),
                  $t('awb.package.status.s3')
                ][form.views]
              "
              :disabled="true"
            />
          </el-form-item>
        </el-col>
        <el-col :sm="12">
          <el-form-item
            v-if="form.take_delivery == 2 || form.take_delivery == 3"
            :label="`${$t('order.package.failedToClearCustoms')}:`"
            prop="views"
          >
            <el-input
              clearable
              :maxlength="50"
              show-word-limit
              :value="
                [
                  '',
                  $t('basics.no'),
                  $t('order.package.failedToClearCustomsTakeDelivery'),
                  $t(
                    'order.package.failedToClearCustomsTakeDeliveryWarehouseRetention'
                  )
                ][form.take_delivery]
              "
              :disabled="true"
            />
          </el-form-item>
        </el-col>
        <el-col :sm="12">
          <el-form-item
            v-if="form.clearance_error != ''"
            :label="`${$t('clearanceError.error_value')}:`"
            prop="clearance_error"
          >
            <el-input
              type="textarea"
              clearable
              autosize
              show-word-limit
              :value="this.form.clearance_error"
              :disabled="true"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div style="width: 100%; height: 20px"></div>
  </ele-modal>
</template>

<script>
  import { info } from '@/api/order/package';
  const DEFAULT_FORM = {
    id: null,
    bol_no: '',
    flight_time: '',
    flight_no: '',
    gross_mass_kg: '',
    exchange_flag: null,
    lgg: '',
    b2b: '',
    t1: '',
    agent: '',
    plan_start_date: '',
    plan_end_date: '',
    remark: '',
    bol_batch: '',
    time_line: [],
    expensive: null,
    clearance_fee: null,
    attach_fee: null,
    address: [],
    gross_weight: null,
    package_reference: null,
    total_value: null,
    take_delivery: ''
  };

  export default {
    name: 'EditAwb',
    components: {},
    props: {
      // 弹窗是否打开
      visible: Boolean,
      // 修改回显的数据
      data: Object
    },
    data() {
      return {
        // 表单数据
        form: { ...DEFAULT_FORM },
        showViewer: false,
        columns: [
          {
            columnKey: 'index',
            type: 'index',
            width: 45,
            showOverflowTooltip: true,
            fixed: 'left'
          },
          {
            prop: 'name',
            label: this.$t('car.address'), //地址名称
            showOverflowTooltip: true,
            minWidth: 110
          },
          {
            prop: 'num',
            label: this.$t('awb.awb.forecastNumber'), //预报数
            showOverflowTooltip: true,
            minWidth: 110
          },
          {
            prop: 'stock_in',
            label: this.$t('awb.awb.receiptQuantity'), //入库数
            showOverflowTooltip: true,
            minWidth: 110
          },
          {
            prop: 'stock_out',
            label: this.$t('awb.awb.deliveryQuantity'), //出库数
            showOverflowTooltip: true,
            minWidth: 110
          }
        ],
        columnsCmr: [
          {
            columnKey: 'index',
            type: 'index',
            width: 45,
            showOverflowTooltip: true,
            fixed: 'left'
          },
          {
            prop: 'cmr_no',
            label: 'CMR',
            showOverflowTooltip: true,
            minWidth: 110
          },
          {
            prop: 'type',
            label: this.$t('car.type'), //车单类型,
            showOverflowTooltip: true,
            minWidth: 110,
            formatter: (row, column, cellValue) => {
              return {
                1: this.$t('car.in'),
                2: this.$t('car.out'),
                3: this.$t('car.air')
              }[cellValue];
            }
          },
          {
            columnKey: 'action',
            label: this.$t('basics.action'),
            width: 220,
            align: 'center',
            resizable: false,
            slot: 'action',
            hideInSetting: true,
            fixed: 'right'
          }
        ],
        datasource: [],
        datasourceCmr: [],
        // 提交状态
        loading: false,
        // 是否是修改
        isUpdate: false,
        timeline: [],
        //提单审核通过、航班起飞、航班落地、清关完成、入仓、出仓、到达目的仓库
        timelineImg: [
          //提单审核通过
          {
            label: this.$t('awb.awb.times.time1'),
            icoName: '\ue613'
          },
          //航班起飞
          // {
          // label: this.$t('awb.awb.times.time2'),
          // icoName: '\ue6b1'
          //},
          //航班落地
          {
            label: this.$t('awb.awb.times.time3'),
            icoName: '\ue6b2'
          },
          //清关完成
          {
            label: this.$t('awb.awb.times.time4'),
            icoName: '\ue64f'
          },
          //入仓
          {
            label: this.$t('awb.awb.times.time9'),
            icoName: '\ue606'
          },
          //出仓
          {
            label: this.$t('awb.awb.times.time10'),
            icoName: '\ue602'
          },
          //到达目的仓库
          {
            label: this.$t('awb.awb.times.time11'),
            icoName: '\ue89d'
          }
        ]
      };
    },
    methods: {
      openUrl(url) {
        window.open(url);
      },
      closeViewer() {
        this.showViewer = false;
      },
      showImage(path) {
        this.srcList = path;
        this.showViewer = true;
        setTimeout(() => {
          this.$refs.elImageViewer.handleActions('clocelise');
        }, 20);
      },
      jump(row) {
        this.$router.push('/carlist?cmr=' + row.cmr_no);
      },
      /* 更新visible */
      updateVisible(value) {
        this.$emit('update:visible', value);
      },
      /* 角色选择回调*/
      roleSelectCallback(value) {
        this.form.role_type = value.type;
      }
    },
    watch: {
      async visible(visible) {
        if (visible) {
          //报错
          let a = await info(this.data);
          this.hasRole = a.hasRole;
          // console.log(a.result.address);
          // // this.form = a.result
          // // let data = this.data;

          if (this.data) {
            this.form = a.result.parcel;
            this.form.checkText =
              this.form.check == 1
                ? this.$t('basics.release')
                : this.form.check == 2
                ? this.$t('basics.inspection')
                : this.form.check == 3
                ? this.$t('basics.unknown')
                : this.form.check == 4
                ? this.$t('order.package.failedToClearCustoms')
                : this.form.check == 5
                ? this.$t('basics.pendingClearance')
                : '';
            this.form.viewsText = this.$t(
              'awb.package.access_warehouse_status'
            )[this.form.access_warehouse];
            console.log(this.form);
            this.isUpdate = true;
            let { timeline } = a.result;
            this.timeline = timeline.map((i, k) => {
              if (k === 2 || k === 3) {
                i.state = i.state && timeline[0].state;
              } else if (k !== 0) {
                i.state = i.state && timeline[k - 1].state;
              }
              return i;
            });
            let clearance_error = '',
              clearance_error_list =
                this.form.clearance_error != null
                  ? JSON.parse(this.form.clearance_error)
                  : [];
            for (let x of clearance_error_list) clearance_error += x + '\r\n';
            this.form.clearance_error = clearance_error;
          } else {
            this.isUpdate = false;
          }
        } else {
          this.$refs['form'].clearValidate();
          this.form = { ...DEFAULT_FORM };
        }
      }
    }
  };
</script>
<style lang="scss">
  .steps {
    width: 100%;
    display: flex;
    height: 80px;
    align-items: center;
    justify-content: center;
    .items {
      display: flex;
      align-items: center;
      .bor {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        padding: 13px;
        box-sizing: border-box;
        flex-shrink: 0;
        border: 1px solid #cecece;
        span {
          width: 100%;
          display: block;
          font-size: 30px;
          line-height: 30px;
          text-align: center;
        }
      }
      .active {
        background: #1296db;
        border: 1px solid #1296db;
      }
      .hr {
        width: 40px;
        height: 2px;
        background: #cecece;
        margin: 0 10px;
      }
      .hractive {
        background: #1296db;
      }
    }
    .items:last-child {
      .hr {
        display: none;
      }
    }
  }
  .el-form-item-time {
    .el-form-item__label {
      line-height: 60px;
    }
  }
</style>
