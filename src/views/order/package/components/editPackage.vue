<!-- 编辑弹窗 -->
<template>
  <ele-modal
    width="1080px"
    :visible="visible"
    :close-on-click-modal="false"
    custom-class="ele-dialog-form"
    :title="$t('basics.detail')"
    @update:visible="updateVisible"
  >
    <ele-pro-table
      ref="table"
      v-loading="start_update"
      :initLoad="false"
      :columns="columns"
      :need-page="false"
      :datasource="datasource"
    >
      <template slot="package_reference" slot-scope="{ row }">
        <el-input
          clearable
          ref="packageInput"
          class="update-yes"
          :disabled="row.check !== 3"
          v-model="row.package_reference">
          <!--<template slot="append" v-if="row.is_update > 0 || row.package_reference.length === 0">
            <div v-if="row.package_reference.length === 0">
              <span style="color:#DD4A68;">不可为空</span>
            </div>
            <div class="update-icon" v-else>
              <i class="el-icon-loading" v-if="row.is_update === 1"></i>
              <i class="el-icon-circle-check" style="color: #2CB897;"
                 v-else-if="row.is_update === 2"></i>
              <i class="el-icon-circle-close" style="color: #DD4A68;"
                 v-else-if="row.is_update === 3"></i>
            </div>
          </template>-->
        </el-input>
      </template>
    </ele-pro-table>
    <div style="width: 100%; height: 20px"></div>
    <div slot="footer">
      <el-button @click="updateVisible(false)">
        {{ $t('basics.cancel') }}
      </el-button>
      <el-button type="primary" :loading="loading" @click="savePackage()">
        {{ $t('basics.save') }}
      </el-button>
    </div>
  </ele-modal>
</template>

<script>
  import { editPackage } from '@/api/order/package';
  export default {
    name: 'EditPackage',
    components: {},
    props: {
      visible: Boolean, // 弹窗是否打开
      data: Array, // 修改回显的数据
    },
    data() {
      return {
        input: '',
        start_update: false, // 开始更新
        columns: [
          {
            prop: 'bol_no',
            label: this.$t('awb.noa.awbno'),
            showOverflowTooltip: true,
            minWidth: 110
          },
          {
            prop: 'track_no',
            label: this.$t('awb.package.no'),
            showOverflowTooltip: true,
            minWidth: 110,
            show: false
          },
          {
            prop: 'package_reference_raw',
            label: this.$t('order.package.packageReferenceOld'),
            showOverflowTooltip: true,
            minWidth: 110
          },
          {
            slot: "package_reference",
            label: this.$t('order.package.packageReferenceNew'),
            showOverflowTooltip: true,
            minWidth: 110
          },
        ],
        datasource: [],
        loading: false, // 提交状态
        isUpdate: false, // 是否是修改
      };
    },
    mounted() {},
    methods: {
      openUrl(url) {
        window.open(url);
      },
      /* 更新visible */
      updateVisible(value) {
        this.$emit('update:visible', value);
      },
      objectArraySort(keyName) {
        return function(objectN, objectM) {
          let valueN = objectN[keyName], valueM = objectM[keyName];
          if (valueN < valueM) return 1;
          else if (valueN > valueM) return -1;
          else return 0;
        };
      },
      savePackage() {
        this.start_update = true;
        let params = [];
        for (let k in this.datasource) {
          this.datasource[k].is_update = 1;
          params.push({
            id: this.datasource[k].id,
            bol_no: this.datasource[k].bol_no,
            check: this.datasource[k].check,
            track_no: this.datasource[k].track_no,
            package_reference: this.datasource[k].package_reference,
            package_reference_raw: this.datasource[k].package_reference_raw,
          });
        }
        editPackage(params)
          .then(success => {
            console.log(success);
            this.datasource = success.result;
            this.$emit("done");
            this.$emit('update:visible', false);
          })
          .catch(error => {
            console.log(error);
            this.$message.error(error);
          })
          .finally(() => {
            this.start_update = false;
          });
      },
    },
    watch: {
      async visible(visible) {
        if (visible) {
          this.datasource = [];
          console.log(this.data, "data");
          setTimeout(() => {
            for (let x of this.data) {
              this.datasource.push({
                id: x.id,
                bol_no: x.bol_no,
                check: x.check,
                track_no: x.track_no,
                package_reference_raw: x.package_reference,
                package_reference: x.package_reference,
                is_update: 0
              });
            }
            this.datasource.sort(this.objectArraySort('id'));
            console.log(this.datasource, "datasource");
            // this.$refs.table.reload();
          }, 200);
        }
      }
    }
  };
</script>
<style lang="scss">
  .update-yes .el-input-group__prepend {
    border: #00ff80;
  }

  .update-no .el-input-group__prepend {
    border: #DD4A68;
  }

  .steps {
    width: 100%;
    height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;

    .items {
      display: flex;
      align-items: center;
      cursor: pointer;
      .bor {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        padding: 13px;
        box-sizing: border-box;
        flex-shrink: 0;
        border: 1px solid #cecece;

        span {
          width: 100%;
          display: block;
          font-size: 30px;
          line-height: 30px;
          text-align: center;
        }
      }

      .active {
        background: #1296db;
        border: 1px solid #1296db;
      }

      .hr {
        width: 40px;
        height: 2px;
        background: #cecece;
        margin: 0 10px;
      }

      .hractive {
        background: #1296db;
      }
    }

    .items:last-child {
      .hr {
        display: none;
      }
    }
  }

  .el-form-item-time {
    .el-form-item__label {
      line-height: 60px;
    }
  }

  .update-icon {
    i {
      font-size: 14pt;
    }
  }
</style>
