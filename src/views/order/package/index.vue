<template>
  <div class="ele-body">
    <el-card shadow="never">
      <!-- 搜索表单 -->
      <outbound-fba-search @search="reload" />
      <!-- 数据表格 -->
      <ele-pro-table
        ref="table"
        :initLoad="false"
        :columns="columns"
        :datasource="datasource"
        :hideOnSinglePage="true"
        :selection.sync="selection"
        @selection-change="handleSelectionChange"
      >
        <template slot="toolbar">
          <el-button
            size="small"
            type="primary"
            icon="el-icon-plus"
            class="ele-btn-icon"
            @click="packSetting()"
            v-permission="'package:update'"
          >
            {{ $t('awb.package.btn1') }}
          </el-button>
          <el-button
            size="small"
            type="primary"
            icon="el-icon-plus"
            class="ele-btn-icon"
            @click="doubleCheck()"
            v-permission="'package:update'"
          >
            {{ $t('awb.package.btn2') }}
          </el-button>
          <!-- <el-button
            v-permission="'parcels:multiParcelOut'"
            size="small"
            type="primary"
            icon="el-icon-plus"
            class="ele-btn-icon"
            @click="openOutDialog()"
          >
            {{ $t('awb.package.out') }}
          </el-button> -->

          <el-button
            size="small"
            icon="el-icon-download"
            class="ele-btn-icon"
            @click="exportXls"
            v-permission="'parcels:export'"
          >
            {{ $t('basics.export') }}
          </el-button>
          <el-button
            size="small"
            icon="el-icon-edit"
            class="ele-btn-icon"
            @click="openPackage()"
            v-permission="'parcels:editPackage'"
          >
            {{ $t('awb.package.edit') }}
          </el-button>
        </template>
        <template slot="action" slot-scope="{ row }">
          <el-link
            @click="showDetailFn(row)"
            type="primary"
            icon="el-icon-s-operation"
            :underline="false"
          >
            {{ $t('awb.awb.see') }}
          </el-link>
          <el-link
            @click="showlogs(row)"
            v-permission="'package:checkinfo'"
            type="primary"
            icon="el-icon-s-operation"
            :underline="false"
          >
            {{ $t('awb.package.btn3') }}
          </el-link>

          <!-- <el-popconfirm
            v-show="row.status === '1'"
            class="ele-action"
            title="确定要取消该订单？"
            @confirm="remove(row)"
          >
            <el-link type="danger" slot="reference" :underline="false">
              取消
            </el-link>
          </el-popconfirm> -->
        </template>
      </ele-pro-table>
    </el-card>
    <update-file :visible.sync="showImport" @done="reload" />
    <show-info :visible.sync="showEdit" :data="currentRow" />
    <setting-pack :visible.sync="showEditPack" :data="current" @done="reload" />

    <detail-package :visible.sync="showDetail" :data="currentRow" />
    <edit-package :visible.sync="showPackage" :data="current" @done="reload" />
  </div>
</template>

<script>
  import { cancel, lists, settingPack } from '@/api/order/package';
  import UpdateFile from './components/update';
  import OutboundFbaSearch from './components/outbound-fba-search';
  import SettingPack from './components/setting';

  import ShowInfo from './components/edit';
  import DetailPackage from './components/detail';
  import EditPackage from './components/editPackage';
  import { hasPermission } from '@/utils/permission';
  import { utils, writeFile } from 'xlsx';
  export default {
    name: 'SystemUser',
    components: {
      EditPackage,
      OutboundFbaSearch,
      UpdateFile,
      SettingPack,
      ShowInfo,
      DetailPackage
      // UserSearch,
      // UserEdit,
      // UserImport,
      // UserChangePassword
    },
    data() {
      return {
        // 表格列配置
        columns: [
          {
            width: 45,
            type: 'selection',
            columnKey: 'selection',
            align: 'center'
          },
          {
            prop: 'track_no',
            label: this.$t('awb.package.no'),
            showOverflowTooltip: true,
            minWidth: 180
          },
          {
            prop: 'package_reference',
            label: this.$t('order.package.packageReference'),
            showOverflowTooltip: true,
            minWidth: 180
          },
          {
            prop: 'bol_no',
            label: this.$t('awb.noa.awbno'),
            showOverflowTooltip: true,
            minWidth: 110
          },
          {
            prop: 'box_no',
            label: this.$t('awb.box.box'),
            showOverflowTooltip: true,
            minWidth: 150
          },
          {
            prop: 'address',
            label: this.$t('basicsMenu.address.name'),
            showOverflowTooltip: true,
            minWidth: 110
          },
          {
            prop: 'user_address_code',
            label: this.$t('userAddress.user_addresses'),
            showOverflowTooltip: true,
            minWidth: 110
          },
          {
            prop: 'user_id',
            label: this.$t('awb.awb.col9'),
            showOverflowTooltip: true,
            minWidth: 80
          },
          {
            prop: 'check',
            label: this.$t('order.package.customsClearanceResults'),
            showOverflowTooltip: true,
            minWidth: 80,
            formatter: (row, column, cellValue) => {
              return [
                '',
                this.$t('basics.release'),
                this.$t('basics.inspection'),
                this.$t('basics.unknown'),
                this.$t('order.package.failedToClearCustoms'),
                this.$t('basics.pendingClearance')
              ][cellValue];
            }
          },
          {
            prop: 'double_check',
            label: this.$t('awb.package.double_check'),
            showOverflowTooltip: true,
            minWidth: 80,
            formatter: (row, column, cellValue) => {
              return ['', this.$t('basics.no'), this.$t('basics.yes')][
                cellValue
              ];
            }
          },
          {
            prop: 'notice_custom',
            label: this.$t('awb.package.notice_custom'),
            showOverflowTooltip: true,
            minWidth: 80,
            formatter: (row, column, cellValue) => {
              return ['', this.$t('basics.no'), this.$t('basics.yes')][
                cellValue
              ];
            }
          },
          {
            prop: 'access_warehouse',
            label: this.$t('awb.package.access_warehouse'),
            showOverflowTooltip: true,
            minWidth: 100,
            formatter: (row, column, cellValue) => {
              return this.$t('awb.package.access_warehouse_status')[cellValue];
            }
          },
          {
            prop: 'created_at',
            label: this.$t('basics.createTime'),
            showOverflowTooltip: true,
            minWidth: 110
          },
          // {
          //   prop: 'created_at',
          //   label: this.$t('basics.status'),
          //   showOverflowTooltip: true,
          //   minWidth: 110,
          //   formatter: (row, column, cellValue) => {
          //     return this.$util.toDateString(parseInt(cellValue));
          //   }
          // },
          {
            columnKey: 'action',
            label: this.$t('basics.action'),
            width: 220,
            resizable: false,
            slot: 'action',
            hideInSetting: true,
            fixed: 'right'
          }
        ],
        userColumns: [
          {
            columnKey: 'index',
            type: 'index',
            width: 80,
            role: 'sonOwner', //什么角色下不显示
            showOverflowTooltip: true,
            fixed: 'left'
          },
          {
            prop: 'track_no',
            label: this.$t('awb.package.no'),
            showOverflowTooltip: true,
            minWidth: 200
          },
          {
            prop: 'package_reference',
            label: this.$t('order.package.packageReference'),
            showOverflowTooltip: true,
            minWidth: 180
          },
          {
            prop: 'bol_no',
            label: this.$t('awb.noa.awbno'),
            showOverflowTooltip: true,
            minWidth: 110
          },
          {
            prop: 'box_no',
            label: this.$t('awb.box.box'),
            showOverflowTooltip: true,
            minWidth: 110
          },
          {
            prop: 'user_address_code',
            label: this.$t('userAddress.user_addresses'),
            showOverflowTooltip: true,
            minWidth: 110
          },
          {
            prop: 'check',
            label: this.$t('order.package.customsClearanceResults'),
            showOverflowTooltip: true,
            minWidth: 80,
            formatter: (row, column, cellValue) => {
              return [
                '',
                this.$t('basics.release'),
                this.$t('basics.inspection'),
                this.$t('basics.unknown'),
                this.$t('order.package.failedToClearCustoms')
              ][cellValue];
            }
          },
          {
            prop: 'access_warehouse',
            label: this.$t('awb.package.access_warehouse'),
            showOverflowTooltip: true,
            minWidth: 80,
            formatter: (row, column, cellValue) => {
              return this.$t('awb.package.access_warehouse_status')[cellValue];
            }
          },
          {
            prop: 'created_at',
            label: this.$t('basics.createTime'),
            showOverflowTooltip: true,
            minWidth: 110
          },
          {
            columnKey: 'action',
            label: this.$t('basics.action'),
            width: 220,
            align: 'center',
            resizable: false,
            slot: 'action',
            hideInSetting: true,
            fixed: 'right'
          }
        ],
        baseUrl: this.$store.getters.user.info.web_url,
        // 表格选中数据
        selection: [],
        // 当前编辑数据
        current: null,
        currentRow: null,
        // 是否显示编辑弹窗
        showEdit: false,
        showEditPack: false,
        // 是否显示导入弹窗
        showImport: false,
        // 显示编辑包裹号弹窗
        showPackage: false,
        // 是否显修改密码
        userId: null,
        showChangePassword: false,
        multipleSelection: [],
        showDetail: false,
        showDialog: false,
        lastWhere: {},
        pageNum: 5000,
        pageAll: 1,
        loading: null
      };
    },
    watch: {
      async selection() {
        console.log(this.selection);
      }
    },
    created() {
      //或者登陆不显示货主名字其他登录显示名称
      console.log(this.$store.state.user.info.role_type);
      if (
        this.$store.state.user.info.role_type === '2' ||
        this.$store.state.user.info.role_type === '3'
      ) {
        const index = this.columns.findIndex(function (key) {
          return key.prop === 'username';
        });
        this.columns.splice(index, index + 1);
      }
      if (!hasPermission('awb:nouserSearch')) {
        this.columns = this.userColumns;
      }
    },
    activated() {
      this.reload();
    },
    methods: {
      showDetailFn(row) {
        this.currentRow = row;
        this.showDetail = true;
      },
      /* 表格数据源 */
      datasource({ page, limit, where, order }) {
        return lists({
          ...where,
          ...order,
          page: page,
          num: limit
        });
      },
      doubleCheck() {
        this.loading = true;
        if (this.multipleSelection.length == 0) {
          return;
        }
        let temp = [];
        for (let item of this.multipleSelection) {
          temp.push(item.id);
        }
        const data = {
          track_id_Arr: temp,
          type: 1,
          value: 2
        };
        settingPack(data)
          .then(() => {
            this.loading = false;
            this.$message.success(this.$t('basics.success'));
            this.reload();
          })
          .catch((e) => {
            this.loading = false;
            this.$message.error(e);
          });
      },
      packSetting() {
        this.current = this.multipleSelection;
        this.showEditPack = true;
      },

      handleSelectionChange(val) {
        this.multipleSelection = val;
        console.log(this.multipleSelection);
      },
      /* 刷新表格 */
      reload(where) {
        this.lastWhere = where;
        this.$refs.table.reload({ page: 1, where: where });
      },
      /* 打开编辑弹窗 */
      showlogs(row) {
        this.currentRow = row;
        this.showEdit = true;
      },
      openPackage() {
        if (this.multipleSelection.length > 0) {
          let list = [];
          for (let x of this.multipleSelection) {
            // if (x.check === 3)
            if (x.clearance === 1) list.push({ ...x });
          }
          if (list.length > 0) {
            this.current = list;
            this.showPackage = true;
          } else {
            this.$message(this.$t('order.package.selectCheckError'));
          }
        } else {
          this.$message(this.$t('order.package.selectNull'));
        }
      },
      /* 打开导入弹窗 */
      openImport() {
        this.showImport = true;
      },
      exportXlsOth(pages, array) {
        var _this = this;
        let roleUser = this.$hasRole('sonOwner');
        const checks = [
          '',
          this.$t('basics.release'),
          this.$t('basics.inspection'),
          this.$t('basics.unknown'),
          this.$t('order.package.failedToClearCustoms')
        ];
        const status = this.$t('awb.package.access_warehouse_status');
        const yesOrNo = ['', this.$t('basics.no'), this.$t('basics.yes')];
        lists({
          ...this.lastWhere,
          page: pages,
          num: this.pageNum
        }).then((res) => {
          res.list.forEach((d) => {
            if (roleUser) {
              array.push([
                d.track_no,
                d.package_reference,
                d.bol_no,
                d.box_no,
                d.user_address_code,
                checks[d.check],
                status[d.access_warehouse],
                d.created_at
              ]);
            } else {
              array.push([
                d.track_no,
                d.package_reference,
                d.bol_no,
                d.box_no,
                d.address,
                d.user_address_code,
                d.user_id,
                checks[d.check],
                yesOrNo[d.double_check],
                yesOrNo[d.notice_custom],
                status[d.access_warehouse],
                d.created_at
              ]);
            }
          });
          if (pages >= _this.pageAll) {
            _this.loading.close();
            const sheetName = 'Sheet1';
            const workbook = {
              SheetNames: [sheetName],
              Sheets: {}
            };
            workbook.Sheets[sheetName] = utils.aoa_to_sheet(array);
            writeFile(workbook, 'parcels.xlsx');
          } else {
            _this.exportXlsOth(Number(pages) + 1, array);
          }
        });
      },
      exportXls() {
        const array = [];
        let roleUser = this.$hasRole('sonOwner');
        if (roleUser) {
          array.push([
            this.$t('awb.package.no'),
            this.$t('order.package.packageReference'),
            this.$t('awb.noa.awbno'),
            this.$t('awb.box.box'),
            this.$t('userAddress.user_addresses'),
            this.$t('order.package.customsClearanceResults'),
            this.$t('awb.package.access_warehouse'),
            this.$t('basics.createTime')
          ]);
        } else {
          array.push([
            this.$t('awb.package.no'),
            this.$t('order.package.packageReference'),
            this.$t('awb.noa.awbno'),
            this.$t('awb.box.box'),
            this.$t('basicsMenu.address.name'),
            this.$t('userAddress.user_addresses'),
            this.$t('awb.awb.col9'),
            this.$t('order.package.customsClearanceResults'),
            this.$t('awb.package.double_check'),
            this.$t('awb.package.notice_custom'),
            this.$t('awb.package.access_warehouse'),
            this.$t('basics.createTime')
          ]);
        }
        const checks = [
          '',
          this.$t('basics.release'),
          this.$t('basics.inspection'),
          this.$t('basics.unknown'),
          this.$t('order.package.failedToClearCustoms')
        ];
        const status = this.$t('awb.package.access_warehouse_status');
        const yesOrNo = ['', this.$t('basics.no'), this.$t('basics.yes')];
        var _this = this;
        _this.loading = this.$loading({ lock: true });
        lists({
          ...this.lastWhere,
          page: 1,
          num: this.pageNum
        }).then((res) => {
          _this.pageAll = Math.ceil(res.count / this.pageNum);
          res.list.forEach((d) => {
            if (roleUser) {
              array.push([
                d.track_no,
                d.package_reference,
                d.bol_no,
                d.box_no,
                d.user_address_code,
                checks[d.check],
                status[d.access_warehouse],
                d.created_at
              ]);
            } else {
              array.push([
                d.track_no,
                d.package_reference,
                d.bol_no,
                d.box_no,
                d.address,
                d.user_address_code,
                d.user_id,
                checks[d.check],
                yesOrNo[d.double_check],
                yesOrNo[d.notice_custom],
                status[d.access_warehouse],
                d.created_at
              ]);
            }
          });

          if (_this.pageAll == 1) {
            _this.loading.close();
            const sheetName = 'Sheet1';
            const workbook = {
              SheetNames: [sheetName],
              Sheets: {}
            };
            workbook.Sheets[sheetName] = utils.aoa_to_sheet(array);
            writeFile(workbook, 'parcels.xlsx');
          } else {
            _this.exportXlsOth(2, array);
          }
        });
      },
      /* 删除 */
      cancel(row) {
        const loading = this.$loading({ lock: true });
        cancel(row.id)
          .then(() => {
            loading.close();
            this.$message.success(this.$t('basics.success'));
            this.reload();
          })
          .catch((e) => {
            loading.close();
            this.$message.error(e);
          });
      }
    }
  };
</script>

<style lang="scss" scoped>
  /* 隐藏分页器中最后一个页码按钮 */
  :deep(.el-pager li.el-icon.more ~ li.number) {
    display: none !important;
  }
</style>
