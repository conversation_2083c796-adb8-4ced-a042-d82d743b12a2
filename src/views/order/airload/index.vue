<template>
  <div class="ele-body">
    <el-card shadow="never">
      <!-- 搜索表单 -->
      <outbound-fba-search @search="reload" />
      <!-- 数据表格 -->
      <ele-pro-table
        ref="table"
        :initLoad="false"
        :columns="columns"
        :datasource="datasource"
        :selection.sync="selection"
      >
        <!-- 表头工具栏 -->
        <template slot="toolbar">
          <router-link
            :to="
              '/outbound/outbound-fba/create?inbound_id=' +
              $route.query.inbound_id
            "
            style="margin-right: 10px"
          >
            <!-- <el-button
              size="small"
              type="primary"
              icon="el-icon-plus"
              class="ele-btn-icon"
            >
              创建出库单
            </el-button> -->
          </router-link>
        </template>
        <template v-slot:expand="{ row }">
          <el-form
            label-width="140px"
            label-position="left"
            size="mini"
            class="ele-form-detail"
          >
            <el-form-item
              v-for="item in row.air_nos"
              :key="item.id"
              :label="item.air_no"
              >{{ item.status == 1 ? $t('awb.air.no') : $t('awb.air.yes') }}
            </el-form-item>
          </el-form>
        </template>
      </ele-pro-table>
    </el-card>
  </div>
</template>

<script>
  import { cancel, lists } from '@/api/order/airload';
  import OutboundFbaSearch from './components/outbound-fba-search';

  export default {
    name: 'SystemUser',
    components: {
      OutboundFbaSearch
    },
    data() {
      return {
        // 表格列配置
        columns: [
          {
            width: 45,
            type: 'expand',
            columnKey: 'expand',
            align: 'center',
            slot: 'expand'
          },
          {
            prop: 'bol_no',
            label: this.$t('awb.noa.awbno'),
            showOverflowTooltip: true,
            minWidth: 110
          },
          {
            prop: 'created_at',
            label: this.$t('basics.createTime'),
            showOverflowTooltip: true,
            minWidth: 110
          }
        ],
        // 表格选中数据
        selection: [],
        // 当前编辑数据
        current: null,
        // 是否显示编辑弹窗
        showEdit: false,
        // 是否显示导入弹窗
        showImport: false,
        // 是否显修改密码
        userId: null,
        showChangePassword: false
      };
    },
    created() {
      //或者登陆不显示货主名字其他登录显示名称
      if (
        this.$store.state.user.info.role_type === '2' ||
        this.$store.state.user.info.role_type === '3'
      ) {
        const index = this.columns.findIndex(function (key) {
          return key.prop === 'username';
        });
        this.columns.splice(index, index + 1);
      }
    },
    activated() {
      this.reload();
    },
    methods: {
      /* 表格数据源 */
      datasource({ page, limit, where, order }) {
        return lists({
          ...where,
          ...order,
          page: page,
          num: limit
          // inbound_id: this.$route.query.inbound_id
        });
      },
      /* 刷新表格 */
      reload(where) {
        this.$refs.table.reload({ page: 1, where: where });
      },
      /* 打开编辑弹窗 */
      openEdit(row) {
        this.current = row;
        this.showEdit = true;
      },
      /* 打开导入弹窗 */
      openImport() {
        this.showImport = true;
      },
      /* 删除 */
      cancel(row) {
        const loading = this.$loading({ lock: true });
        cancel(row.id)
          .then(() => {
            loading.close();
            this.$message.success(this.$t('basics.success'));
            this.reload();
          })
          .catch((e) => {
            loading.close();
            this.$message.error(e);
          });
      }
    }
  };
</script>
<style>
  .ele-form-detail {
    font-size: 0;
  }

  .ele-form-detail label {
    width: 90px;
    color: #99a9bf;
  }

  .ele-form-detail .el-form-item {
    margin-right: 0;
    margin-bottom: 0;
    margin-left: 50px;
    /* width: 50%; */
  }
</style>
