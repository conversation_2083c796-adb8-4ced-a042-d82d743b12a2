<template>
  <div class="ele-body">
    <el-card shadow="never">
      <!-- 搜索表单 -->
      <outbound-fba-search @search="reload" />
      <!-- 数据表格 -->
      <ele-pro-table
        ref="table"
        :initLoad="true"
        :columns="columns"
        :datasource="listDate"
        :currentPage="currentPage"
      >
        <template slot="toolbar">
          <el-button @click="exportXls" icon="el-icon-download">{{
            $t('basics.export')
          }}</el-button>
        </template>
      </ele-pro-table>
    </el-card>
  </div>
</template>

<script>
  import { abnormalData } from '@/api/order/awb';
  import OutboundFbaSearch from './components/outbound-fba-search';

  import { utils, writeFile } from 'xlsx';

  export default {
    name: 'SystemUser',
    components: {
      OutboundFbaSearch
    },
    data() {
      return {
        // 表格列配置
        columns: [
          {
            columnKey: 'index',
            type: 'index',
            width: 65,
            showOverflowTooltip: true,
            fixed: 'left'
          },
          {
            prop: 'bol_no',
            label: this.$t('awb.noa.awbno'),
            showOverflowTooltip: true,
            minWidth: 120
          },
          {
            prop: 'lgg',
            label: this.$t('awb.awb.port'),
            showOverflowTooltip: true,
            minWidth: 110
          },
          {
            prop: 'time3',
            label: this.$t('awb.awb.col21'),
            showOverflowTooltip: true,
            minWidth: 110
          },
          // {
          //   prop: 'time5',
          //   label: this.$t('awb.noa.time'),
          //   showOverflowTooltip: true,
          //   minWidth: 110
          // },
          // {
          //   prop: 'cmrInTime1',
          //   label: this.$t('car.timeWords.in1'),
          //   showOverflowTooltip: true,
          //   minWidth: 110
          // },
          // {
          //   prop: 'cmrInTime2',
          //   label: this.$t('car.timeWords.in2'),
          //   showOverflowTooltip: true,
          //   minWidth: 110
          // },
          // {
          //   prop: 'cmrInTime3',
          //   label: this.$t('car.timeWords.in3'),
          //   showOverflowTooltip: true,
          //   minWidth: 110
          // },
          // {
          //   prop: 'cmrOutTime2',
          //   label: this.$t('car.timeWords.out2'),
          //   showOverflowTooltip: true,
          //   minWidth: 110
          // },
          {
            prop: 'disTime1',
            label: this.$t('awb.abnormal.disTime1'),
            showOverflowTooltip: true,
            minWidth: 80
          },
          {
            prop: 'disTime2',
            label: this.$t('awb.abnormal.disTime2'),
            showOverflowTooltip: true,
            minWidth: 80
          },
          {
            prop: 'disTime3',
            label: this.$t('awb.abnormal.disTime3'),
            showOverflowTooltip: true,
            minWidth: 80
          },
          {
            prop: 'disTime4',
            label: this.$t('awb.abnormal.disTime4'),
            showOverflowTooltip: true,
            minWidth: 80
          },
          {
            prop: 'disInBox',
            label: this.$t('awb.abnormal.disInBox'),
            showOverflowTooltip: true,
            minWidth: 80
          },
          {
            prop: 'disOutBox',
            label: this.$t('awb.abnormal.disOutBox'),
            showOverflowTooltip: true,
            minWidth: 110
          }
        ],
        // 是否显示导入弹窗
        showImport: false,
        showBatchImport: false,
        // 是否显修改密码
        userId: null,
        showChangePassword: false,
        listDate: [],
        oldWhwere: [],
        currentPage: 1
      };
    },
    created() {
      this.oldWhwere = [];
      this.reload(this.oldWhwere);
    },
    methods: {
      /* 刷新表格 */
      reload(where) {
        const loading = this.$loading({ lock: true });
        this.oldWhwere = where;
        abnormalData(where).then((res) => {
          this.$refs.table.reload({ page: 1 });
          this.listDate = res;
          loading.close();
        });
      },
      exportXls() {
        const array = [];
        array.push([
          this.$t('awb.noa.awbno'),
          this.$t('awb.awb.port'),
          this.$t('awb.awb.col21'),
          this.$t('awb.noa.time'),
          this.$t('car.timeWords.in1'),
          this.$t('car.timeWords.in2'),
          this.$t('car.timeWords.in3'),
          this.$t('car.timeWords.out2'),
          this.$t('awb.abnormal.disTime1'),
          this.$t('awb.abnormal.disTime2'),
          this.$t('awb.abnormal.disTime3'),
          this.$t('awb.abnormal.disTime4'),
          this.$t('awb.abnormal.disInBox'),
          this.$t('awb.abnormal.disOutBox')
        ]);
        this.listDate.forEach((d) => {
          array.push([
            d.bol_no,
            d.lgg,
            d.time3,
            d.time5,
            d.cmrInTime1,
            d.cmrInTime2,
            d.cmrInTime3,
            d.cmrOutTime2,
            d.disTime1,
            d.disTime2,
            d.disTime3,
            d.disTime4,
            d.disInBox,
            d.disOutBox
          ]);
        });
        const sheetName = 'Sheet1';
        const workbook = {
          SheetNames: [sheetName],
          Sheets: {}
        };
        workbook.Sheets[sheetName] = utils.aoa_to_sheet(array);
        writeFile(workbook, 'awb.xlsx');
      },
      /* 打开导入弹窗 */
      openImport() {
        this.showImport = true;
      }
    }
  };
</script>
