<template>
  <div>
    <div class="ele-page-header">
      <div class="ele-page-title">{{ $t('route.order.packageXls._name') }}</div>
      <!-- <div class="ele-page-desc">
        表单页用于向用户收集或验证信息, 基础表单常见于数据项较少的表单场景。
      </div> -->
    </div>
    <div class="ele-body">
      <el-card shadow="never">
        <el-form label-width="90px" style="max-width: 700px; margin: 10px auto">
          <el-upload
            drag
            action=""
            class="ele-block"
            v-loading="loading"
            accept=".xls,.xlsx"
            :show-file-list="false"
            :before-upload="doUpload"
          >
            <i class="el-icon-upload"></i>
            <div class="el-upload__text"
              >{{ $t('awb.awb.tips.t1')
              }}<em>{{ $t('awb.awb.tips.t2') }}</em></div
            >
            <div class="el-upload__tip ele-text-center" slot="tip">
              <span>{{ $t('awb.awb.tips.t3') }} </span>
              <el-link
                download
                :href="url"
                type="primary"
                :underline="false"
                style="vertical-align: baseline"
              >
                {{ $t('awb.awb.tips.t4') }}
              </el-link>
            </div>
          </el-upload>
        </el-form>
      </el-card>
    </div>
  </div>
</template>

<script>
  import { uploadFile } from '@/api/system/file';
  import { exportPackage } from '@/api/order/package';

  export default {
    name: 'UploadFile',
    props: {
      // 是否打开弹窗
      visible: Boolean
    },
    data() {
      return {
        // 导入请求状态
        loading: false,
        // 导入模板下载地址
        url: 'https://eur-test-read-open.oss-eu-central-1.aliyuncs.com/wms/wmsbe/awb_sample.xlsx',
        xls_path: '',
        loadingTop: false
      };
    },
    methods: {
      /* 上传 */
      doUpload(file) {
        this.loading = true;
        console.log(file.name);
        uploadFile(file)
          .then((res) => {
            this.loading = false;
            this.xls_path = res.files;
            this.exportPackage({ xls_path: this.xls_path });
          })
          .catch((e) => {
            this.loading = false;
            this.$message.error(e);
          });
        return false;
      },
      doDownload(url, name = '') {
        let a = document.createElement('a'), //创建a标签
          e = document.createEvent('MouseEvents'); //创建鼠标事件对象
        e.initEvent('click', false, false); //初始化事件对象
        a.href = url; //设置下载地址
        a.target = '_blank';
        a.download = name; //设置下载文件名
        a.dispatchEvent(e); //给指定的元素，执行事件click事件
      },
      exportPackage(params) {
        // this.loading = true;
        this.loadingTop = this.$loading({ lock: true });
        exportPackage(params)
          .then((res) => {
            this.loadingTop.close();
            this.$message.success(this.$t('basics.success'));
            this.doDownload(res.result.objectUrl, 'package.csv');
          })
          .catch((e) => {
            this.loadingTop.close();
            this.$message.error(e);
          });
      },
      /* 更新visible */
      updateVisible(value) {
        this.$emit('update:visible', value);
      }
    }
  };
</script>
<style scoped>
  .ele-block ::v-deep .el-upload,
  .ele-block ::v-deep .el-upload-dragger {
    width: 100%;
  }
</style>
