<template>
  <el-card shadow="never" body-style="padding: 22px 22px 7px 22px;">
    <!-- 搜索表单 -->
    <el-form
      label-width="140px"
      class="ele-form-search"
      @keyup.enter.native="search"
      @submit.native.prevent
    >
      <el-row :gutter="15">
        <el-col :lg="8" :md="12">
          <el-form-item
            :label="this.$t('awb.noa.address') + ':'"
            prop="address_code"
          >
            <address-select :searchType="2" v-model="where.address_code" />
          </el-form-item>
        </el-col>
        <el-col :lg="8" :md="12">
          <el-form-item :label="this.$t('awb.noa.awbno') + ':'" prop="bol_no">
            <!-- <awb-select-no
              v-model="where.bol_no"
            /> -->
            <el-input v-model="where.bol_no" />
          </el-form-item>
        </el-col>
        <el-col :lg="8" :md="12">
          <el-form-item
            :label="this.$t('awb.noa.loadmode') + ':'"
            prop="load_type"
          >
            <mode-select v-model="where.load_type" />
          </el-form-item>
        </el-col>
        <el-col :lg="8" :md="12" v-permission="'address:nouser'">
          <el-form-item
            :label="this.$t('awb.noa.sendMail') + ':'"
            prop="email_notice"
          >
            <el-select
              <el-select
              v-model="where.email_notice"
              clearable
              class="ele-fluid"
            >
              <el-option :label="this.$t('basics.no')" :value="1" />
              <el-option :label="this.$t('basics.yes')" :value="2" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :lg="8" :md="12">
          <el-form-item :label="`${$t('awb.noa.time')}:`" prop="noa_time">
            <el-date-picker
              v-model="where.noa_time"
              type="datetimerange"
              value-format="yyyy-MM-dd HH:mm:ss"
              :range-separator="this.$t('basics.to')"
              class="ele-fluid"
              :start-placeholder="this.$t('basics.beginTime')"
              :end-placeholder="this.$t('basics.endTime')"
            >
            </el-date-picker>
          </el-form-item>
        </el-col>

        <el-col :lg="8" :md="12">
          <el-form-item
            :label="this.$t('awb.noa.is_determine') + ':'"
            prop="email_notice"
          >
            <el-select v-model="where.is_determine" clearable class="ele-fluid">
              <el-option :label="this.$t('basics.no')" :value="'1'" />
              <el-option :label="this.$t('basics.yes')" :value="'2'" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :lg="8" :md="12">
          <div class="ele-form-actions">
            <el-button
              type="primary"
              icon="el-icon-search"
              class="ele-btn-icon"
              @click="search"
            >
              {{ $t('basics.query') }}
            </el-button>
            <el-button @click="reset">{{ $t('basics.reset') }}</el-button>
          </div>
        </el-col>
      </el-row>
    </el-form>
  </el-card>
</template>

<script>
  import AddressSelect from '@/components/AddressSelect';
  import AwbSelectNo from '@/components/AwbSelectNo';
  import ModeSelect from './mode-select';
  // 默认表单数据

  const DEFAULT_WHERE = {
    email_notice: null,
    bol_no: '',
    address_code: '',
    load_type: '',
    noa_time: ''
  };

  export default {
    name: 'outbound-fba-search',
    components: { AddressSelect, ModeSelect, AwbSelectNo },
    data() {
      return {
        // 表单数据
        where: { ...DEFAULT_WHERE },
        // 搜索表单是否展开
        searchExpand: false,
        goodsCategoryList: [],
        category_id: ''
      };
    },
    methods: {
      /* 搜索 */
      search() {
        //解决搜索 也搜索下级的数据
        this.$emit('search', this.where);
      },
      /* 重置搜索 */
      reset() {
        this.where = { ...DEFAULT_WHERE };
        this.search();
      },
      /* 搜索展开/收起 */
      toggleExpand() {
        this.searchExpand = !this.searchExpand;
        this.$emit('expand-change', this.searchExpand);
      },
      /* 搜索展开/收起 */
      goodsCategoryFilterListClick(e) {
        this.goodsCategoryList = e.data;
      }
    }
  };
</script>
