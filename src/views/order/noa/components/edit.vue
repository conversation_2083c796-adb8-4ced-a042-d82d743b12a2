<!-- 编辑弹窗 -->
<template>
  <ele-modal
    width="720px"
    :visible="visible"
    :close-on-click-modal="false"
    custom-class="ele-dialog-form"
    :title="isUpdate ? this.$t('basics.edit') : this.$t('basics.create')"
    @update:visible="updateVisible"
  >
    <el-form ref="form" :model="form" :rules="rules" label-width="140px">
      <el-row :gutter="15">
        <el-col :sm="12">
          <el-form-item :label="this.$t('awb.noa.time') + ':'" prop="noa_time">
            <el-date-picker
              v-model="form.noa_time"
              type="datetime"
              value-format="yyyy-MM-dd HH:mm:ss"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :sm="12" v-if="!isUpdate">
          <el-form-item :label="this.$t('awb.noa.awbno') + ':'" prop="bol_id">
            <awb-select v-model="form.bol_id" />
          </el-form-item>
        </el-col>
        <el-col :sm="12" v-else>
          <el-form-item :label="this.$t('awb.noa.awbno') + ':'" prop="bol_no">
            <el-input v-model="form.bol_no" :disabled="true" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="15">
        <el-col :sm="12">
          <el-form-item
            :label="this.$t('awb.noa.boxCount') + ':'"
            prop="box_count"
          >
            <el-input v-model="form.box_count" />
          </el-form-item>
        </el-col>
        <el-col :sm="12">
          <el-form-item :label="this.$t('awb.noa.weight') + ':'" prop="weight">
            <el-input v-model="form.weight" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="15">
        <el-col :sm="12">
          <el-form-item
            :label="this.$t('awb.noa.loadmode') + ':'"
            prop="load_type"
          >
            <mode-select v-model="form.load_type" />
          </el-form-item>
        </el-col>
        <el-col :sm="12">
          <el-form-item :label="this.$t('awb.noa.pmc') + ':'" prop="pmc">
            <el-input :maxlength="40" v-model="form.pmc" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="15">
        <el-col :sm="12">
          <el-form-item
            :label="this.$t('awb.noa.address') + ':'"
            prop="address_code"
          >
            <address-select :searchType="2" v-model="form.address_code" />
          </el-form-item>
        </el-col>
        <el-col :sm="12">
          <el-form-item
            :label="this.$t('awb.noa.sendMail') + ':'"
            prop="email_notice"
          >
            <el-select v-model="form.email_notice" clearable class="ele-fluid">
              <el-option :label="this.$t('basics.no')" :value="1" />
              <el-option :label="this.$t('basics.yes')" :value="2" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div slot="footer">
      <el-button @click="updateVisible(false)">
        {{ $t('basics.cancel') }}
      </el-button>
      <el-button type="primary" :loading="loading" @click="save">
        {{ $t('basics.save') }}
      </el-button>
    </div>
  </ele-modal>
</template>

<script>
  import { edit, create } from '@/api/order/noa';
  import AwbSelect from '@/components/AwbSelect';
  import AddressSelect from '@/components/AddressSelect';
  import ModeSelect from './mode-select';

  const DEFAULT_FORM = {
    id: null,
    load_type: '',
    bol_id: null,
    bol_no: '',
    noa_time: '',
    box_count: null,
    address_code: '',
    email_notice: null,
    pmc: '',
    weight: ''
  };

  export default {
    name: 'UserEdit',
    components: { AddressSelect, AwbSelect, ModeSelect },
    props: {
      // 弹窗是否打开
      visible: Boolean,
      // 修改回显的数据
      data: Object
    },
    data() {
      return {
        // 表单数据
        form: { ...DEFAULT_FORM },
        // 表单验证规则
        rules: {
          code: [
            {
              required: true,
              message: this.$t('basics.pleaseInput'),
              trigger: 'blur'
            }
          ],
          addr_name: [
            {
              required: true,
              message: this.$t('basics.pleaseInput'),
              trigger: 'blur'
            }
          ],
          country: [
            {
              required: true,
              message: this.$t('basics.pleaseChoose'),
              trigger: ['blur', 'change']
            }
          ],
          type: [
            {
              required: true,
              message: this.$t('basics.pleaseChoose'),
              trigger: ['blur', 'change']
            }
          ],
          address: [
            {
              required: true,
              message: this.$t('basics.pleaseInput'),
              trigger: 'blur'
            }
          ]
        },
        // 提交状态
        loading: false,
        // 是否是修改
        isUpdate: false
      };
    },
    created() {
      // if (!hasPermission('address:nouser')) {
      //   this.form.type = 1
      // }
    },
    methods: {
      /* 保存编辑 */
      save() {
        this.$refs['form'].validate((valid) => {
          if (!valid) {
            return false;
          }
          this.loading = true;
          const data = {
            ...this.form
          };
          const apiname = this.isUpdate ? edit : create;
          apiname(data)
            .then((msg) => {
              this.loading = false;
              this.$message.success(this.$t('basics.success'));
              this.updateVisible(false);
              this.$emit('done');
            })
            .catch((e) => {
              this.loading = false;
              this.$message.error(e);
            });
        });
      },
      /* 更新visible */
      updateVisible(value) {
        this.$emit('update:visible', value);
      },
      /* 角色选择回调*/
      roleSelectCallback(value) {
        this.form.role_type = value.type;
      }
    },
    watch: {
      async visible(visible) {
        console.log(visible);
        if (visible) {
          let data = this.data;
          if (this.data) {
            this.$util.assignObject(this.form, {
              ...data
            });
            this.isUpdate = true;
          } else {
            this.isUpdate = false;
          }
        } else {
          this.$refs['form'].clearValidate();
          this.form = { ...DEFAULT_FORM };
        }
      }
    }
  };
</script>
