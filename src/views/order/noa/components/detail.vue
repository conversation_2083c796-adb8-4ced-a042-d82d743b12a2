<!-- 编辑弹窗 -->
<template>
  <ele-modal
    width="720px"
    :visible="visible"
    :close-on-click-modal="false"
    custom-class="ele-dialog-form"
    :title="$t('basics.detail')"
    @update:visible="updateVisible"
  >
    <el-form ref="form" :model="form"  label-width="140px">
      <el-row :gutter="15">
        <el-col :sm="12">
          <el-form-item :label="this.$t('awb.noa.time') + ':'" prop="noa_time">
            <el-date-picker
              v-model="form.noa_time"
              type="datetime"
              :disabled="isUpdate"
              value-format="yyyy-MM-dd HH:mm:ss"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :sm="12">
          <el-form-item :label="this.$t('awb.noa.awbno') + ':'" prop="bol_no">
            <el-input v-model="form.bol_no" :disabled="isUpdate" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="15">
        <el-col :sm="12">
          <el-form-item
            :label="this.$t('awb.noa.boxCount') + ':'"
            prop="box_count"
          >
            <el-input v-model="form.box_count" :disabled="isUpdate" />
          </el-form-item>
        </el-col>
        <el-col :sm="12">
          <el-form-item :label="this.$t('awb.noa.weight') + ':'" prop="weight">
            <el-input v-model="form.weight" :disabled="isUpdate" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="15">
        <el-col :sm="12">
          <el-form-item
            :label="this.$t('awb.noa.loadmode') + ':'"
            prop="load_type"
          >
            <el-input v-model="form.load_type" :disabled="isUpdate" />
          </el-form-item>
        </el-col>
        <el-col :sm="12">
          <el-form-item :label="this.$t('awb.noa.pmc') + ':'" prop="pmc">
            <el-input :maxlength="40" v-model="form.pmc" :disabled="isUpdate" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="15">
        <el-col :sm="12">
          <el-form-item
            :label="this.$t('awb.noa.address') + ':'"
            prop="address_code"
          >
            <el-input v-model="form.address_code" :disabled="isUpdate" />
          </el-form-item>
        </el-col>
        <el-col :sm="12">
          <el-form-item
            :label="this.$t('awb.noa.sendMail') + ':'"
            prop="email_notice"
          >
            <el-select
              v-model="form.email_notice"
              :disabled="isUpdate"
              clearable
              class="ele-fluid"
            >
              <el-option :label="this.$t('basics.no')" :value="1" />
              <el-option :label="this.$t('basics.yes')" :value="2" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div slot="footer">
      <el-button @click="updateVisible(false)">
        {{ $t('basics.cancel') }}
      </el-button>
    </div>
  </ele-modal>
</template>

<script>
  const DEFAULT_FORM = {
    id: null,
    load_type: '',
    bol_id: null,
    bol_no: '',
    noa_time: '',
    box_count: null,
    address_code: '',
    email_notice: null,
    pmc: '',
    weight: ''
  };

  export default {
    name: 'UserEdit',
    props: {
      // 弹窗是否打开
      visible: Boolean,
      // 修改回显的数据
      data: Object
    },
    data() {
      return {
        // 表单数据
        form: { ...DEFAULT_FORM },
        // 提交状态
        loading: false,
        // 是否是修改
        isUpdate: false
      };
    },
    methods: {
      /* 更新visible */
      updateVisible(value) {
        this.$emit('update:visible', value);
      },
      /* 角色选择回调*/
      roleSelectCallback(value) {
        this.form.role_type = value.type;
      }
    },
    watch: {
      async visible(visible) {
        if (visible) {
          let data = this.data;
          if (this.data) {
            this.$util.assignObject(this.form, {
              ...data
            });
            this.isUpdate = true;
          } else {
            this.isUpdate = false;
          }
        } else {
          this.$refs['form'].clearValidate();
          this.form = { ...DEFAULT_FORM };
        }
      }
    }
  };
</script>
