<template>
  <div class="ele-body">
    <el-card shadow="never">
      <!-- 搜索表单 -->
      <outbound-fba-search @search="reload" />
      <!-- 数据表格 -->
      <ele-pro-table
        ref="table"
        :initLoad="false"
        :columns="columns"
        :datasource="datasource"
        :selection.sync="selection"
      >
        <!-- 表头工具栏 -->
        <template slot="toolbar">
          <el-button
            v-permission="'noa:create'"
            size="small"
            type="primary"
            icon="el-icon-plus"
            class="ele-btn-icon"
            @click="openEdit()"
          >
            {{ $t('basics.create') }}
          </el-button>
          <el-button
            v-permission="'noa:create'"
            size="small"
            icon="el-icon-upload2"
            class="ele-btn-icon"
            @click="openBatchImport()"
          >
            {{ $t('basics.import') }}
          </el-button>
        </template>
        <!-- 操作列 -->
        <template slot="action" slot-scope="{ row }">
          <el-link
            @click="determine(row)"
            v-if="row.is_determine != 2"
            v-permission="'noa:determine'"
            type="primary"
            icon="el-icon-edit"
            :underline="false"
          >
            {{ $t('awb.noa.determine') }}
          </el-link>
          <el-link
            @click="openEdit(row)"
            v-if="row.is_determine != 2"
            v-permission="'noa:edit'"
            type="primary"
            icon="el-icon-edit"
            :underline="false"
          >
            {{ $t('basics.edit') }}
          </el-link>
          <el-link @click="openDetail(row)" type="primary" :underline="false">
            {{ $t('basics.detail') }}
          </el-link>
          <!-- <el-link
            v-permission="'address:finance'"
            @click="openFinanceEdit(row)"
            type="primary"
            :underline="false">
            {{$t('awb.noa.financeSetting')}}
          </el-link> -->
          <el-popconfirm
            v-if="row.is_determine != 2"
            class="ele-action"
            :title="$t('basics.confirmDel')"
            @confirm="remove(row)"
            icon="el-icon-delete"
          >
            <el-link
              type="danger"
              v-permission="'noa:delete'"
              slot="reference"
              icon="el-icon-delete"
              :underline="false"
            >
              {{ $t('basics.delete') }}
            </el-link>
          </el-popconfirm>
        </template>
      </ele-pro-table>
    </el-card>
    <address-edit :visible.sync="showEdit" :data="current" @done="reload" />
    <batch-import :visible.sync="showBatchImport" @done="reload" />
    <finance-edit
      :visible.sync="showFinanceEdit"
      :data="current"
      @done="reload"
    />
    <address-detail :visible.sync="showDetail" :data="current" @done="reload" />
  </div>
</template>

<script>
  import { del, determine, lists } from '@/api/order/noa';
  import AddressEdit from './components/edit';
  import AddressDetail from './components/detail';
  import OutboundFbaSearch from './components/outbound-fba-search';
  import FinanceEdit from './components/finance-edit';
  import BatchImport from '@/views/order/noa/components/batch-import';

  export default {
    name: 'SystemUser',
    components: {
      BatchImport,
      OutboundFbaSearch,
      AddressEdit,
      AddressDetail,
      FinanceEdit
    },
    data() {
      return {
        // 表格列配置
        columns: [
          {
            columnKey: 'index',
            type: 'index',
            width: 45,
            showOverflowTooltip: true,
            fixed: 'left'
          },
          {
            prop: 'bol_no',
            label: this.$t('awb.noa.awbno'),
            showOverflowTooltip: true,
            minWidth: 180
          },
          {
            prop: 'address',
            label: this.$t('awb.noa.address'),
            showOverflowTooltip: true,
            minWidth: 180,
            align: 'center'
          },
          {
            prop: 'box_count',
            label: this.$t('awb.noa.boxCount'),
            showOverflowTooltip: true,
            minWidth: 110
          },
          {
            prop: 'weight',
            label: this.$t('awb.noa.weight') + '(kg)',
            showOverflowTooltip: true,
            minWidth: 110
          },
          {
            prop: 'load_type',
            label: this.$t('awb.noa.loadmode'),
            showOverflowTooltip: true,
            minWidth: 110
          },
          {
            prop: 'pmc',
            label: this.$t('awb.noa.pmc'),
            showOverflowTooltip: true,
            minWidth: 110
          },
          {
            prop: 'noa_time',
            label: this.$t('awb.noa.time'),
            showOverflowTooltip: true,
            minWidth: 110
          },
          {
            prop: 'is_determine',
            label: this.$t('awb.noa.is_determine'),
            showOverflowTooltip: true,
            minWidth: 110,
            formatter: (row, column, cellValue) => {
              return [this.$t('basics.no'), this.$t('basics.yes')][
                cellValue - 1
              ];
            }
          },
          {
            prop: 'email_notice',
            label: this.$t('awb.noa.sendMail'),
            showOverflowTooltip: true,
            minWidth: 110,
            formatter: (row, column, cellValue) => {
              return [this.$t('basics.no'), this.$t('basics.yes')][
                cellValue - 1
              ];
            }
          },
          {
            prop: 'created_at',
            label: this.$t('basics.createTime'),
            showOverflowTooltip: true,
            minWidth: 110
          },
          {
            columnKey: 'action',
            label: this.$t('basics.action'),
            width: 220,
            align: 'center',
            resizable: false,
            slot: 'action',
            hideInSetting: true,
            fixed: 'right'
          }
        ],
        // 表格选中数据
        selection: [],
        // 当前编辑数据
        current: null,
        // 是否显示编辑弹窗
        showEdit: false,
        showDetail: false,
        showFinanceEdit: false,
        // 是否显示导入弹窗
        showImport: false,
        showBatchImport: false,
        // 是否显修改密码
        userId: null,
        showChangePassword: false
      };
    },
    created() {
      //或者登陆不显示货主名字其他登录显示名称
      // if (
      //   this.$store.state.user.info.role_type === '2' ||
      //   this.$store.state.user.info.role_type === '3'
      // ) {
      //   const index = this.columns.findIndex(function (key) {
      //     return key.prop === 'username';
      //   });
      //   this.columns.splice(index, index + 1);
      // }
    },
    activated() {
      this.reload();
    },
    methods: {
      /* 表格数据源 */
      datasource({ page, limit, where, order }) {
        return lists({
          ...where,
          ...order,
          page: page,
          num: limit
          // inbound_id: this.$route.query.inbound_id
        });
      },
      /* 刷新表格 */
      reload(where) {
        this.$refs.table.reload({ page: 1, where: where });
      },
      /* 打开编辑弹窗 */
      openEdit(row) {
        this.current = row;
        this.showEdit = true;
      },
      /* 打开编辑弹窗 */
      openBatchImport() {
        this.showBatchImport = true;
      },
      openFinanceEdit(row) {
        this.current = row;
        this.showFinanceEdit = true;
      },
      openDetail(row) {
        this.current = row;
        this.showDetail = true;
      },
      /* 打开导入弹窗 */
      openImport() {
        this.showImport = true;
      },
      /* 删除 */
      remove(row) {
        const loading = this.$loading({ lock: true });
        del(row)
          .then(() => {
            loading.close();
            this.$message.success(this.$t('basics.success'));
            this.reload();
          })
          .catch((e) => {
            loading.close();
            this.$message.error(e);
          });
      },
      /* 确定操作 */
      determine(row) {
        const loading = this.$loading({ lock: true });
        determine({ id: row.id })
          .then(() => {
            loading.close();
            this.$message.success(this.$t('basics.success'));
            this.reload();
          })
          .catch((e) => {
            loading.close();
            this.$message.error(e);
          });
      }
    }
  };
</script>
