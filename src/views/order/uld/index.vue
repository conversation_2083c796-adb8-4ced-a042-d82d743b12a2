<template>
  <div class="ele-body">
    <el-card shadow="never">
      <!-- 搜索表单 -->
      <outbound-fba-search @search="reload" />
      <!-- 数据表格 -->
      <ele-pro-table
        ref="table"
        :initLoad="false"
        :columns="columns"
        :datasource="datasource"
        :selection.sync="selection"
      >
        <!-- 表头工具栏 -->
        <template slot="toolbar">
          <el-button
            size="small"
            icon="el-icon-download"
            class="ele-btn-icon"
            @click="exportXls"
            v-permission="'uld:export'"
          >
            {{ $t('basics.export') }}
          </el-button>
        </template>

        <!-- 操作列 -->
        <template slot="action" slot-scope="{ row }">
          <el-link
            @click="setComments(row)"
            v-permission="'noa:determine'"
            type="primary"
            icon="el-icon-edit"
            :underline="false"
          >
            {{ $t('order.uid.setComments') }}
          </el-link>
        </template>
      </ele-pro-table>
    </el-card>
    <set-remarks :visible.sync="showEdit" :data="current" @done="reload" />
  </div>
</template>

<script>
  import { expLists, lists } from '@/api/order/uld';
  import OutboundFbaSearch from './components/outbound-fba-search';
  import { utils, writeFile } from 'xlsx';
  import SetRemarks from './components/set-remarks';
  export default {
    name: 'SystemUser',
    components: {
      SetRemarks,
      OutboundFbaSearch
    },
    data() {
      return {
        // 表格列配置
        columns: [
          {
            columnKey: 'index',
            type: 'index',
            width: 45,
            showOverflowTooltip: true,
            fixed: 'left'
          },
          {
            prop: 'air_no',
            label: this.$t('car.airno'),
            showOverflowTooltip: true,
            minWidth: 110
          },
          {
            prop: 'bol_no',
            label: this.$t('awb.noa.awbno'),
            showOverflowTooltip: true,
            minWidth: 110
          },
          {
            prop: 'address',
            label: this.$t('car.addressIn'),
            showOverflowTooltip: true,
            minWidth: 110
          },
          {
            prop: 'return_address',
            label: this.$t('car.addressReturn'),
            showOverflowTooltip: true,
            minWidth: 110
          },
          {
            prop: 'returnCmr',
            label: 'CMR',
            showOverflowTooltip: true,
            minWidth: 110
          },
          {
            prop: 'status',
            label: this.$t('basics.status'),
            showOverflowTooltip: true,
            minWidth: 110,
            formatter: (row, column, cellValue) => {
              // return cellValue;
              return { 1: this.$t(`awb.air.no`), 2: this.$t(`awb.air.yes`) }[
                cellValue
              ];
            }
          },
          {
            prop: 'created_at',
            label: this.$t('awb.box.stock_in_time'),
            showOverflowTooltip: true,
            minWidth: 110
          },
          {
            prop: 'updated_at',
            label: this.$t('awb.box.stock_out_time'),
            showOverflowTooltip: true,
            minWidth: 110
          },
          {
            prop: 'remarks',
            label: this.$t('order.uid.comments'),
            showOverflowTooltip: true,
            minWidth: 110
          },
          {
            columnKey: 'action',
            label: this.$t('basics.action'),
            width: 140,
            align: 'center',
            resizable: false,
            slot: 'action',
            hideInSetting: true,
            fixed: 'right'
          }
        ],
        // 表格选中数据
        selection: [],
        // 当前编辑数据
        current: null,
        // 是否显示编辑弹窗
        showEdit: false,
        // 是否显示导入弹窗
        showImport: false,
        // 是否显修改密码
        userId: null,
        showChangePassword: false,
        lastWhere: {}
      };
    },
    created() {
      //或者登陆不显示货主名字其他登录显示名称
      if (
        this.$store.state.user.info.role_type === '2' ||
        this.$store.state.user.info.role_type === '3'
      ) {
        const index = this.columns.findIndex(function (key) {
          return key.prop === 'username';
        });
        this.columns.splice(index, index + 1);
      }
    },
    activated() {
      this.reload();
    },
    methods: {
      /* 表格数据源 */
      datasource({ page, limit, where, order }) {
        return lists({
          ...where,
          ...order,
          page: page,
          num: limit
          // inbound_id: this.$route.query.inbound_id
        });
      },
      /* 刷新表格 */
      reload(where) {
        this.lastWhere = where;
        this.$refs.table.reload({ page: 1, where: where });
      },
      /* 设置备注 */
      setComments({ remarks, id, return_address }) {
        this.current = {
          return_address: return_address,
          remarks: remarks,
          id: id
        };
        this.showEdit = true;
        // this.$prompt(this.$t('order.uid.setComments'), {
        //   inputValue: remarks
        // })
        //   .then(({ value }) => {
        //     modifyRemarks({
        //       id: id,
        //       remarks: value
        //     })
        //       .then(() => {
        //         this.reload();
        //         this.$message.success(this.$t('basics.success'));
        //       })
        //       .catch((res) => {
        //         this.$message.error(res);
        //       });
        //   })
        //   .catch(() => {});
      },
      exportXls() {
        const array = [];
        array.push([
          this.$t('car.airno'),
          this.$t('awb.noa.awbno'),
          this.$t('car.addressIn'),
          this.$t('car.addressReturn'),
          'CMR',
          this.$t('basics.status'),
          this.$t('awb.box.stock_in_time'),
          this.$t('awb.box.stock_out_time'),
          this.$t('order.uid.comments')
        ]);
        const status = ['', this.$t('awb.air.no'), this.$t('awb.air.yes')];

        expLists({
          ...this.lastWhere
        }).then((res) => {
          res.forEach((d) => {
            array.push([
              d.air_no,
              d.bol_no,
              d.address,
              d.return_address,
              d.returnCmr,
              status[d.status],
              d.created_at,
              d.updated_at,
              d.remarks
            ]);
          });
          const sheetName = 'Sheet1';
          const workbook = {
            SheetNames: [sheetName],
            Sheets: {}
          };
          workbook.Sheets[sheetName] = utils.aoa_to_sheet(array);
          writeFile(workbook, 'ULD.xlsx');
        });
      }
    }
  };
</script>
<style>
  .ele-form-detail {
    font-size: 0;
  }

  .ele-form-detail label {
    width: 90px;
    color: #99a9bf;
  }

  .ele-form-detail .el-form-item {
    margin-right: 0;
    margin-bottom: 0;
    margin-left: 50px;
    /* width: 50%; */
  }
</style>
