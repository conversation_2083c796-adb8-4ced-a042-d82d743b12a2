<template>
  <el-card shadow="never" body-style="padding: 22px 22px 7px 22px;">
    <!-- 搜索表单 -->
    <el-form
      label-width="120px"
      class="ele-form-search"
      @keyup.enter.native="search"
      @submit.native.prevent
    >
      <el-row :gutter="15">
        <el-col :lg="8" :md="12">
          <el-form-item :label="$t('car.airno') + ':'" prop="air_no">
            <el-input v-model="where.air_no" />
          </el-form-item>
        </el-col>
        <el-col :lg="8" :md="12">
          <el-form-item :label="$t('awb.noa.awbno') + ':'" prop="bol_no">
            <el-input v-model="where.bol_no" />
          </el-form-item>
        </el-col>
        <el-col :lg="8" :md="12">
          <el-form-item :label="$t('basics.status') + ':'" prop="status">
            <el-select
              v-model="where.status"
              :placeholder="$t('basics.pleaseChoose')"
              clearable
              class="ele-fluid"
            >
              <el-option :label="$t('awb.air.no')" :value="1" />
              <el-option :label="$t('awb.air.yes')" :value="2" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :lg="8" :md="12">
          <el-form-item
            :label="`${$t('awb.box.stock_in_time')}:`"
            prop="created_at"
          >
            <el-date-picker
              v-model="where.created_at"
              type="datetimerange"
              value-format="yyyy-MM-dd HH:mm:ss"
              class="ele-fluid"
              :start-placeholder="$t('basics.beginTime')"
              :end-placeholder="$t('basics.endTime')"
            >
            </el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :lg="8" :md="12">
          <el-form-item
            :label="`${$t('awb.box.stock_out_time')}:`"
            prop="created_at"
          >
            <el-date-picker
              v-model="where.updated_at"
              type="datetimerange"
              value-format="yyyy-MM-dd HH:mm:ss"
              class="ele-fluid"
              :start-placeholder="$t('basics.beginTime')"
              :end-placeholder="$t('basics.endTime')"
            >
            </el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :lg="8" :md="12">
          <el-form-item
            :label="$t('car.addressReturn') + ':'"
            prop="return_address"
          >
            <address-select v-model="where.return_address" :searchType="2" />
          </el-form-item>
        </el-col>
        <el-col :lg="8" :md="12">
          <div class="ele-form-actions">
            <el-button
              type="primary"
              icon="el-icon-search"
              class="ele-btn-icon"
              @click="search"
            >
              {{ $t('basics.query') }}
            </el-button>
            <el-button @click="reset">{{ $t('basics.reset') }}</el-button>
          </div>
        </el-col>
      </el-row>
    </el-form>
  </el-card>
</template>

<script>
  // 默认表单数据
  import AddressSelect from '@/components/AddressSelect';
  const DEFAULT_WHERE = {
    bol_no: null,
    air_no: null,
    status: '',
    created_at: '',
    updated_at: ''
  };

  export default {
    name: 'outbound-fba-search',
    components: {
      AddressSelect
    },
    data() {
      return {
        // 表单数据
        where: { ...DEFAULT_WHERE },
        // 搜索表单是否展开
        searchExpand: false
      };
    },
    methods: {
      /* 搜索 */
      search() {
        //解决搜索 也搜索下级的数据
        this.$emit('search', this.where);
      },
      /* 重置搜索 */
      reset() {
        this.where = { ...DEFAULT_WHERE };
        this.search();
      },
      /* 搜索展开/收起 */
      toggleExpand() {
        this.searchExpand = !this.searchExpand;
        this.$emit('expand-change', this.searchExpand);
      },
      /* 搜索展开/收起 */
      goodsCategoryFilterListClick(e) {
        this.goodsCategoryList = e.data;
      }
    }
  };
</script>
