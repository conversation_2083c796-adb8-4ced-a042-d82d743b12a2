<!-- 编辑弹窗 -->
<template>
  <ele-modal
    width="720px"
    :visible="visible"
    :close-on-click-modal="false"
    custom-class="ele-dialog-form"
    :title="$t('basics.edit')"
    @update:visible="updateVisible"
  >
    <el-form ref="form" :model="form" :rules="rules" label-width="100px">
      <el-row type="flex">
        <el-col :span="12">
          <el-form-item :label="$t('order.uid.comments') + ':'" prop="date">
            <el-input v-model="form.remarks" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="$t('car.addressReturn') + ':'" prop="date">
            <address-select v-model="form.return_address" :searchType="2" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div slot="footer">
      <el-button @click="updateVisible(false)">
        {{ $t('basics.cancel') }}
      </el-button>
      <el-button type="primary" :loading="loading" @click="save">
        {{ $t('basics.save') }}
      </el-button>
    </div>
  </ele-modal>
</template>

<script>
  import { modifyRemarks } from '@/api/order/uld';
  import AddressSelect from '@/components/AddressSelect';
  const DEFAULT_FORM = {
    id: null,
    remarks: '',
    return_address: ''
  };

  export default {
    name: 'set-remarks',
    components: {
      AddressSelect
    },
    props: {
      visible: Boolean,
      data: Object
    },
    data() {
      return {
        // 表单数据
        form: { ...DEFAULT_FORM },
        // 表单验证规则
        rules: {
          remarks: [
            {
              required: true,
              message: this.$t('basics.pleaseChoose'),
              trigger: ['blur', 'change']
            }
          ]
        },
        // 提交状态
        loading: false,
        // 是否是修改
        isUpdate: false
      };
    },
    methods: {
      /* 保存编辑 */
      save() {
        this.$refs['form'].validate((valid) => {
          if (!valid) {
            return false;
          }
          this.loading = true;
          modifyRemarks(this.form)
            .then(() => {
              this.loading = false;
              this.$message.success(this.$t('basics.success'));
              this.updateVisible(false);
              this.$emit('done');
            })
            .catch((e) => {
              this.loading = false;
              this.$message.error(e);
            });
        });
      },
      /* 更新visible */
      updateVisible(value) {
        this.$emit('update:visible', value);
      }
    },
    watch: {
      async visible(visible) {
        if (visible) {
          if (this.data) {
            let data = this.data;
            this.$util.assignObject(this.form, {
              ...data
            });
            this.isUpdate = true;
          } else {
            this.isUpdate = false;
          }
        } else {
          this.$refs['form'].clearValidate();
          this.form = { ...DEFAULT_FORM };
        }
      }
    }
  };
</script>
