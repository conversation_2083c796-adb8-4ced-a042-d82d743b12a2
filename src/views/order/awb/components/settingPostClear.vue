<!-- 编辑弹窗 -->
<template>
  <ele-modal
    width="720px"
    :visible="visible"
    :close-on-click-modal="false"
    custom-class="ele-dialog-form"
    :title="$t('order.awb.declarationLink')"
    @update:visible="updateVisible"
  >
    <el-form ref="form" :model="form" label-width="135px">
      <el-row type="flex">
        <el-col :span="11">
          <el-form-item :label="$t('order.awb.is_emergency') + ':'" prop="type">
            <el-select v-model="form.is_emergency" clearable class="ele-fluid">
              <el-option :label="this.$t('basics.no')" :value="1" />
              <el-option :label="this.$t('basics.yes')" :value="2" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="$t('order.awb.emergency_code') + ':'">
            <el-input
              clearable
              class="update-yes"
              :disabled="form.is_emergency === 1"
              :maxlength="50"
              v-model="form.emergency_code"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div slot="footer">
      <el-button @click="updateVisible(false)">
        {{ $t('basics.cancel') }}
      </el-button>
      <el-button type="primary" :loading="loading" @click="save">
        {{ $t('basics.submit') }}
      </el-button>
    </div>
  </ele-modal>
</template>

<script>
  import { postClear } from '@/api/order/awb';

  const DEFAULT_FORM = {
    is_emergency: 1,
    emergency_code: ''
  };

  export default {
    name: 'UserEdit',
    components: {},
    props: {
      visible: Boolean,
      data: Object
    },
    data() {
      return {
        // 表单数据
        form: { ...DEFAULT_FORM },
        // 提交状态
        loading: false,
        // 是否是修改
        isUpdate: false
      };
    },
    methods: {
      save() {
        this.$refs['form'].validate((valid) => {
          if (!valid) {
            return false;
          }
          this.loading = true;
          const data = {
            id: this.data.id,
            is_emergency: this.form.is_emergency,
            emergency_code: this.form.emergency_code
          };
          postClear(data)
            .then((msg) => {
              this.loading = false;
              this.$message.success(this.$t('basics.success'));
              this.updateVisible(false);
              this.$emit('done');
            })
            .catch((e) => {
              this.loading = false;
              this.$message.error(e);
            });
        });
      },
      /* 更新visible */
      updateVisible(value) {
        this.$emit('update:visible', value);
      },
      /* 角色选择回调*/
      roleSelectCallback(value) {
        this.form.role_type = value.type;
      }
    },
    watch: {
      async visible(visible) {
        if (visible) {
          if (this.data) {
            this.isUpdate = true;
          } else {
            this.isUpdate = false;
          }
        } else {
          this.$refs['form'].clearValidate();
          this.form = { ...DEFAULT_FORM };
        }
      }
    }
  };
</script>
