<!-- 编辑弹窗 -->
<template>
  <ele-modal
    width="860px"
    :visible="visible"
    :close-on-click-modal="false"
    custom-class="ele-dialog-form"
    :title="$t('awb.awb.menu.m2')"
    @update:visible="updateVisible"
  >
    <el-form ref="form" :model="form" :rules="rules" label-width="180px">
      <el-row :gutter="15">
        <el-col :sm="12">
          <el-form-item :label="$t('awb.awb.times.time2')+':'" prop="time2">
            <el-date-picker
              v-model="form.time2"
              type="datetime"
              value-format="yyyy-MM-dd HH:mm:ss"
              :placeholder="$t('basics.placeholder.time1')"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :sm="12">
          <el-form-item :label="$t('awb.awb.times.time3')+':'" prop="time3">
            <el-date-picker
              v-model="form.time3"
              type="datetime"
              value-format="yyyy-MM-dd HH:mm:ss"
              :placeholder="$t('basics.placeholder.time1')"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="15">
        <el-col :sm="12">
          <el-form-item :label="$t('awb.awb.times.time4')+':'" prop="time4">
            <el-date-picker
              v-model="form.time4"
              type="datetime"
              value-format="yyyy-MM-dd HH:mm:ss"
              :placeholder="$t('basics.placeholder.time1')"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <!-- <el-row :gutter="15" v-for="(item,index) in form.time5" :key="index">
        <el-col :sm="12">
          <el-form-item :label="'收到NOA'+(index+1)+':'" prop="time5">
            <el-date-picker
              v-model="form.time5[index]"
              type="datetime"
              value-format="yyyy-MM-dd HH:mm:ss"
              placeholder="请选择时间"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-button @click="removeNoa(item,index)">
           {{ $t('basics.delete') }}
        </el-button>
      </el-row> -->
    </el-form>
    <div slot="footer">
      <!-- <el-button @click="addNoa()">
        增加noa时间
      </el-button> -->
      <!-- <el-button @click="updateVisible(false)">
        {{ $t('basics.cancel') }}
      </el-button> -->
      <el-button type="primary" :loading="loading" @click="save">
        {{ $t('basics.save') }}
      </el-button>
    </div>
  </ele-modal>
</template>

<script>
  import { timelineEdit } from '@/api/order/awb'

  const DEFAULT_FORM = {
    id: null,
    time2: '',
    time3: '',
    time4: '',
    time5: [],
  };

  export default {
    name: 'EditTime',
    components: { },
    props: {
      // 弹窗是否打开
      visible: Boolean,
      // 修改回显的数据
      data: Object
    },
    data() {
      return {
        // 表单数据
        form: { ...DEFAULT_FORM },
        // 表单验证规则
        rules: {
          // time2: [
          //   {
          //     required: true,
          //     message: '请输入地址代码',
          //     trigger: 'blur'
          //   }
          // ],
          // time3: [
          //   {
          //     required: true,
          //     message: '请输入地址代码',
          //     trigger: 'blur'
          //   }
          // ],
        },
        // 提交状态
        loading: false,
        // 是否是修改
        isUpdate: false
      };
    },
    methods: {
      /* 保存编辑 */
      save() {
        this.$refs['form'].validate((valid) => {
          if (!valid) {
            return false;
          }
          // let lastArr = this.form.time5.slice(-1)
          // if(lastArr[0] == ''){
          //   this.$message.success('请填写NOA信息，或者删除不需要的NOA时间')
          //   return
          // }
          this.loading = true;
          let data = []
          for(let i=2;i<5;i++){
            data.push({
              type:i,
              time:this.form['time'+i]
            })
          }
          timelineEdit(this.form.id,data)
            .then((msg) => {
              this.loading = false;
              this.$message.success(this.$t('basics.success'));
              this.updateVisible(false);
              this.$emit('done');
            })
            .catch((e) => {
              this.loading = false;
              this.$message.error(e);
            });
        });
      },
      removeNoa(item,index){
        this.form.time5.splice(index,1)
      },
      addNoa(){
        let lastArr = this.form.time5.slice(-1)
        if(lastArr[0] == ''){
          this.$message.success('请填写完上一条在添加')
          return
        }
        this.form.time5.push('')
      },
      /* 更新visible */
      updateVisible(value) {
        this.$emit('update:visible', value);
      },
      /* 角色选择回调*/
      roleSelectCallback(value) {
        this.form.role_type = value.type;
      }
    },
    watch: {
      async visible(visible) {
        console.log(this.data)
        if (visible) {
          let data = this.data;
          if (this.data) {
            this.$util.assignObject(this.form, {
              ...data
            });
            if(this.form.time5.length ==0){
              this.form.time5.push('')
            }
            this.isUpdate = true;
          } else {
            this.isUpdate = false;
          }
        } else {
          this.$refs['form'].clearValidate();
          this.form = { ...DEFAULT_FORM };
        }
      }
    }
  };
</script>
