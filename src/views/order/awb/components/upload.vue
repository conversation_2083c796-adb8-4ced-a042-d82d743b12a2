<!-- 用户导入弹窗 -->
<template>
  <ele-modal
    width="1000px"
    :title="this.$t('car.uploadvoucher')"
    :visible="visible"
    :close-on-click-modal="false"
    @update:visible="updateVisible"
  >
    <el-form
      ref="form"
      :model="awbData"
      :rule="rules"
      label-width="170px"
      label-position="right"
    >
      <el-row :gutter="15">
        <el-col :sm="24">
            <el-upload
              drag
              action=""
              class="ele-block"
              accept=".pdf,.png,.jpg,.gif"
              :on-remove="handleRemove"
              :auto-upload="false"
              :show-file-list="false"
              :on-change="manifestPdfUpload"
              v-loading="manifestPdfLoading"
            >
              <i class="el-icon-upload"></i>
              <div class="el-upload__text" v-if="!manifestPdf.name">
                {{ $t('awb.awb.tips.t7')
                }}<em>{{ $t('awb.awb.tips.t2') }}</em></div
              >
              <div class="el-upload__text" v-else>{{ manifestPdf.name }}</div>
            </el-upload>
        </el-col>
      </el-row>
    </el-form>
    <div slot="footer">
      <el-button @click="updateVisible(false)">
        {{ $t('basics.cancel') }}
      </el-button>
      <el-button type="primary" :loading="loading" @click="save">
        {{ $t('basics.save') }}
      </el-button>
    </div>
  </ele-modal>
</template>

<script>
  import { bolFileUpload } from '@/api/system/file';
  import { uploadPdf} from '@/api/order/awb';
  export default {
    name: 'UploadFile',
    components: {},
    props: {
      // 是否打开弹窗
      visible: Boolean,
      // 修改回显的数据
      data: Object
    },
    data() {
      return {
        manifestPdf: {}, //pdf
        manifestPdfLoading: false,
        // 导入请求状态
        loading: false,
        pdfName: '2',
        awbData: {
          bol_pdf_path: '',
        },
        rules: {
          bol_pdf_path: [
            {
              required: true,
              message: '请上传文件',
              trigger: 'blur'
            }
          ]
        }
      };
    },
    created() {
      //重新打开情况数据
    },
    methods: {
      handleRemove(file) {
        this.highValueManifestList.splice(
          this.highValueManifestList.findIndex((i) => i.uid === file.uid),
          1
        );
      },
      /**
       * pdf上传
       * */
      manifestPdfUpload(file) {
        this.manifestPdfLoading = true;
        //调用上传接口.
        bolFileUpload(file.raw, 'pdf')
          .then(({ result }) => {
            this.manifestPdfLoading = false;
            this.manifestPdf = {
              name: result.filesName,
              url: result.objectUrl,
              data: result
            };
            console.log(this.manifestPdf);
          })
          .catch((e) => {
            this.manifestPdfLoading = false;
            this.$message.error(e);
          });
        return false;
      },
      updateVisible(value) {
        this.$emit('update:visible', value);
      },
      save() {
        console.log(this.data.id);
        //验证pdf或者图片必须存在
        if (!this.manifestPdf?.url) {
          this.$message.warning(this.$t('car.tips3'));
          return false;
        }
        uploadPdf(this.data.id,this.manifestPdf)
          .then((msg) => {
            this.loading = false;
            this.$message.success(this.$t('basics.success'));
            this.updateVisible(false);
            this.$emit('done');
          })
          .catch((e) => {
            this.loading = false;
            this.$message.error(e);
          });
      }
    },
    watch: {
      async visible(visible) {
        this.manifestPdf = {};
      }
    }
  };
</script>

<style scoped>
  .ele-block ::v-deep .el-upload,
  .ele-block ::v-deep .el-upload-dragger {
    width: 100%;
  }
</style>
<style>
  .overflow {
    max-width: 620px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    -o-text-overflow: ellipsis;
  }
</style>
