<!-- 编辑弹窗 -->
<template>
  <ele-modal
    width="1080px"
    :visible="visible"
    :close-on-click-modal="false"
    custom-class="ele-dialog-form"
    :title="$t('awb.awb.menu.m3')"
    @update:visible="updateVisible"
  >
    <ele-pro-table ref="tablelog" :columns="columns" :datasource="datasource">
    </ele-pro-table>
    <div style="height: 20px; width: 100%"></div>
  </ele-modal>
</template>

<script>
  import { logs } from '@/api/order/awb';

  const DEFAULT_FORM = {
    id: null
  };

  export default {
    name: 'EditTime',
    components: {},
    props: {
      // 弹窗是否打开
      visible: Boolean,
      // 修改回显的数据
      data: Object
    },
    data() {
      return {
        // 表单数据
        form: { ...DEFAULT_FORM },
        columns: [
          {
            columnKey: 'index',
            type: 'index',
            width: 45,
            showOverflowTooltip: true,
            fixed: 'left'
          },
          {
            prop: 'type',
            label: this.$t('basics.action'),
            showOverflowTooltip: true,
            formatter: (row, column, cellValue) => {
              if (row.type == 17) {
                return (
                  this.$t(`awb.awb.action_types.${cellValue}`) +
                  ':' +
                  row.storage_name +
                  ':' +
                  row.address
                );
              }
              return this.$t(`awb.awb.action_types.${cellValue}`);
            },
            minWidth: 110
          },
          {
            prop: 'nickname',
            label: this.$t('basics.action_person'),
            showOverflowTooltip: true,
            minWidth: 80
          },
          // {
          //   prop: 'type',
          //   label: '类型',
          //   showOverflowTooltip: true,
          //   minWidth: 110,
          //   formatter: (row, column, cellValue) => {
          //     return ['配送地址', '货站地址'][cellValue-1];
          //   }
          // },
          {
            prop: 'created_at',
            label: this.$t('basics.time'),
            showOverflowTooltip: true,
            minWidth: 80
          }
          // {
          //   prop: 'created_at',
          //   label: '时间',
          //   sortable: 'custom',
          //   showOverflowTooltip: true,
          //   minWidth: 110,
          //   formatter: (row, column, cellValue) => {
          //     return this.$util.toDateString(parseInt(cellValue));
          //   }
          // },
        ],
        // 提交状态
        loading: false,
        // 是否是修改
        isUpdate: false
      };
    },
    methods: {
      datasource({ page, limit, where, order }) {
        return logs({
          ...where,
          ...order,
          page: page,
          num: limit,
          id: this.data.id
        });
      },
      reload() {
        this.$nextTick(() => {
          this.$refs.tablelog.reload({ page: 1, id: this.data.id });
        });
      },
      /* 更新visible */
      updateVisible(value) {
        this.$emit('update:visible', value);
      },
      /* 角色选择回调*/
      roleSelectCallback(value) {
        this.form.role_type = value.type;
      }
    },
    watch: {
      async visible(visible) {
        console.log(this.data);
        this.reload();
        if (visible) {
          let data = this.data;
          if (this.data) {
            this.$util.assignObject(this.form, {
              ...data
            });
            this.isUpdate = true;
          } else {
            this.isUpdate = false;
          }
        } else {
          // this.$refs['form'].clearValidate();
          this.form = { ...DEFAULT_FORM };
        }
      }
    }
  };
</script>
