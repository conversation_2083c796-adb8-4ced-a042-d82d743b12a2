<!-- 编辑弹窗 -->
<template>
  <ele-modal
    width="720px"
    :visible="visible"
    :close-on-click-modal="false"
    custom-class="ele-dialog-form"
    :title="$t('awb.awb.menu.m1')"
    @update:visible="updateVisible"
  >
    <el-timeline :reverse="reverse">
      <el-timeline-item
        v-for="(activity, index) in timedata"
        :key="index"
        :timestamp="activity.timestamp"
      >
        {{ activity.content }}
      </el-timeline-item>
    </el-timeline>
    <div slot="footer">
      <el-button @click="updateVisible(false)">
        {{ $t('basics.cancel') }}
      </el-button>
      <!-- <el-button type="primary" :loading="loading" @click="save">
        {{ $t('basics.save') }}
      </el-button> -->
    </div>
  </ele-modal>
</template>

<script>
  import { timeline } from '@/api/order/awb';

  const DEFAULT_FORM = {
    id: null,
    name: ''
  };

  export default {
    name: 'ShowTime',
    components: {},
    props: {
      reverse: true,
      // 弹窗是否打开
      visible: Boolean,
      // 修改回显的数据
      data: Object
      // timedata:Object
    },
    data() {
      return {
        // 表单数据
        form: { ...DEFAULT_FORM },
        // 表单验证规则
        rules: {
          name: [
            {
              required: true,
              message: this.$t('basics.pleaseInput'),
              trigger: 'blur'
            }
          ]
        },
        typeWords: [
          {
            id: 'time1',
            label: this.$t('awb.awb.times.time1')
          },
          {
            id: 'time2',
            label: this.$t('awb.awb.times.time2')
          },
          {
            id: 'time3',
            label: this.$t('awb.awb.times.time3')
          },
          {
            id: 'time4',
            label: this.$t('awb.awb.times.time4')
          },
          {
            id: 'time5',
            label: this.$t('awb.awb.times.time5')
          },
          {
            id: 'time6',
            label: this.$t('awb.awb.times.time6')
          },
          {
            id: 'time7',
            label: this.$t('awb.awb.times.time7')
          },
          {
            id: 'time8',
            label: this.$t('awb.awb.times.time8')
          }
        ],
        // 提交状态
        loading: false,
        // 是否是修改
        isUpdate: false,
        timedata: []
      };
    },
    async created() {
      /* 获取角色数据 */
    },
    methods: {
      /* 保存编辑 */
      save() {
        this.$refs['form'].validate((valid) => {
          if (!valid) {
            return false;
          }
          this.loading = true;
          const data = {
            ...this.form
          };
          const apiname = this.isUpdate ? update : create;
          apiname(data)
            .then((msg) => {
              this.loading = false;
              this.$message.success(this.$t('basics.success'));
              this.updateVisible(false);
              this.$emit('done');
            })
            .catch((e) => {
              this.loading = false;
              this.$message.error(e);
            });
        });
      },
      /* 更新visible */
      updateVisible(value) {
        this.$emit('update:visible', value);
      },
      /* 角色选择回调*/
      roleSelectCallback(value) {
        this.form.role_type = value.type;
      },
      famtterType(row) {
        let index = this.typeWords.findIndex((a) => a.id == row.type);
        if (index == -1) return;
        if (row.type == 'time6' || row.type == 'time7' || row.type == 'time8') {
          return this.typeWords[index].label + ' CMR:' + row.cmr;
        }
        return this.typeWords[index].label;
      }
    },
    watch: {
      async visible(visible) {
        console.log(visible);
        let timedata = await timeline(this.data);

        let TimeLabel = [
          this.$t('awb.awb.times.time1'),
          this.$t('awb.awb.times.time2'),
          this.$t('awb.awb.times.time3'),
          this.$t('awb.awb.times.time4'),
          this.$t('awb.awb.times.time9'),
          this.$t('awb.awb.times.time10'),
          this.$t('awb.awb.times.time11')
        ];
        let arry = [];
        timedata.map((item, index) => {
          let data = {
            content: '',
            timestamp: ''
          };
          data.content = TimeLabel[index];

          data.timestamp = item;
          arry.push(data);
        });
        this.timedata = arry.filter((i) => i.timestamp);
        if (visible) {
          let data = this.data;
          if (this.data) {
            this.isUpdate = true;
          } else {
            this.isUpdate = false;
          }
        } else {
          // this.$refs['form'].clearValidate();
          // this.form = { ...DEFAULT_FORM };
        }
      }
    }
  };
</script>
