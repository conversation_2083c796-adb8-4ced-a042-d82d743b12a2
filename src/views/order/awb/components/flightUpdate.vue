<!-- 用户导入弹窗 -->
<template>
  <ele-modal
    width="520px"
    :title="$t('awb.awb.title3')"
    :visible="visible"
    :close-on-click-modal="false"
    @update:visible="updateVisible"
  >
    <el-upload
      drag
      action=""
      class="ele-block"
      v-loading="loading"
      accept=".xls,.xlsx"
      :show-file-list="false"
      :before-upload="doUpload"
    >
      <i class="el-icon-upload"></i>
      <div class="el-upload__text"
        >{{ $t('awb.awb.tips.t1') }}<em>{{ $t('awb.awb.tips.t2') }}</em></div
      >
      <div class="el-upload__tip ele-text-center" slot="tip">
        <span>{{ $t('awb.awb.tips.t30') }} </span>
        <el-link
          download
          :href="url"
          type="primary"
          :underline="false"
          style="vertical-align: baseline"
        >
          {{ $t('awb.awb.tips.t4') }}
        </el-link>
      </div>
    </el-upload>
  </ele-modal>
</template>

<script>
  import { uploadFile } from '@/api/system/file';
  import { uploadFlight } from '@/api/order/awb';

  export default {
    name: 'UploadFile',
    props: {
      // 是否打开弹窗
      visible: Boolean
    },
    data() {
      return {
        // 导入请求状态
        loading: false,
        // 导入模板下载地址
        // url: 'https://yunkesys.obs.ap-southeast-1.myhuaweicloud.com/wmsbe/package_inspection_sample.xlsx',
        // url: 'https://test-apiserver.obs.ap-southeast-1.myhuaweicloud.com/wmsbe/flight_sample.xls',
        url: 'https://erp-tpl.oss-eu-central-1.aliyuncs.com/wmsbe/flight_sample.xls',
        // url: this.$store.getters.user.info.web_url + '2022-08-25/o3ybKMOEcwJCqz9RmQF7cDRe2bCkTY6MhevLKCrX.xlsx',
        manifest: '1',
        pdfName: '2',
        bol_path: ''
      };
    },
    methods: {
      /* 上传 */
      doUpload(file) {
        this.loading = true;
        console.log(file.name);
        uploadFile(file)
          .then((res) => {
            this.loading = false;
            this.bol_path = res.files;
            this.getUploadFlight();
          })
          .catch((e) => {
            this.loading = false;
            this.$message.error(e);
          });
        return false;
      },
      getUploadFlight() {
        this.loading = true;
        uploadFlight({ bol_path: this.bol_path })
          .then((res) => {
            this.loading = false;
            this.$message.success(this.$t('basics.success'));
            this.updateVisible(false);
            this.$emit('done');
          })
          .catch((e) => {
            this.loading = false;
            this.$message.error(e);
          });
      },
      /* 更新visible */
      updateVisible(value) {
        this.$emit('update:visible', value);
      }
    }
  };
</script>

<style scoped>
  .ele-block ::v-deep .el-upload,
  .ele-block ::v-deep .el-upload-dragger {
    width: 100%;
  }
</style>
