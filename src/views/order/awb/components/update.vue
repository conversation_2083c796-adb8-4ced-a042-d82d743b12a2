<!-- 用户导入弹窗 -->
<template>
  <ele-modal
    width="1000px"
    :title="$t('awb.awb.title2')"
    :visible="visible"
    :close-on-click-modal="false"
    @update:visible="updateVisible"
  >
    <el-form
      ref="form"
      :model="awbData"
      :rule="rules"
      label-width="170px"
      label-position="right"
    >
      <el-row :gutter="15">
        <el-col :sm="24" v-permission="'awb:nouserSearch'">
          <el-form-item :label="$t('awb.awb.col9') + ':'" prop="user_id">
            <user-select v-model="awbData.user_id" />
          </el-form-item>
        </el-col>
        <!--  箱数  -->
        <el-col :sm="12">
          <el-form-item
            :label="$t('batch.out.boxnum') + ':'"
            prop="exchange_flag"
          >
            <el-input
              clearable
              :maxlength="50"
              show-word-limit
              v-model="awbData.box_number"
              :placeholder="$t('basics.pleaseInput')"
            />
          </el-form-item>
        </el-col>
        <!--  清关代理  -->
        <el-col :sm="12">
          <el-form-item
            :label="$t('awb.awb.agent') + ':'"
            prop="agent"
            v-permission="'awb:nouserSearch'"
          >
            <agent-select v-model="awbData.agent" />
          </el-form-item>
        </el-col>
        <!--  包裹数  -->
        <el-col :sm="12">
          <el-form-item
            :label="$t('batch.out.packagenum') + ':'"
            prop="exchange_flag"
          >
            <el-input
              clearable
              :maxlength="50"
              show-word-limit
              v-model="awbData.package_number"
              :placeholder="$t('basics.pleaseInput')"
            />
          </el-form-item>
        </el-col>
        <!--  T1代理  -->
        <el-col :sm="12">
          <el-form-item
            :label="$t('awb.awb.t1') + ':'"
            prop="t1"
            v-permission="'awb:nouserSearch'"
          >
            <agent-select v-model="awbData.t1" />
          </el-form-item>
        </el-col>
        <!--  毛重  -->
        <el-col :sm="12">
          <el-form-item
            :label="$t('car.grossweight') + ':'"
            prop="plan_end_date"
          >
            <el-input
              clearable
              :maxlength="50"
              show-word-limit
              v-model="awbData.gross_weight"
              :placeholder="$t('basics.pleaseInput')"
            />
          </el-form-item>
        </el-col>
        <!--  业务类型  -->
        <el-col :sm="12">
          <el-form-item
            :label="$t('awb.awb.col1') + ':'"
            prop="b2b"
          >
            <bussiness-select v-model="awbData.b2b" />
          </el-form-item>
        </el-col>
        <!--  计费重量  -->
        <el-col :sm="12">
          <el-form-item
            :label="$t('awb.awb.col6') + '(kg):'"
            prop="gross_mass_kg"
          >
            <el-input
              clearable
              :maxlength="50"
              show-word-limit
              v-model="awbData.gross_mass_kg"
              :placeholder="$t('basics.pleaseInput')"
            />
          </el-form-item>
        </el-col>
        <!--  换标数  -->
        <el-col :sm="12">
          <el-form-item :label="$t('awb.awb.col8') + ':'" prop="exchange_flag">
            <el-input
              clearable
              :maxlength="50"
              show-word-limit
              v-model="awbData.exchange_flag"
              :placeholder="$t('basics.pleaseInput')"
            />
          </el-form-item>
        </el-col>
        <!--  航班港口  -->
        <el-col :sm="12">
          <el-form-item :label="$t('awb.awb.port') + ':'" prop="lgg">
            <port-select v-model="awbData.lgg" />
          </el-form-item>
        </el-col>
        <!--  航班号  -->
        <el-col :sm="12">
          <el-form-item :label="$t('awb.awb.col10') + ':'" prop="flight_no">
            <el-input
              clearable
              :maxlength="50"
              show-word-limit
              v-model="awbData.flight_no"
              :placeholder="$t('basics.pleaseInput')"
            />
          </el-form-item>
        </el-col>
        <!--  预计起飞时间  -->
        <el-col :sm="12">
          <el-form-item
            :label="$t('awb.awb.col17') + ':'"
            prop="plan_start_date"
          >
            <el-date-picker
              v-model="awbData.plan_start_date"
              type="datetime"
              value-format="yyyy-MM-dd HH:mm:ss"
              :placeholder="$t('basics.placeholder.time1')"
              class="ele-fluid"
            >
            </el-date-picker>
          </el-form-item>
        </el-col>
        <!--  预计落地时间  -->
        <el-col :sm="12">
          <el-form-item :label="$t('awb.awb.col18') + ':'" prop="plan_end_date">
            <el-date-picker
              v-model="awbData.plan_end_date"
              type="datetime"
              value-format="yyyy-MM-dd HH:mm:ss"
              :placeholder="$t('basics.placeholder.time1')"
              class="ele-fluid"
            >
            </el-date-picker>
          </el-form-item>
        </el-col>

        <!-- <el-col :sm="12">
          <el-form-item :label="$t('awb.awb.col7') + ':'" prop="expensive">
            <el-input
              clearable
              :maxlength="50"
              show-word-limit
              v-model="awbData.expensive"
              :placeholder="$t('basics.pleaseInput')"
            />
          </el-form-item>
        </el-col> -->
      </el-row>
      <el-row :gutter="15">
        <el-col :sm="24">
          <el-form-item
            :label="$t('order.awb.lowValueManifest')"
            prop="bol_path"
          >
            <el-upload
              drag
              action=""
              class="ele-block"
              v-loading="lowValueManifestLoading"
              :auto-upload="false"
              accept=".xlsx"
              :show-file-list="false"
              :on-change="lowValueManifestUpload"
            >
              <i class="el-icon-upload"></i>
              <div
                class="el-upload__text"
                v-if="!(lowValueManifest && lowValueManifest.url)"
                >{{ $t('awb.awb.tips.t1')
                }}<em>{{ $t('awb.awb.tips.t2') }}</em></div
              >
              <div class="el-upload__text" v-else>{{
                lowValueManifest.name
              }}</div>
              <div class="el-upload__tip ele-text-center" slot="tip">
                <span>{{ $t('awb.awb.tips.t3') }}</span>
                <el-link
                  download
                  :href="url"
                  type="primary"
                  :underline="false"
                  style="vertical-align: baseline"
                >
                  {{ $t('awb.awb.tips.t4') }}
                </el-link>
              </div>
            </el-upload>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="15">
        <el-col :sm="24">
          <el-form-item
            :label="$t('order.awb.highValueManifest')"
            prop="bol_path"
          >
            <el-upload
              class="ele-block"
              accept=".xlsx"
              drag
              action=""
              multiple
              :on-remove="handleRemove"
              :auto-upload="false"
              :file-list="highValueManifestList"
              :on-change="highValueManifestUpload"
              v-loading="highValueManifestLoading"
            >
              <i class="el-icon-upload"></i>
              <div class="el-upload__text"
                >{{ $t('awb.awb.tips.t1')
                }}<em>{{ $t('awb.awb.tips.t2') }}</em></div
              >
            </el-upload>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="15">
        <el-col :sm="24">
          <el-form-item
            :label="$t('order.awb.bol_pdf_path')"
            prop="bol_pdf_path"
          >
            <el-upload
              drag
              action=""
              class="ele-block"
              accept=".pdf,.png,.jpg,.gif"
              :on-remove="handleRemove"
              :auto-upload="false"
              :show-file-list="false"
              :on-change="manifestPdfUpload"
              v-loading="manifestPdfLoading"
            >
              <i class="el-icon-upload"></i>
              <div class="el-upload__text" v-if="!manifestPdf.name">
                {{ $t('awb.awb.tips.t7')
                }}<em>{{ $t('awb.awb.tips.t2') }}</em></div
              >
              <div class="el-upload__text" v-else>{{ manifestPdf.name }}</div>
            </el-upload>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div slot="footer">
      <el-button @click="updateVisible(false)">
        {{ $t('basics.cancel') }}
      </el-button>
      <el-button type="primary" :loading="loading" @click="save">
        {{ $t('basics.save') }}
      </el-button>
    </div>
  </ele-modal>
</template>

<script>
  import { hasPermission } from '@/utils/permission';
  import { bolFileUpload } from '@/api/system/file';
  import { create } from '@/api/order/awb';
  import BussinessSelect from '@/components/BussinessSelect';
  import AgentSelect from '@/components/AgentSelect';
  import PortSelect from '@/components/PortSelect';
  // import { UserSelect } from '@/components/select/owner'

  export default {
    name: 'UploadFile',
    components: {
      UserSelect: () => import('./user-select'),
      AgentSelect,
      PortSelect,
      BussinessSelect
    },
    props: {
      // 是否打开弹窗
      visible: Boolean
    },
    data() {
      return {
        highValueManifestList: [], //高价值包裹列表
        lowValueManifest: {}, //低价值包裹列表
        manifestPdf: {}, //pdf
        highValueManifestLoading: false,
        lowValueManifestLoading: false,
        manifestPdfLoading: false,
        // 导入请求状态
        loading: false,
        loading1: false,
        loading2: false,
        // 导入模板下载地址
        // url: 'https://yunkesys.obs.ap-southeast-1.myhuaweicloud.com/wmsbe/manifest_sample.xlsx',
        //url: 'https://eur-test-read-open.oss-eu-central-1.aliyuncs.com/billOfLading/manifest_sample.xlsx',
        url: 'https://eur-test-read-open.oss-eu-central-1.aliyuncs.com/billOfLading/manifest_sample.xlsx',
        // url: this.$store.getters.user.info.web_url + '2022-08-25/9ZQEiPlxOcKtwevt8YNBZOpM9Dbc10nGS5bYfQLP.xlsx',
        manifest: '1',
        pdfName: '2',
        awbData: {
          bol_path: '',
          bol_pdf_path: '',
          user_id: null,
          bol_no: '',
          flight_time: '',
          flight_no: '',
          gross_mass_kg: '',
          exchange_flag: null,
          lgg: '',
          b2b: '',
          t1: '',
          expensive: null,
          clearance_fee: null,
          attach_fee: null,
          gross_weight: '',
          box_number: '',
          package_number: ''
        },
        rules: {
          user_id: [
            {
              required: true,
              message: this.$t('basics.pleaseChoose'),
              trigger: ['blur', 'change']
            }
          ],
          bol_path: [
            {
              required: true,
              message: '请上传文件',
              trigger: 'blur'
            }
          ],
          bol_pdf_path: [
            {
              required: true,
              message: '请上传文件',
              trigger: 'blur'
            }
          ]
        }
      };
    },
    created() {
      //重新打开情况数据
    },
    methods: {
      handleRemove(file) {
        this.highValueManifestList.splice(
          this.highValueManifestList.findIndex((i) => i.uid === file.uid),
          1
        );
      },
      /**
       * 高价值上传
       * */
      highValueManifestUpload(file, fileList) {
        const fileListIndex = fileList.findIndex((i) => i.uid === file.uid);
        //判断名称是否重复
        if (
          this.highValueManifestList.findIndex((i) => i.name === file.name) !==
          -1
        ) {
          this.highValueManifestList.splice(fileListIndex, 1);
          return this.$message.error(this.$t('order.awb.nameErr'));
        }
        this.highValueManifestLoading = true;
        //调用上传接口.
        bolFileUpload(file.raw, 'highValueManifest')
          .then(({ result }) => {
            this.highValueManifestLoading = false;
            this.highValueManifestList.push({
              uid: file.uid,
              name: result.filesName,
              url: result.objectUrl,
              data: result
            });
          })
          .catch((e) => {
            this.highValueManifestLoading = false;
            fileList.splice(fileListIndex);
            this.$message.error(e);
          });
        return false;
      },
      /**
       * 低价值上传
       * */
      lowValueManifestUpload(file) {
        this.lowValueManifestLoading = true;
        //调用上传接口.
        bolFileUpload(file.raw, 'lowValueManifest')
          .then(({ result }) => {
            this.lowValueManifestLoading = false;
            this.lowValueManifest = {
              name: result.filesName,
              url: result.objectUrl,
              data: result
            };
          })
          .catch((e) => {
            this.lowValueManifestLoading = false;
            this.$message.error(e);
          });
        return false;
      },
      /**
       * pdf上传
       * */
      manifestPdfUpload(file) {
        this.manifestPdfLoading = true;
        //调用上传接口.
        bolFileUpload(file.raw, 'pdf')
          .then(({ result }) => {
            this.manifestPdfLoading = false;
            this.manifestPdf = {
              name: result.filesName,
              url: result.objectUrl,
              data: result
            };
            console.log(this.manifestPdf);
          })
          .catch((e) => {
            this.manifestPdfLoading = false;
            this.$message.error(e);
          });
        return false;
      },
      save() {
        //验证高低价值必须存在一个
        if (
          this.highValueManifestList.length === 0 &&
          !this.lowValueManifest?.name
        ) {
          return this.$message.error(this.$t('order.awb.highValueManifestMin'));
        }
        //验证pdf或者图片必须存在
        if (!this.manifestPdf?.name) {
          return this.$message.error(this.$t('awb.awb.tips.t6'));
        }
        if (this.awbData.flight_no == '') {
          this.$message.error(this.$t('awb.awb.tips.t11'));
          return;
        }
        if (this.awbData.gross_mass_kg == '') {
          this.$message.error(this.$t('awb.awb.tips.t12'));
          return;
        }
        if (
          this.awbData.plan_start_date == '' ||
          this.awbData.plan_start_date == null
        ) {
          this.$message.error(this.$t('awb.awb.tips.t13'));
          return;
        }
        if (
          this.awbData.plan_end_date == '' ||
          this.awbData.plan_end_date == null
        ) {
          this.$message.error(this.$t('awb.awb.tips.t14'));
          return;
        }
        this.loading = true;
        create({
          ...this.awbData,
          manifest_pdf: this.manifestPdf.data,
          manifest: {
            lowValue: this.lowValueManifest?.data ?? [],
            highValue: this.highValueManifestList.map((i) => {
              return i.data;
            })
          }
        })
          .then((res) => {
            console.log(res);
            this.loading = false;
            this.$message.success(this.$t('basics.success'));
            this.updateVisible(false);
            this.$emit('done');
          })
          .catch((err) => {
            this.loading = false;
            this.$message({
              dangerouslyUseHTMLString: true,
              type: 'error',
              message: '<div class="overflow">' + err + '</div>'
            });
          });
      },
      /* 更新visible */
      updateVisible(value) {
        this.$emit('update:visible', value);
      }
    },
    watch: {
      async visible(visible) {
        this.highValueManifestList = []; //高价值包裹列表
        this.lowValueManifest = {}; //高价值包裹列表
        this.manifestPdf = {}; //高价值包裹列表
        if (visible) {
          this.awbData = {
            bol_path: '',
            bol_pdf_path: '',
            flight_no: '',
            gross_mass_kg: '',
            plan_start_date: '',
            b2b: 'B2C',
            agent: 'EDT',

            plan_end_date: '',
            user_id: !hasPermission('awb:nouserSearch')
              ? this.$store.getters.user.info.id
              : null
          };
        } else {
          this.awbData = {
            bol_path: '',
            bol_pdf_path: '',
            flight_no: '',
            gross_mass_kg: '',
            plan_start_date: '',
            plan_end_date: '',
            user_id: null
          };
        }
      }
    }
  };
</script>

<style scoped>
  .ele-block ::v-deep .el-upload,
  .ele-block ::v-deep .el-upload-dragger {
    width: 100%;
  }
</style>
<style>
  .overflow {
    max-width: 620px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    -o-text-overflow: ellipsis;
  }
</style>
