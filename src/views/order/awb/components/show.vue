<!-- 编辑弹窗 -->
<template>
  <ele-modal
    width="1000px"
    :visible="visible"
    :close-on-click-modal="false"
    custom-class="ele-dialog-form"
    :title="$t('basics.edit')"
    @update:visible="updateVisible"
  >
    <el-form ref="form" :model="form" label-width="170px">
      <!--          备注-->
      <el-row :gutter="15">
        <!-- 毛重-->
        <el-col :sm="24">
          <div style="height: 600px; overflow: scroll;">
            <embed
              v-if="isPDF"
              :src="form.bol_pdf_path"
              type="application/pdf"
              width="100%"
              height="100%"
            />
            <embed
              v-else
              :src="form.bol_pdf_path"
              type="image/jpg"
              width="100%"
            />
          </div>
        </el-col>
      </el-row>
    </el-form>
    <div slot="footer">
      <el-button @click="updateVisible(false)">
        {{ $t('basics.cancel') }}
      </el-button>
    </div>
  </ele-modal>
</template>

<script>
  const DEFAULT_FORM = {
    id: null,
    bol_no: '',
    bol_pdf_path: ''
  };

  export default {
    name: 'EditAwb',
    components: {},
    props: {
      // 弹窗是否打开
      visible: Boolean,
      // 修改回显的数据
      data: Object
    },
    data() {
      return {
        // 表单数据
        form: { ...DEFAULT_FORM },
        //文件类型
        isPDF: true,
        // 表单验证规则
        isUpdate: false
      };
    },
    methods: {
      updateVisible(value) {
        this.$emit('update:visible', value);
      }
    },
    watch: {
      async visible(visible) {
        if (visible) {
          let data = this.data;
          if (this.data) {
            this.$util.assignObject(this.form, {
              ...data
            });
            let pdfPath = this.form.bol_pdf_path;
            if (pdfPath.substr(-3).toLowerCase() == 'pdf') {
              this.isPDF = true;
            } else {
              this.isPDF = false;
            }
            this.isUpdate = true;
          } else {
            this.isUpdate = false;
          }
        } else {
          this.$refs['form'].clearValidate();
          this.form = { ...DEFAULT_FORM };
        }
      }
    }
  };
</script>
