<!-- 编辑弹窗 -->
<template>
  <ele-modal
    width="720px"
    :visible="visible"
    :close-on-click-modal="false"
    custom-class="ele-dialog-form"
    :title="$t('awb.awb.menu.m10')"
    @update:visible="updateVisible"
  >
    <el-form ref="form" :model="form" :rules="rules" label-width="120px">
      <el-row type="flex">
        <!-- <el-col :span="12">
          <el-form-item label="状态:" prop="type">
            <el-select
              v-model="form.type"
              clearable
              class="ele-fluid"
            >
              <el-option label="是否通知海关" :value="2" />
              <el-option label="放行" :value="3" />
              <el-option label="海关取走" :value="4" />
            </el-select>
          </el-form-item>
        </el-col> -->

        <el-col :lg="16" :span="12">
          <el-form-item :label="$t('awb.awb.clearStartTime') + ':'" prop="time">
            <el-date-picker
              v-model="form.time"
              :placeholder="$t('basics.placeholder.time1')"
              type="datetime"
              value-format="yyyy-MM-dd HH:mm:ss"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div slot="footer">
      <el-button @click="updateVisible(false)">
        {{ $t('basics.cancel') }}
      </el-button>
      <el-button type="primary" :loading="loading" @click="save">
        {{ $t('basics.save') }}
      </el-button>
    </div>
  </ele-modal>
</template>

<script>
import { getclear, notStarted, getDeclaration } from '@/api/order/awb';

const DEFAULT_FORM = {
  time: ''
};

export default {
  name: 'UserEdit',
  components: {},
  props: {
    visible: Boolean,
    data: Object
  },
  data() {
    return {
      // 表单数据
      form: { ...DEFAULT_FORM },
      // 表单验证规则
      rules: {
        date: [
          {
            required: true,
            message: this.$t('basics.pleaseChoose'),
            trigger: ['blur', 'change']
          }
        ]
      },
      // 提交状态
      loading: false,
      // 是否是修改
      isUpdate: false
    };
  },
  methods: {
    /* 保存编辑 */
    // const loading = this.$loading({ lock: true });
    //   notStarted(row.id)
    //     .then((msg) => {
    //       loading.close();
    //       this.$message.success(this.$t('basics.success'));
    //       this.reload();
    //     })
    //     .catch((e) => {
    //       loading.close();
    //       this.$message.error(e);
    //     });
    save() {
      this.$refs['form'].validate((valid) => {
        if (!valid) {
          return false;
        }
        this.loading = true;
        const data = {
          id: this.data.id,
          time: this.form.time
        };
        console.log(data);
        notStarted(data)
          .then((msg) => {
            console.log(msg);
            // this.submitDeclaration();
            this.loading = false;
            this.$message.success(this.$t('basics.success'));
            this.updateVisible(false);
            this.$emit('done');
          })
          .catch((e) => {
            this.loading = false;
            this.$message.error(e);
          });
      });
    },
    /* 更新visible */
    updateVisible(value) {
      this.$emit('update:visible', value);
    },
    /* 角色选择回调*/
    roleSelectCallback(value) {
      this.form.role_type = value.type;
    },
  },
  watch: {
    async visible(visible) {
      if (visible) {
         var time = new Date();
        var y = time.getFullYear();
        var m = (time.getMonth()+1).toString().padStart(2,"0");
        var d = time.getDate().toString().padStart(2,"0");
        var hh = time.getHours().toString().padStart(2,"0");
        var mm = time.getMinutes().toString().padStart(2,"0");
        var ss = time.getSeconds().toString().padStart(2,"0");
        this.form.time = y + '-' + m + '-' + d + " " + hh + ":" + mm + ":" + ss
        // 2022-11-09 00:00:00
        if (this.data) {
          this.isUpdate = true;
        } else {
          this.isUpdate = false;
        }
      } else {
        this.$refs['form'].clearValidate();
        this.form = { ...DEFAULT_FORM };
      }
    }
  }
};
</script>
