<!-- 编辑弹窗 -->
<template>
  <ele-modal
    width="720px"
    :visible="visible"
    :close-on-click-modal="false"
    custom-class="ele-dialog-form"
    :title="$t('awb.awb.action_types.15')"
    @update:visible="updateVisible"
  >
    <el-form ref="form" :model="form" :rules="rules" label-width="120px">
      <el-row type="flex">
        <el-col :lg="16" :span="12">
          <el-form-item :label="$t('awb.noa.awbno')" prop="time">
            <el-input disabled :maxlength="50" v-model="dataA.bol_no" />
          </el-form-item>
          <el-form-item
            :label="$t('order.awb.sendMail.AddresseeA')"
            prop="time"
          >
            <el-input
              clearable
              type="textarea"
              :popper-append-to-body="false"
              disabled
              :value="getAddressee()"
            />
          </el-form-item>
          <el-form-item
            :label="$t('order.awb.sendMail.addresses')"
            prop="addressesId"
          >
            <address-select
              @addressClick="addressClick"
              v-model="form.addressesId"
              :searchType="2"
            />
          </el-form-item>
          <el-form-item
            :label="$t('order.awb.sendMail.istNumber')"
            prop="istNumber"
          >
            <el-input
              clearable
              :maxlength="50"
              show-word-limit
              v-model="form.istNumber"
              :placeholder="$t('basics.pleaseInput')"
            />
          </el-form-item>
          <el-form-item
            :label="$t('order.awb.sendMail.estimatedArrivalDate')"
            prop="estimatedArrivalDate"
          >
            <el-date-picker
              v-model="form.estimatedArrivalDate"
              type="datetime"
              value-format="yyyy-MM-dd HH:mm:ss"
              :placeholder="$t('basics.placeholder.time1')"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div slot="footer">
      <el-button @click="updateVisible(false)">
        {{ $t('basics.cancel') }}
      </el-button>
      <el-button type="primary" :loading="loading" @click="save">
        {{ $t('awb.noa.sendMail') }}
      </el-button>
    </div>
  </ele-modal>
</template>

<script>
  import { sendMail } from '@/api/order/awb';
  import AddressSelect from '@/components/AddressSelect';
  import { toDateString } from 'ele-admin';
  import { getConfig } from '@/api/views';

  const DEFAULT_FORM = {
    addressesId: null,
    istNumber: null,
    estimatedArrivalDate: null
  };

  export default {
    name: 'sendMail',
    components: { AddressSelect },
    props: {
      visible: Boolean,
      data: Object
    },
    data() {
      return {
        // 表单数据
        form: { ...DEFAULT_FORM },
        addressesId: null,
        addressClickList: [],
        addresseeNew: [],
        addressee: [],
        dataA: {},
        // 表单验证规则
        rules: {
          addressesId: [
            {
              required: true,
              message: this.$t('basics.pleaseChoose'),
              trigger: ['blur', 'change']
            }
          ],
          istNumber: [
            {
              required: true,
              message: this.$t('basics.pleaseChoose'),
              trigger: ['blur', 'change']
            }
          ],
          estimatedArrivalDate: [
            {
              required: true,
              message: this.$t('basics.pleaseChoose'),
              trigger: ['blur', 'change']
            }
          ]
        },
        // 提交状态
        loading: false,
        // 是否是修改
        isUpdate: false
      };
    },
    methods: {
      async setaaa(){
         const { content } = await getConfig();
      // istMailRecipientsL
      this.addressee = [];
      this.addressClickList = [];
      content.istMailRecipientsL.split(',').map((v) => {
        this.addressee.push({
          address: v,
          name: ''
        });
      });
      content.otherIstMailRecipientsL.split(',').map((v) => {
        const aa = v.split(':');
        this.addressClickList[aa[0]] = aa[1];
      });
      },
      getAddressee() {
        let msg = '';
        this.addressee.map((v) => {
          msg = `${msg}${v.address};`;
        });
        if (this.addresseeNew?.address) {
          msg = `${msg}${this.addresseeNew.address};`;
        }
        return msg;
      },
      addressClick(e) {
        if (this.addressClickList[e]) {
          this.addresseeNew = {
            address: this.addressClickList[e],
            name: null
          };
        } else {
          this.addresseeNew = [];
        }
      },
      /* 保存编辑 */
      save() {
        this.$refs['form'].validate((valid) => {
          if (!valid) {
            return false;
          }
          this.loading = true;

          let addressee = this.addressee;
          if (this.addresseeNew?.address) {
            addressee.push(this.addresseeNew);
          }

          const data = {
            id: this.data.id,
            addressesId: this.form.addressesId,
            estimatedArrivalDate: toDateString(
              this.form.estimatedArrivalDate,
              'yyyy-MM-dd HH:mm:ss'
            ),
            istNumber: this.form.istNumber,
            addressee: addressee
          };

              this.addressee = [];
              this.addressClickList = [];
          sendMail(data)
            .then(() => {
              this.loading = false;
              this.$message.success(this.$t('basics.success'));
              this.updateVisible(false);
              this.$emit('done');
            })
            .catch((e) => {
              this.loading = false;
              this.$message.error(e);
            });
        });
      },
      /* 更新visible */
      updateVisible(value) {
        this.$emit('update:visible', value);
      },
      /* 角色选择回调*/
      roleSelectCallback(value) {
        this.form.role_type = value.type;
      }
    },
    watch: {
      async visible(visible) {
        if (visible) {
          let data = this.data;
          console.log('data', data);
          if (this.data) {
            this.setaaa();
            this.$util.assignObject(this.dataA, {
              ...data
            });
            this.dataA = data;
            this.isUpdate = true;

            this.form = { ...DEFAULT_FORM };
            this.addresseeNew = [];
          } else {
            this.isUpdate = false;
          }
        }
      }
    }
  };
</script>
