<template>
  <el-card shadow="never" body-style="padding: 22px 22px 7px 22px;">
    <!-- 搜索表单 -->
    <el-form
      label-width="170px"
      class="ele-form-search"
      @keyup.enter.native="search"
      @submit.native.prevent
    >
      <el-row :gutter="15">
        <el-col :lg="8" :md="12">
          <el-form-item :label="this.$t('awb.noa.awbno') + ':'" prop="bol_no">
            <el-input
              v-model="where.bol_no"
              :placeholder="this.$t('basics.pleaseInput')"
            />
          </el-form-item>
        </el-col>
        <el-col :lg="8" :md="12">
          <el-form-item
            :label="this.$t('awb.awb.clearStatus') + ':'"
            prop="clearance"
          >
            <el-select
              v-model="where.clearance"
              :placeholder="this.$t('basics.pleaseChoose')"
              clearable
              class="ele-fluid"
            >
              <el-option :label="this.$t('basics.statusWords.s1')" :value="1" />
              <el-option :label="this.$t('basics.statusWords.s2')" :value="2" />
              <el-option :label="this.$t('basics.statusWords.s3')" :value="3" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :lg="8" :md="12">
          <el-form-item :label="this.$t('basics.status') + ':'" prop="status">
            <el-select
              v-model="where.status"
              :placeholder="this.$t('basics.pleaseChoose')"
              clearable
              class="ele-fluid"
            >
              <el-option :label="$t('awb.awb.status.s1')" :value="1" />
              <el-option :label="$t('awb.awb.status.s2')" :value="2" />
              <el-option :label="$t('awb.awb.status.s3')" :value="3" />
              <!--<el-option :label="$t('awb.awb.status.s5')" :value="5" />-->
              <el-option :label="$t('awb.awb.status.s6')" :value="6" />
              <el-option :label="$t('awb.awb.status.s4')" :value="4" />
              <!-- <el-option label="已完成" :value="5" /> -->
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :lg="8" :md="12">
          <el-form-item
            :label="this.$t('awb.awb.is_stock') + ':'"
            prop="isStock"
          >
            <el-select
              v-model="where.is_stock"
              :placeholder="this.$t('basics.pleaseChoose')"
              clearable
              class="ele-fluid"
            >
              <el-option :label="this.$t('basics.no')" :value="1" />
              <el-option :label="this.$t('basics.yes')" :value="2" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :lg="8" :md="12">
          <el-form-item
            :label="$t('basics.createTime') + ':'"
            prop="created_at"
          >
            <el-date-picker
              v-model="where.created_at"
              type="datetimerange"
              value-format="yyyy-MM-dd HH:mm:ss"
              :range-separator="$t('basics.to')"
              class="ele-fluid"
              :start-placeholder="$t('basics.beginTime')"
              :end-placeholder="$t('basics.endTime')"
            >
            </el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :lg="8" :md="12" v-if="searchExpand">
          <el-form-item
            :label="this.$t('awb.awb.batch') + ':'"
            prop="bol_batch"
          >
            <el-input
              v-model="where.bol_batch"
              :placeholder="this.$t('basics.pleaseInput')"
            />
          </el-form-item>
        </el-col>
        <el-col
          :lg="8"
          :md="12"
          v-permission="'awb:nouserSearch'"
          v-if="searchExpand"
        >
          <el-form-item :label="`${$t('awb.awb.col9')}:`" prop="bol_no">
            <user-select
              v-model="where.user_id"
              @callback="roleSelectCallback"
            />
          </el-form-item>
        </el-col>
        <el-col :lg="8" :md="12" v-if="searchExpand">
          <el-form-item :label="$t('awb.awb.col18') + ':'" prop="plan_end_date">
            <el-date-picker
              v-model="where.plan_end_date"
              type="datetimerange"
              value-format="yyyy-MM-dd HH:mm:ss"
              :range-separator="$t('basics.to')"
              class="ele-fluid"
              :start-placeholder="$t('basics.beginTime')"
              :end-placeholder="$t('basics.endTime')"
            >
            </el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :lg="8" :md="12" v-if="searchExpand">
          <el-form-item :label="$t('awb.awb.col21') + ':'" prop="time3">
            <el-date-picker
              v-model="where.time3"
              type="datetimerange"
              value-format="yyyy-MM-dd HH:mm:ss"
              :range-separator="$t('basics.to')"
              class="ele-fluid"
              :start-placeholder="$t('basics.beginTime')"
              :end-placeholder="$t('basics.endTime')"
            >
            </el-date-picker>
          </el-form-item>
        </el-col>
        <el-col
          :lg="8"
          :md="12"
          v-permission="'awb:nouserSearch'"
          v-if="searchExpand"
        >
          <el-form-item :label="this.$t('awb.awb.t1') + ':'" prop="t1">
            <agent-select v-model="where.t1" @callback="roleSelectCallback" />
          </el-form-item>
        </el-col>
        <el-col
          :lg="8"
          :md="12"
          v-permission="'awb:nouserSearch'"
          v-if="searchExpand"
        >
          <el-form-item :label="this.$t('awb.awb.agent') + ':'" prop="agent">
            <agent-select
              v-model="where.agent"
              @callback="roleSelectCallback"
            />
          </el-form-item>
        </el-col>
        <el-col :lg="8" :md="12" v-if="searchExpand">
          <el-form-item
            :label="$t('awb.awb.in_storage') + ':'"
            prop="inStorages"
          >
            <el-select
              v-model="where.in_storage"
              :placeholder="this.$t('basics.pleaseChoose')"
              clearable
              class="ele-fluid"
            >
              <el-option :label="$t('awb.awb.in_storages.s1')" :value="1" />
              <el-option :label="$t('awb.awb.in_storages.s2')" :value="2" />
              <el-option :label="$t('awb.awb.in_storages.s3')" :value="3" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col
          :lg="8"
          :md="12"
          v-permission="'awb:nouserSearch'"
          v-if="searchExpand"
        >
          <el-form-item :label="this.$t('awb.awb.port') + ':'" prop="lgg">
            <port-select v-model="where.lgg" @callback="roleSelectCallback" />
          </el-form-item>
        </el-col>
        <el-col :lg="8" :md="12" v-if="searchExpand">
          <el-form-item
            :label="this.$t('awb.awb.col1') + ':'"
            prop="b2b"
          >
            <bussiness-select
              v-model="where.b2b"
              @callback="roleSelectCallback"
            />
          </el-form-item>
        </el-col>

        <el-col :lg="8" :md="12" v-if="searchExpand">
          <el-form-item
            :label="this.$t('awb.noa.sendMail') + ':'"
            v-permission="'awb:nouserSearch'"
            prop="isStock"
          >
            <el-select
              v-model="where.mail_sending_status"
              :placeholder="this.$t('basics.pleaseChoose')"
              clearable
              class="ele-fluid"
            >
              <el-option :label="this.$t('basics.no')" :value="1" />
              <el-option :label="this.$t('basics.yes')" :value="2" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :lg="8" :md="12">
          <div class="ele-form-actions">
            <el-button
              type="primary"
              icon="el-icon-search"
              class="ele-btn-icon"
              @click="search"
            >
              {{ $t('basics.query') }}
            </el-button>
            <el-button @click="reset">{{ $t('basics.reset') }}</el-button>
            <el-link type="primary" :underline="false" @click="toggleExpand">
              <template v-if="searchExpand">
                {{ $t('basics.arrowUp') }}<i class="el-icon-arrow-up"></i>
              </template>
              <template v-else>
                {{ $t('basics.arrowDown') }}<i class="el-icon-arrow-down"></i>
              </template>
            </el-link>
          </div>
        </el-col>
      </el-row>
    </el-form>
  </el-card>
</template>

<script>
  import UserSelect from './user-select.vue';
  import BussinessSelect from '@/components/BussinessSelect';
  import AgentSelect from '@/components/AgentSelect';
  import PortSelect from '@/components/PortSelect';
  // 默认表单数据

  const DEFAULT_WHERE = {
    t1: null,
    agent: null,
    bol_no: '',
    appoint_type: null
  };

  export default {
    name: 'outbound-fba-search',
    components: {
      UserSelect,
      AgentSelect,
      PortSelect,
      BussinessSelect
    },
    data() {
      return {
        // 表单数据
        where: { ...DEFAULT_WHERE },
        // 搜索表单是否展开
        searchExpand: false,
        goodsCategoryList: [],
        category_id: '',
        role_id: this.$store.getters.user.info.role_id
      };
    },
    methods: {
      /* 搜索 */
      search() {
        //解决搜索 也搜索下级的数据
        this.$emit('search', this.where);
      },
      /* 重置搜索 */
      reset() {
        this.where = { ...DEFAULT_WHERE };
        this.search();
      },
      roleSelectCallback() {},
      /* 搜索展开/收起 */
      toggleExpand() {
        this.searchExpand = !this.searchExpand;
        this.$emit('expand-change', this.searchExpand);
      },
      /* 搜索展开/收起 */
      goodsCategoryFilterListClick(e) {
        this.goodsCategoryList = e.data;
      }
    }
  };
</script>
