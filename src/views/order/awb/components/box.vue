<!-- 编辑弹窗 -->
<template>
  <ele-modal
    width="1080px"
    :visible="visible"
    :close-on-click-modal="false"
    custom-class="ele-dialog-form"
    :title="data && data.status == 1 ? $t('awb.awb.col4') : $t('awb.awb.col5')"
    @update:visible="updateVisible"
  >
    <ele-pro-table ref="tablelog" :columns="columns" :datasource="datasource">
    </ele-pro-table>
    <div style="height: 20px; width: 100%"></div>
  </ele-modal>
</template>

<script>
  import { boxes } from '@/api/order/awb';

  const DEFAULT_FORM = {
    id: null,
    status: null,
    address_code: null
  };

  export default {
    name: 'EditTime',
    components: {},
    props: {
      // 弹窗是否打开
      visible: Boolean,
      // 修改回显的数据
      data: Object
    },
    data() {
      return {
        // 表单数据
        form: { ...DEFAULT_FORM },
        columns: [
          {
            columnKey: 'index',
            type: 'index',
            width: 45,
            showOverflowTooltip: true,
            fixed: 'left'
          },
          {
            prop: 'box_no',
            label: this.$t('awb.box.box'),
            showOverflowTooltip: true,
            minWidth: 110
          },
          {
            prop: 'status',
            label: this.$t('basics.status'),
            showOverflowTooltip: true,
            minWidth: 70,
            formatter: (row, column, cellValue) => {
              return [
                this.$t('awb.box.status.s1'),
                this.$t('awb.box.status.s2'),
                this.$t('awb.box.status.s3'),
                this.$t('awb.box.status.s4')
              ][cellValue - 1];
            }
          },
          {
            prop: 'clearance',
            label: this.$t('awb.awb.clearStatus'),
            showOverflowTooltip: true,
            minWidth: 70,
            formatter: (row, column, cellValue) => {
              // return cellValue;
              return this.$t(`basics.statusWords.s${cellValue}`);
            }
          },
          {
            prop: 'stock_in_time',
            label: this.$t('awb.box.stock_in_time'),
            showOverflowTooltip: true,
            minWidth: 110
          }
        ],
        // 提交状态
        loading: false,
        // 是否是修改
        isUpdate: false
      };
    },
    methods: {
      datasource({ page, limit, where, order }) {
        return boxes({
          ...where,
          ...order,
          page: page,
          num: limit,
          id: this.data.id,
          status: this.data.status,
          address_code: this.data.address_code
        });
      },
      reload() {
        this.$nextTick(() => {
          this.$refs.tablelog.reload({
            page: 1,
            id: this.data.id,
            status: this.data.status
          });
        });
      },
      /* 更新visible */
      updateVisible(value) {
        this.$emit('update:visible', value);
      },
      /* 角色选择回调*/
      roleSelectCallback(value) {
        this.form.role_type = value.type;
      }
    },
    watch: {
      async visible(visible) {
        if (this.data) {
          this.reload();
        }
        if (visible) {
          let data = this.data;
          if (this.data) {
            this.$util.assignObject(this.form, {
              ...data
            });
            this.isUpdate = true;
          } else {
            this.isUpdate = false;
          }
        } else {
          // this.$refs['form'].clearValidate();
          this.form = { ...DEFAULT_FORM };
        }
      }
    }
  };
</script>
