<!-- 编辑弹窗 -->
<template>
  <ele-modal
    width="720px"
    :visible="visible"
    :close-on-click-modal="false"
    custom-class="ele-dialog-form"
    :title="$t('awb.awb.export')"
    @update:visible="updateVisible"
  >
    <el-form ref="form" :model="form" :rules="rules" label-width="140px">
      <el-row type="flex">
        <el-col :lg="16" :span="12">
          <el-form-item :label="$t('awb.awb.export_type')+':'" prop="type">
            <el-select v-model="form.type" clearable class="ele-fluid">
              <el-option :label="$t('awb.awb.export_types.export1')" :value="1" />
              <el-option :label="$t('awb.awb.export_types.export1')" :value="2" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div slot="footer">
      <el-button @click="updateVisible(false)">
        {{ $t('basics.cancel') }}
      </el-button>
      <el-button type="primary" :loading="loading" @click="save">
        {{ $t('basics.confirm') }}
      </el-button>
    </div>
  </ele-modal>
</template>

<script>
  import { financeExport, financeExportCost } from '@/api/order/awb';

  const DEFAULT_FORM = {
    id: null,
    date: '',
    type: null
  };

  export default {
    name: 'UserEdit',
    components: {},
    props: {
      visible: Boolean,
      data: Array
    },
    data() {
      return {
        // 表单数据
        form: { ...DEFAULT_FORM },
        // 表单验证规则
        rules: {
          date: [
            {
              required: true,
              message: this.$t('basics.pleaseChoose'),
              trigger: ['blur', 'change']
            }
          ]
        },
        // 提交状态
        loading: false,
        // 是否是修改
        isUpdate: false
      };
    },
    methods: {
      /* 保存编辑 */
      save() {
        this.$refs['form'].validate((valid) => {
          if (!valid) {
            return false;
          }
          this.loading = true;
          let temp = [];
          for (let item of this.data) {
            temp.push(item.bol_no);
          }
          let data = {
            bols: temp
          };
          let apiUrl = this.form.type == 1 ? financeExport : financeExportCost;
          apiUrl(data)
            .then((res) => {
              this.loading = false;
              this.downloadExcel(res.result);
              this.$message.success(this.$t('basics.success'));
              this.updateVisible(false);
              this.$emit('done');
            })
            .catch((e) => {
              this.loading = false;
              this.$message.error(e);
            });
        });
      },
      downloadExcel(url) {
          console.log(url)
        let a = document.createElement('a'), //创建a标签
          e = document.createEvent('MouseEvents'); //创建鼠标事件对象
        e.initEvent('click', false, false); //初始化事件对象
        a.href = url; //设置下载地址
        a.target = '_blank';
        a.download = name; //设置下载文件名
        a.dispatchEvent(e); //给指定的元素，执行事件click事件
      },
      /* 更新visible */
      updateVisible(value) {
        this.$emit('update:visible', value);
      },
      /* 角色选择回调*/
      roleSelectCallback(value) {
        this.form.role_type = value.type;
      }
    },
    watch: {
      async visible(visible) {
        if (visible) {
          this.isUpdate = !!this.data;
        } else {
          this.$refs['form'].clearValidate();
          this.form = { ...DEFAULT_FORM };
        }
      }
    }
  };
</script>
