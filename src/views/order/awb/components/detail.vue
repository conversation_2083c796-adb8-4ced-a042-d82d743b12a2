<!-- 编辑弹窗 -->
<template>
  <ele-modal
    width="1080px"
    :visible="visible"
    :close-on-click-modal="false"
    custom-class="ele-dialog-form"
    :title="$t('basics.detail')"
    @update:visible="updateVisible"
  >
    <el-form ref="form" :model="form" label-width="170px">
      <el-row>
        <el-col :sm="22">
          <el-form-item
            :label="$t('awb.awb.menu.m1')"
            prop="bol_no"
            class="el-form-item-time"
          >
            <div class="steps">
              <div class="items" v-for="(item, index) in timeline" :key="index">
                <div class="bor" v-if="item && item.state">
                  <el-tooltip
                    class="item"
                    effect="dark"
                    :content="item.time"
                    placement="top-start"
                  >
                    <span class="iconfont" style="color: #ee4737">{{
                      timelineImg[index].icoName
                    }}</span>
                  </el-tooltip>
                  <div
                    style="
                      width: 80px;
                      margin-top: 8px;
                      margin-left: -23px;
                      text-align: center;
                    "
                  >
                    {{ timelineImg[index].label }}</div
                  >
                </div>
                <div class="bor" v-else>
                  <span class="iconfont">{{ timelineImg[index].icoName }}</span>
                  <div
                    style="
                      width: 80px;
                      margin-top: 8px;
                      margin-left: -23px;
                      text-align: center;
                    "
                  >
                    {{ timelineImg[index].label }}</div
                  >
                </div>
                <div class="hr"></div>
              </div>
            </div>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <el-divider></el-divider>
    <el-form
      ref="form"
      :model="form"
      label-width="170px"
      style="margin-top: 50px"
    >
      <el-row :gutter="15">
        <el-col :sm="12">
          <el-form-item :label="`${$t('awb.noa.awbno')}:`" prop="bol_no">
            <el-input
              clearable
              :maxlength="50"
              show-word-limit
              v-model="form.bol_no"
              :disabled="true"
            />
          </el-form-item>
        </el-col>
        <el-col :sm="12">
          <el-form-item :label="`${$t('awb.awb.col10')}:`" prop="flight_no">
            <el-input
              clearable
              show-word-limit
              v-model="form.flight_no"
              :disabled="true"
            />
          </el-form-item>
        </el-col>
        <el-col :sm="12">
          <el-form-item :label="`${$t('awb.awb.batch')}:`" prop="expensive">
            <el-input
              clearable
              show-word-limit
              v-model="form.bol_batch"
              :disabled="true"
            />
          </el-form-item>
        </el-col>
        <el-col :sm="12">
          <el-form-item
            :label="`${$t('awb.awb.col6')}(kg):`"
            prop="gross_mass_kg"
          >
            <el-input
              clearable
              show-word-limit
              v-model="form.gross_mass_kg"
              :disabled="true"
            />
          </el-form-item>
        </el-col>
        <el-col :sm="12">
          <el-form-item
            :label="`${$t('awb.awb.t1')}:`"
            prop="t1"
            v-permission="'awb:nouserSearch'"
          >
            <el-input
              clearable
              show-word-limit
              v-model="form.t1"
              :disabled="true"
            />
          </el-form-item>
        </el-col>
        <el-col :sm="12">
          <el-form-item
            :label="`${$t('awb.awb.agent')}:`"
            prop="t1"
            v-permission="'awb:nouserSearch'"
          >
            <el-input
              clearable
              show-word-limit
              v-model="form.agent"
              :disabled="true"
            />
          </el-form-item>
        </el-col>
        <el-col :sm="12">
          <el-form-item :label="`${$t('awb.awb.port')}:`" prop="lgg">
            <el-input
              clearable
              :maxlength="50"
              show-word-limit
              v-model="form.lgg"
              :disabled="true"
            />
          </el-form-item>
        </el-col>
        <el-col :sm="12">
          <el-form-item :label="`${$t('awb.awb.col1')}:`" prop="b2b">
            <el-input
              clearable
              :maxlength="50"
              show-word-limit
              v-model="form.b2b"
              :disabled="true"
            />
          </el-form-item>
        </el-col>
        <el-col :sm="12">
          <el-form-item :label="`${$t('awb.awb.col8')}:`" prop="exchange_flag">
            <el-input
              clearable
              :maxlength="50"
              show-word-limit
              v-model="form.exchange_flag"
              :disabled="true"
            />
          </el-form-item>
        </el-col>
        <el-col :sm="12">
          <el-form-item :label="`${$t('awb.awb.col7')}:`" prop="expensive">
            <el-input
              clearable
              :maxlength="50"
              show-word-limit
              v-model="form.expensive"
              :disabled="true"
            />
          </el-form-item>
        </el-col>
        <el-col :sm="12">
          <el-form-item
            :label="`${$t('awb.awb.col17')}:`"
            prop="plan_start_date"
          >
            <el-input
              clearable
              show-word-limit
              v-model="form.plan_start_date"
              :disabled="true"
            />
          </el-form-item>
        </el-col>
        <el-col :sm="12">
          <el-form-item :label="`${$t('awb.awb.col18')}:`" prop="plan_end_date">
            <el-input
              clearable
              show-word-limit
              v-model="form.plan_end_date"
              :disabled="true"
            />
          </el-form-item>
        </el-col>
        <el-col :sm="12">
          <el-form-item :label="`${$t('awb.awb.col20')}:`" prop="remark">
            <el-input
              clearable
              show-word-limit
              v-model="form.time_line.time2"
              :disabled="true"
            />
          </el-form-item>
        </el-col>
        <el-col :sm="12">
          <el-form-item :label="`${$t('awb.awb.col21')}:`" prop="remark">
            <el-input
              clearable
              show-word-limit
              v-model="form.time_line.time3"
              :disabled="true"
            />
          </el-form-item>
        </el-col>
        <el-col :sm="12">
          <el-form-item
            :label="`${$t('order.awb.totalValue')}:`"
            v-permission="'awb:nouserSearch'"
            prop="total_value"
          >
            <el-input
              clearable
              show-word-limit
              v-model="form.total_value"
              :disabled="true"
            />
          </el-form-item>
        </el-col>
        <el-col :sm="12">
          <el-form-item
            :label="`${$t('car.grossweight')}(kg):`"
            prop="gross_weight"
          >
            <el-input
              clearable
              show-word-limit
              v-model="form.gross_weight"
              :disabled="true"
            />
          </el-form-item>
        </el-col>
        <el-col :sm="12">
          <el-form-item
            :label="`${$t('awb.awb.col3')}:`"
            prop="box_count"
          >
            <el-input
              clearable
              show-word-limit
              v-model="form.box_count"
              :disabled="true"
            />
          </el-form-item>
        </el-col>
        <el-col :sm="12">
          <el-form-item
            :label="`${$t('batch.out.packagenum')}:`"
            prop="count"
          >
            <el-input
              clearable
              show-word-limit
              v-model="form.count"
              :disabled="true"
            />
          </el-form-item>
        </el-col>
        <el-col :sm="12">
          <el-form-item
            :label="`${$t('batch.out.checknum')}:`"
            prop="check_count"
          >
            <el-input
              clearable
              show-word-limit
              v-model="check_count"
              :disabled="true"
            />
          </el-form-item>
        </el-col>
        <el-col :sm="12">
          <el-form-item :label="`${$t('awb.awb.col19')}:`" prop="remark">
            <el-input
              clearable
              show-word-limit
              v-model="form.remark"
              :disabled="true"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <ele-pro-table
      ref="cmr"
      :initLoad="false"
      :columns="columnsCmr"
      :need-page="false"
      :datasource="datasourceCmr"
    >
      <template slot="action" slot-scope="{ row }">
        <el-link
          @click="jump(row)"
          type="primary"
          icon="el-icon-tickets"
          :underline="false"
          v-permission="'awb:nouserSearch'"
          v-if="!hasRole"
        >
          {{ $t('basics.detail') }}
        </el-link>
        <el-link
          type="primary"
          icon="el-icon-tickets"
          :underline="false"
          v-if="row.cmr_path"
          @click="download(row.cmr_path)"
        >
          {{ $t('car.viewVehicleList') }}
        </el-link>

        <el-link
          type="primary"
          icon="el-icon-tickets"
          :underline="false"
          v-if="
            row.voucher_url && row.voucher_url != 'undefined' && row.type != 1
          "
          @click="openUrl(row.voucher_url)"
        >
          {{ $t('car.viewvoucher') }}
        </el-link>
      </template>
    </ele-pro-table>
    <ele-pro-table
      ref="table"
      :initLoad="false"
      :columns="columns"
      :need-page="false"
      :datasource="datasource"
    >
    </ele-pro-table>
    <el-image-viewer
      ref="elImageViewer"
      v-if="showViewer"
      :on-close="closeViewer"
      :url-list="srcList"
      :z-index="3000"
    />
    <div style="width: 100%; height: 20px"></div>
  </ele-modal>
</template>

<script>
  import { info } from '@/api/order/awb';
  import { getBase } from '@/api/list/carlist';
  import ElImageViewer from 'element-ui/packages/image/src/image-viewer';

  const DEFAULT_FORM = {
    id: null,
    bol_no: '',
    flight_time: '',
    flight_no: '',
    gross_mass_kg: '',
    exchange_flag: null,
    lgg: '',
    b2b: '',
    t1: '',
    agent: '',
    plan_start_date: '',
    plan_end_date: '',
    remark: '',
    bol_batch: '',
    time_line: [],
    expensive: null,
    clearance_fee: null,
    attach_fee: null,
    address: [],
    gross_weight: null,
    total_value: null,
    count: '',
    box_count: ''
  };

  export default {
    name: 'EditAwb',
    components: {
      ElImageViewer
    },
    props: {
      // 弹窗是否打开
      visible: Boolean,
      // 修改回显的数据
      data: Object
    },
    data() {
      return {
        // 表单数据
        form: { ...DEFAULT_FORM },
        check_count: 0,
        showViewer: false,
        columns: [
          {
            columnKey: 'index',
            type: 'index',
            width: 45,
            showOverflowTooltip: true,
            fixed: 'left'
          },
          {
            prop: 'name',
            label: this.$t('car.address'), //地址名称
            showOverflowTooltip: true,
            minWidth: 110
          },
          {
            prop: 'num',
            label: this.$t('awb.awb.forecastNumber'), //预报数
            showOverflowTooltip: true,
            minWidth: 110
          },
          {
            prop: 'stock_in',
            label: this.$t('awb.awb.receiptQuantity'), //入库数
            showOverflowTooltip: true,
            minWidth: 110
          },
          {
            prop: 'stock_out',
            label: this.$t('awb.awb.deliveryQuantity'), //出库数
            showOverflowTooltip: true,
            minWidth: 110
          }
        ],
        columnsCmr: [
          {
            columnKey: 'index',
            type: 'index',
            width: 45,
            showOverflowTooltip: true,
            fixed: 'left'
          },
          {
            prop: 'cmr_no',
            label: 'CMR',
            showOverflowTooltip: true,
            minWidth: 110
          },
          {
            prop: 'addr_name',
            label: this.$t('car.address'),
            showOverflowTooltip: true,
            minWidth: 110
          },
          {
            prop: 'type',
            label: this.$t('car.type'), //车单类型,
            showOverflowTooltip: true,
            minWidth: 110,
            formatter: (row, column, cellValue) => {
              return {
                1: this.$t('car.in'),
                2: this.$t('car.out'),
                3: this.$t('car.air')
              }[cellValue];
            }
          },
          {
            columnKey: 'action',
            label: this.$t('basics.action'),
            width: 220,
            align: 'center',
            resizable: false,
            slot: 'action',
            hideInSetting: true,
            fixed: 'right'
          }
        ],
        datasource: [],
        datasourceCmr: [],
        // 提交状态
        loading: false,
        // 是否是修改
        isUpdate: false,
        timeline: [],
        //提单审核通过、航班起飞、航班落地、清关完成、入仓、出仓、到达目的仓库
        timelineImg: [
          //提单审核通过
          {
            label: this.$t('awb.awb.times.time1'),
            icoName: '\ue613'
          },
          //航班起飞
         // {
           // label: this.$t('awb.awb.times.time2'),
            //icoName: '\ue6b1'
          //},
          //航班落地
          {
            label: this.$t('awb.awb.times.time3'),
            icoName: '\ue6b2'
          },
          //清关完成
          {
            label: this.$t('awb.awb.times.time4'),
            icoName: '\ue64f'
          },
          //入仓
          {
            label: this.$t('awb.awb.times.time9'),
            icoName: '\ue606'
          },
          //出仓
          {
            label: this.$t('awb.awb.times.time10'),
            icoName: '\ue602'
          },
          //到达目的仓库
          {
            label: this.$t('awb.awb.times.time11'),
            icoName: '\ue89d'
          }
        ]
      };
    },
    methods: {
      openUrl(url) {
        window.open(url);
      },
      closeViewer() {
        this.showViewer = false;
      },
      showImage(path) {
        this.srcList = path;
        this.showViewer = true;
        setTimeout(() => {
          this.$refs.elImageViewer.handleActions('clocelise');
        }, 20);
      },
      download(cmr_path) {
        getBase({ file_path: cmr_path })
          .then(({ result }) => {
            const { img_base_64 } = result;
            this.showImage([img_base_64]);
          })
          .catch((res) => {
            this.$message.error(res);
          });
      },
      jump(row) {
        this.$router.push('/carlist?cmr=' + row.cmr_no);
      },
      /* 更新visible */
      updateVisible(value) {
        this.$emit('update:visible', value);
      },
      /* 角色选择回调*/
      roleSelectCallback(value) {
        this.form.role_type = value.type;
      }
    },
    watch: {
      async visible(visible) {
        if (visible) {
          //报错
          let a = await info(this.data);
          this.hasRole = a.hasRole;
          // console.log(a.result.address);
          // // this.form = a.result
          // // let data = this.data;
          if (this.data) {
            this.$util.assignObject(this.form, {
              ...a.result.bol
            });
            this.datasource = a.result.address;
            this.datasourceCmr = a.result.cmr;
            this.check_count = a.result.check_count;
            console.log(this.form);
            this.isUpdate = true;
            let { timeline } = a.result;
            this.timeline = timeline.map((i, k) => {
              if (k === 2 || k === 3) {
                i.state = i.state && timeline[0].state;
              } else if (k !== 0) {
                i.state = i.state && timeline[k - 1].state;
              }
              return i;
            });
          } else {
            this.isUpdate = false;
          }
        } else {
          this.$refs['form'].clearValidate();
          this.form = { ...DEFAULT_FORM };
        }
      }
    }
  };
</script>
<style lang="scss">
  .steps {
    width: 100%;
    height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;

    .items {
      display: flex;
      align-items: center;
      cursor: pointer;
      .bor {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        padding: 13px;
        box-sizing: border-box;
        flex-shrink: 0;
        border: 1px solid #cecece;

        span {
          width: 100%;
          display: block;
          font-size: 30px;
          line-height: 30px;
          text-align: center;
        }
      }

      .active {
        background: #1296db;
        border: 1px solid #1296db;
      }

      .hr {
        width: 40px;
        height: 2px;
        background: #cecece;
        margin: 0 10px;
      }

      .hractive {
        background: #1296db;
      }
    }

    .items:last-child {
      .hr {
        display: none;
      }
    }
  }

  .el-form-item-time {
    .el-form-item__label {
      line-height: 60px;
    }
  }
</style>
