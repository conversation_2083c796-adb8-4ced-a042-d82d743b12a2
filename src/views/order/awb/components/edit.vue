<!-- 编辑弹窗 -->
<template>
  <ele-modal
    width="1000px"
    :visible="visible"
    :close-on-click-modal="false"
    custom-class="ele-dialog-form"
    :title="$t('basics.edit')"
    @update:visible="updateVisible"
  >
    <el-form ref="form" :model="form" :rules="rules" label-width="170px">
      <!--          备注-->
      <el-row :gutter="15">
        <!-- 提单号 -->
        <el-col :sm="12">
          <el-form-item :label="$t('awb.noa.awbno') + ':'" prop="bol_no">
            <el-input
              clearable
              :maxlength="50"
              show-word-limit
              v-model="form.bol_no"
              :disabled="true"
            />
          </el-form-item>
        </el-col>
        <!-- 航班号 -->
        <el-col :sm="12">
          <el-form-item :label="$t('awb.awb.col10') + ':'" prop="flight_no">
            <el-input
              clearable
              :maxlength="50"
              show-word-limit
              v-model="form.flight_no"
              :placeholder="$t('basics.pleaseInput')"
            />
          </el-form-item>
        </el-col>
        <!-- T1代理-->
        <el-col :sm="12" v-show="!$hasRole('sonOwner')">
          <el-form-item :label="$t('awb.awb.t1') + ':'" prop="t1">
            <agent-select v-model="form.t1" />
          </el-form-item>
        </el-col>
        <!-- 清关代理 -->
        <el-col :sm="12" v-show="!$hasRole('sonOwner')">
          <el-form-item :label="$t('awb.awb.agent') + ':'" prop="agent">
            <agent-select v-model="form.agent" />
          </el-form-item>
        </el-col>
        <!-- 业务类型-->
        <el-col :sm="12">
          <el-form-item :label="$t('awb.awb.col1') + ':'" prop="b2b">
            <bussiness-select v-model="form.b2b" />
          </el-form-item>
        </el-col>
        <!-- 航班港口 -->
        <el-col :sm="12">
          <el-form-item :label="$t('awb.awb.port') + ':'" prop="lgg">
            <port-select v-model="form.lgg" />
          </el-form-item>
        </el-col>
        <!-- 预计起飞时间-->
        <el-col :sm="12">
          <el-form-item
            :label="$t('awb.awb.col17') + ':'"
            prop="plan_start_date"
          >
            <el-date-picker
              v-model="form.plan_start_date"
              type="datetime"
              value-format="yyyy-MM-dd HH:mm:ss"
              :placeholder="$t('basics.placeholder.time1')"
              class="ele-fluid"
            >
            </el-date-picker>
          </el-form-item>
        </el-col>
        <!-- 预计落地时间-->
        <el-col :sm="12">
          <el-form-item :label="$t('awb.awb.col18') + ':'" prop="plan_end_date">
            <el-date-picker
              v-model="form.plan_end_date"
              type="datetime"
              value-format="yyyy-MM-dd HH:mm:ss"
              :placeholder="$t('basics.placeholder.time1')"
              class="ele-fluid"
            >
            </el-date-picker>
          </el-form-item>
        </el-col>
        <!-- 毛重-->
        <el-col :sm="12">
          <el-form-item :label="this.$t('car.grossweight') + ':'" prop="remark">
            <el-input
              clearable
              :maxlength="50"
              show-word-limit
              v-model="form.gross_weight"
              :placeholder="$t('basics.pleaseInput')"
            />
          </el-form-item>
        </el-col>
        <!--计费重量-->
        <el-col :sm="12">
          <el-form-item
            :label="$t('awb.awb.col6') + '(kg):'"
            prop="gross_mass_kg"
          >
            <el-input
              clearable
              :maxlength="50"
              show-word-limit
              v-model="form.gross_mass_kg"
              :placeholder="$t('basics.pleaseInput')"
            />
          </el-form-item>
        </el-col>
        <!-- 换标数-->
        <el-col :sm="12">
          <el-form-item :label="$t('awb.awb.col8')" prop="exchange_flag">
            <el-input
              clearable
              :maxlength="50"
              show-word-limit
              v-model="form.exchange_flag"
              :placeholder="$t('basics.pleaseInput')"
            />
          </el-form-item>
        </el-col>
        <!-- 备注-->
        <el-col :sm="12" v-show="!$hasRole('sonOwner')">
          <el-form-item
            :label="this.$t('batch.car.remark') + ':'"
            prop="remark"
          >
            <el-input
              clearable
              :maxlength="50"
              show-word-limit
              v-model="form.remark"
              :placeholder="$t('basics.pleaseInput')"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div slot="footer">
      <el-button @click="updateVisible(false)">
        {{ $t('basics.cancel') }}
      </el-button>
      <el-button type="primary" :loading="loading" @click="save">
        {{ $t('basics.save') }}
      </el-button>
    </div>
  </ele-modal>
</template>

<script>
  import { edit } from '@/api/order/awb';
  import BussinessSelect from '@/components/BussinessSelect';
  import AgentSelect from '@/components/AgentSelect';
  import PortSelect from '@/components/PortSelect';

  const DEFAULT_FORM = {
    id: null,
    bol_no: '',
    flight_time: '',
    flight_no: '',
    gross_mass_kg: '',
    exchange_flag: null,
    lgg: '',
    b2b: '',
    t1: '',
    agent: '',
    remark: '',
    plan_start_date: '',
    plan_end_date: '',
    expensive: null,
    clearance_fee: null,
    attach_fee: null,
    gross_weight: ''
  };

  export default {
    name: 'EditAwb',
    components: {
      AgentSelect,
      PortSelect,
      BussinessSelect
    },
    props: {
      // 弹窗是否打开
      visible: Boolean,
      // 修改回显的数据
      data: Object
    },
    data() {
      return {
        // 表单数据
        form: { ...DEFAULT_FORM },
        // 表单验证规则
        rules: {
          agent: [
            {
              required: true,
              message: this.$t('basics.pleaseChoose'),
              trigger: ['blur', 'change']
            }
          ],
          b2b: [
            {
              required: true,
              message: this.$t('basics.pleaseChoose'),
              trigger: ['blur', 'change']
            }
          ],
          lgg: [
            {
              required: true,
              message: this.$t('basics.pleaseChoose'),
              trigger: ['blur', 'change']
            }
          ]
        },
        // 提交状态
        loading: false,
        // 是否是修改
        isUpdate: false
      };
    },
    methods: {
      /* 保存编辑 */
      save() {
        if (!this.$hasRole('sonOwner')) {
          this.$refs['form'].validate((valid) => {
            if (!valid) {
              return false;
            }
            this.loading = true;
            const data = {
              ...this.form
            };
            this.editQ(data);
          });
        } else {
          this.loading = true;
          const data = {
            ...this.form
          };
          this.editQ(data);
        }
      },
      editQ(data) {
        edit(data)
          .then(() => {
            this.loading = false;
            this.$message.success(this.$t('basics.success'));
            this.updateVisible(false);
            this.$emit('done');
          })
          .catch((e) => {
            this.loading = false;
            this.$message.error(e);
          });
      },
      /* 更新visible */
      updateVisible(value) {
        this.$emit('update:visible', value);
      },
      /* 角色选择回调*/
      roleSelectCallback(value) {
        this.form.role_type = value.type;
      }
    },
    watch: {
      async visible(visible) {
        console.log(this.data);
        if (visible) {
          let data = this.data;
          if (this.data) {
            this.$util.assignObject(this.form, {
              ...data
            });
            this.isUpdate = true;
          } else {
            this.isUpdate = false;
          }
        } else {
          this.$refs['form'].clearValidate();
          this.form = { ...DEFAULT_FORM };
        }
      }
    }
  };
</script>
