<template>
  <div class="ele-body">
    <el-card shadow="never">
      <!-- 搜索表单 -->
      <outbound-fba-search @search="outboundFbaSearch" />
      <!-- 数据表格 -->
      <ele-pro-table
        ref="table"
        row-key="id"
        :columns="columns"
        :datasource="datasource"
        @columns-change="columnsChange"
        :selection.sync="selection"
        @selection-change="handleSelectionChange"
      >
        <!-- 表头工具栏 -->
        <template slot="toolbar">
          <el-button
            size="small"
            icon="el-icon-upload2"
            class="ele-btn-icon"
            @click="openImport"
            v-permission="'awb:create'"
          >
            {{ $t('basics.import') }}
          </el-button>
          <el-button
            size="small"
            icon="el-icon-upload2"
            class="ele-btn-icon"
            @click="showClearUpdate()"
            v-permission="'package:setting'"
          >
            {{ $t('awb.awb.title1') }}
          </el-button>
          <el-button
            size="small"
            icon="el-icon-upload2"
            class="ele-btn-icon"
            @click="showFlightUpdate()"
            v-permission="'awb:uploadFlight'"
          >
            {{ $t('awb.awb.title3') }}
          </el-button>
          <el-button
            size="small"
            icon="el-icon-download"
            class="ele-btn-icon"
            @click="showExport()"
            v-permission="'awb:financeExport'"
          >
            {{ $t('awb.awb.export') }}
          </el-button>
          <el-popover
            class="ele-btn-icon"
            placement="bottom"
            width="200"
            trigger="click"
          >
            <el-table
              :data="exportColumns"
              height="500"
              @selection-change="handleSelectionExportChange"
            >
              <el-table-column type="selection" width="25"></el-table-column>
              <el-table-column property="label">
                <template slot="header">
                  <div
                    class="exportTitle"
                    style="
                      width: 100%;
                      display: flex;
                      justify-content: flex-end;
                    "
                  >
                    <el-button
                      size="small"
                      icon="el-icon-download"
                      class="ele-btn-icon"
                      @click="exportXls()"
                      type="primary"
                      v-permission="'user:getBolExport'"
                    >
                      {{ $t('basics.export') }}
                    </el-button>
                  </div>
                </template>
              </el-table-column>
            </el-table>
            <el-button
              size="small"
              icon="el-icon-download"
              class="ele-btn-icon"
              v-permission="'user:getBolExport'"
              slot="reference"
              >{{ $t('basics.export') }}
            </el-button>
          </el-popover>
          <!-- <el-button
            size="small"
            icon="el-icon-download"
            class="ele-btn-icon"
            @click="exportXls()"
            v-permission="'user:getBolExport'"
          >
            {{ $t('basics.export') }}
          </el-button> -->
          <el-button
            :loading="dl_pdf_load"
            size="small"
            icon="el-icon-download"
            class="ele-btn-icon"
            @click="getPdfZip()"
            v-permission="'bols:downloadPdfZip'"
            >{{ $t('order.awb.batchDownloadPDF') }}
          </el-button>
        </template>
        <template slot="bol_no" slot-scope="{ row }">
          <div
            @click="detailawb(row)"
            style="cursor: pointer; color: var(--color-primary)"
          >
            {{ row.bol_no }}
          </div>
        </template>
        <template slot="check_box_count" slot-scope="{ row }">
          {{ row.box_count }}
          <span
            v-if="row.check_box_count > 0"
            class="item"
            effect="dark"
            :title="$t('order.awb.check_box_tips') + row.check_box_count"
            placement="top-start"
            style="cursor: pointer; color: var(--color-primary)"
          >
            <i @click="jumpBoxCheck(row)" class="el-icon-_warning"></i>
          </span>
        </template>

        <template slot="check_count" slot-scope="{ row }">
          {{ row.count }}
          <span
            v-if="row.check_count > 0"
            class="item"
            effect="dark"
            :title="$t('order.awb.check_tips') + row.check_count"
            placement="top-start"
            style="cursor: pointer; color: var(--color-primary)"
          >
            <i @click="jumpCheck(row)" class="el-icon-_warning"></i>
          </span>
        </template>
        <template slot="prediction_count" slot-scope="{ row }">
          <div
            @click="showboxes(row, 1)"
            style="cursor: pointer; color: var(--color-primary)"
          >
            {{ row.prediction_count }}
          </div>
        </template>
        <template slot="stock" slot-scope="{ row }">
          <div
            @click="showboxes(row, 3)"
            style="cursor: pointer; color: var(--color-primary)"
          >
            {{ row.stock }}
          </div>
        </template>
        <!-- 操作列 -->
        <template slot="action" slot-scope="{ row }">
          <el-link
            @click="detailawb(row)"
            type="primary"
            icon="el-icon-tickets"
            :underline="false"
          >
            {{ $t('basics.detail') }}
          </el-link>
          <el-link
            v-permission="'awb:finance'"
            @click="getFinance(row)"
            type="primary"
            icon="el-icon-tickets"
            :underline="false"
          >
            {{ $t('basics.financeSetting') }}
          </el-link>
          <el-link
            v-permission="'bols:uploadPdf'"
            v-show="row.user_confirm == 1"
            @click="uploadPdf(row)"
            type="primary"
            icon="el-icon-upload2"
            :underline="false"
          >
            {{ $t('car.uploadvoucher') }}
          </el-link>

          <el-link
            @click="editawb(row)"
            v-show="
              row.finance_confirm == 1 &&
              (!$hasRole('sonOwner') || row.user_confirm == 1)
            "
            type="primary"
            icon="el-icon-edit"
            :underline="false"
          >
            {{ $t('basics.edit') }}
          </el-link>
          <el-popconfirm
            v-permission="'awb:check'"
            v-show="row.finance_confirm == 1 && row.user_confirm == 2"
            class="ele-action"
            :title="$t('awb.awb.tips.t9')"
            @confirm="checkAwb(row)"
          >
            <el-link
              type="primary"
              icon="el-icon-s-check"
              slot="reference"
              :underline="false"
            >
              {{ $t('awb.awb.menu.m6') }}
            </el-link>
          </el-popconfirm>
          <el-popconfirm
            v-permission="'awb:usercheck'"
            v-show="row.user_confirm == 1"
            class="ele-action"
            :title="$t('awb.awb.tips.t8')"
            @confirm="checkUserAwb(row)"
          >
            <el-link
              type="primary"
              icon="el-icon-s-check"
              slot="reference"
              :underline="false"
            >
              {{ $t('awb.awb.menu.m9') }}
            </el-link>
          </el-popconfirm>
          <!-- notStartedCli -->
          <el-link
            @click="startCleared(row)"
            v-permission="'bols:notStarted'"
            v-show="row.clearance == 1 && row.user_confirm == 2"
            type="primary"
            icon="el-icon-s-check"
            :underline="false"
          >
            {{ $t('awb.awb.menu.m10') }}
          </el-link>
          <el-link
            @click="cleared(row)"
            v-show="row.clearance == 2 && row.user_confirm == 2"
            type="primary"
            icon="el-icon-s-check"
            v-permission="'awb:clearupdate'"
            :underline="false"
          >
            {{ $t('awb.awb.menu.m8') }}
          </el-link>
          <!-- <el-popconfirm
            v-show="row.clearance == '1'"
            v-permission="'awb:clearupdate'"
            class="ele-action"
            title="确定要改变清关状态？"
            @confirm="cleared(row)"
          >
            <el-link type="primary" icon="el-icon-s-check" slot="reference" :underline="false">
              清关完成
            </el-link>
          </el-popconfirm>
          <el-popconfirm
            v-permission="'bols:declaration'"
            v-show="row.user_confirm == 2 && row.clearance_status == 1 && row.clearance < 3"
            class="ele-action"
            :title="$t('order.awb.declarationLink')"
            @confirm="submitDeclaration(row)"
          >
            <el-link
              type="primary" icon="el-icon-message"
              slot="reference" :underline="false"
            >{{ $t('order.awb.declarationLink') }}</el-link>
          </el-popconfirm>-->
          <el-link
            @click="postClear(row)"
            v-permission="'bols:postClear'"
            v-show="
              row.user_confirm == 2 &&
              row.clearance_status == 1 &&
              row.manifest_lowValue == 1 &&
              row.clearance < 3
            "
            type="primary"
            icon="el-icon-message"
            :underline="false"
          >
            {{ $t('order.awb.declarationLink') }}
          </el-link>
          <el-popconfirm
            v-show="
              row.status == '1' &&
              (!$hasRole('sonOwner') || row.user_confirm == 1)
            "
            class="ele-action"
            :title="$t('awb.awb.tips.t10')"
            @confirm="deleteAwb(row)"
          >
            <el-link
              type="primary"
              icon="el-icon-delete"
              slot="reference"
              :underline="false"
            >
              {{ $t('awb.awb.menu.m7') }}
            </el-link>
          </el-popconfirm>
          <el-dropdown @command="(command) => onDropClick(command, row)">
            <el-link type="primary" :underline="false">
              {{ $t('basics.more') }}<i class="el-icon-arrow-down"></i>
            </el-link>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item
                command="gettimeline"
                icon="el-icon-s-operation"
                v-permission="'awb:gettimeline'"
                >{{ $t('awb.awb.menu.m1') }}
              </el-dropdown-item>
              <el-dropdown-item
                command="timeline"
                icon="el-icon-edit"
                v-permission="'awb:timeline'"
                v-show="row.user_confirm == 2"
                >{{ $t('awb.awb.menu.m2') }}
              </el-dropdown-item>
              <el-dropdown-item
                command="getlog"
                icon="el-icon-s-order"
                v-permission="'awb:getlog'"
                >{{ $t('awb.awb.menu.m3') }}
              </el-dropdown-item>
              <el-dropdown-item
                command="dlFest"
                v-show="row.user_confirm == 2"
                icon="el-icon-download"
                >{{ $t('awb.awb.menu.m4') }}
              </el-dropdown-item>
              <!-- <el-dropdown-item command="dlPDF" icon="el-icon-download">{{
                 $t('awb.awb.menu.m5')
               }}</el-dropdown-item> -->
              <el-dropdown-item
                command="swPDF"
                icon="el-icon-picture-outline"
                v-show="row.bol_pdf_path"
                >{{ $t('awb.awb.menu.m55') }}
              </el-dropdown-item>
              <el-dropdown-item
                command="sendMail"
                icon="el-icon-message"
                v-permission="'bols:sendMail'"
                v-show="row.mail_sending_status == 1"
                >{{ $t('awb.noa.sendMail') }}
              </el-dropdown-item>
              <!--<el-dropdown-item
                command="declaration"
                icon="el-icon-message"
                v-permission="'bols:declaration'"
                v-show="row.user_confirm == 2 && row.clearance_status == 1"
              >{{ $t("order.awb.declarationLink") }}</el-dropdown-item>-->
              <!-- <el-dropdown-item command="deleteAwb" icon="el-icon-delete" v-show="row.status == '1'">删除提单</el-dropdown-item> -->
            </el-dropdown-menu>
          </el-dropdown>
        </template>
      </ele-pro-table>
    </el-card>
    <!-- 导入弹窗 -->
    <update-file :visible.sync="showImport" @done="reload" />
    <clear-update :visible.sync="showClear" @done="reload" />
    <flight-update :visible.sync="showFlight" @done="reload" />
    <show-time :visible.sync="showTime" :data="current" @done="reload" />
    <edit-time :visible.sync="showTimeEdit" :data="current" @done="reload" />
    <edit-awb :visible.sync="showEditAwb" :data="current" @done="reload" />
    <detail-awb :visible.sync="showDetailAwb" :data="current" />
    <finance-edit :visible.sync="showFinance" :data="current" @done="reload" />
    <update-pdf :visible.sync="showUploadPdf" :data="current" @done="reload" />
    <setting-clear
      :visible.sync="showSettingClear"
      :data="current"
      @done="reload"
    />
    <setting-start-clear
      :visible.sync="showSettingStartClear"
      :data="current"
      @done="reload"
    />
    <setting-post-clear
      :visible.sync="showSettingPostClear"
      :data="current"
      @done="reload"
    />
    <show-logs :visible.sync="displayshowlogs" :data="current" />
    <show-boxes :visible.sync="displayshowboxes" :data="current" />
    <download-excel
      :visible.sync="showEditPack"
      :data="arraycurrent"
      @done="reload"
    />
    <show :visible.sync="displayshowpdf" :data="current" />
    <send-mail :visible.sync="sendMailShow" :data="current" @done="reload" />
  </div>
</template>

<script>
  import {
    cancel,
    lists,
    financeConfirm,
    userConfirm,
    notStarted,
    downloadManifest,
    getAdminBolExport,
    downloadPdfZip,
    getDeclaration,
    postClear
  } from '@/api/order/awb';
  import { setTableCols } from '@/api/layout/index';
  import UpdateFile from './components/update';
  import UpdatePdf from './components/upload';
  import ClearUpdate from './components/clearUpdate';
  import FlightUpdate from './components/flightUpdate';
  import ShowTime from './components/showTime';
  import Show from './components/show';
  import EditTime from './components/timeEdit';
  import ShowLogs from './components/logs';
  import ShowBoxes from './components/box';
  import EditAwb from './components/edit';
  import DetailAwb from './components/detail';
  import FinanceEdit from './components/finance-edit';
  import SettingClear from './components/settingClear';
  import SettingStartClear from './components/settingStartClear';
  import settingPostClear from './components/settingPostClear';
  import OutboundFbaSearch from './components/outbound-fba-search';
  import DownloadExcel from './components/download-excel';
  import SendMail from './components/sendMail';
  import store from '@/store';
  import { hasPermission } from '@/utils/permission';
  import { utils, writeFile } from 'xlsx';

  export default {
    name: 'awb-index',
    components: {
      OutboundFbaSearch,
      ShowTime,
      Show,
      EditTime,
      UpdateFile,
      UpdatePdf,
      ClearUpdate,
      FlightUpdate,
      ShowLogs,
      ShowBoxes,
      EditAwb,
      DetailAwb,
      FinanceEdit,
      SettingClear,
      SettingStartClear,
      settingPostClear,
      DownloadExcel,
      SendMail
    },
    created() {
      this.columns = this.getColumns();
      let roleUser = this.$hasRole('sonOwner');
      console.log(this.$hasRole('sonOwner'), 'sonOwner');
      this.exportColumns = (
        roleUser ? this.baseUserColumns : this.expAdminColumns
      )
        .map((i) => {
          if (i.label && i?.columnKey !== 'action') {
            return i;
          }
        })
        .filter((i) => i);
    },
    data() {
      return {
        // 表格列配置
        exportColumns: [],
        columns: [],
        expAdminColumns: [
          {
            prop: 'bol_batch',
            label: this.$t('order.awbExp.col1'),
            show: false
          },
          {
            prop: 'created_at',
            label: this.$t('order.awbExp.col2'),
            show: false
          },
          {
            prop: 'bol_no',
            label: this.$t('order.awbExp.col3'),
            show: false
          },
          {
            prop: 'box_count',
            label: this.$t('order.awbExp.col4'),
            show: false
          },
          {
            prop: 'count',
            label: this.$t('order.awbExp.col5'),
            show: false
          },
          {
            prop: 'gross_weight',
            label: this.$t('order.awbExp.col6'),
            show: false
          },
          {
            prop: 'gross_mass_kg',
            label: this.$t('order.awbExp.col7'),
            show: false
          },
          {
            prop: 't1',
            label: this.$t('order.awbExp.col8'),
            show: false
          },
          {
            prop: 'agent',
            label: this.$t('order.awbExp.col9'),
            show: false
          },
          {
            prop: 'flight_no',
            label: this.$t('order.awbExp.col10'),
            show: false
          },
          {
            prop: 'lgg',
            label: this.$t('order.awbExp.col11'),
            show: false
          },
          {
            prop: 'add_str',
            label: this.$t('order.awbExp.col111'),
            show: false
          },
          {
            prop: 'b2b',
            label: this.$t('order.awbExp.col12'),
            show: false
          },
          {
            prop: 'plan_start_date',
            label: this.$t('order.awbExp.col13'),
            show: false
          },
          {
            prop: 'plan_end_date',
            label: this.$t('order.awbExp.col14'),
            show: false
          },
          {
            prop: 'time2',
            label: this.$t('order.awbExp.col15'),
            show: false
          },
          {
            prop: 'time_line.time3',
            label: this.$t('order.awbExp.col16'),
            show: false
          },
          {
            prop: 'time4',
            label: this.$t('order.awbExp.col17'),
            show: false
          },
          {
            prop: 'noa_time',
            label: this.$t('order.awbExp.col18'),
            show: false
          },
          {
            prop: 'in_b_time',
            label: this.$t('order.awbExp.col19'),
            show: false
          },
          {
            prop: 'in_e_time',
            label: this.$t('order.awbExp.col20'),
            show: false
          },
          {
            prop: 'in_box_count',
            label: this.$t('order.awbExp.col21'),
            show: false
          },
          {
            prop: 'prediction_count',
            label: this.$t('order.awbExp.col22'),
            show: false
          },
          {
            prop: 'stock',
            label: this.$t('order.awbExp.col23'),
            show: false
          },
          {
            prop: 'in_trag_count',
            label: this.$t('order.awbExp.col24'),
            show: false
          },
          {
            prop: 'out_b_time',
            label: this.$t('order.awbExp.col25'),
            show: false
          },
          {
            prop: 'out_box_count',
            label: this.$t('order.awbExp.col26'),
            show: false
          },
          {
            prop: 'out_e_time',
            label: this.$t('order.awbExp.col27'),
            show: false
          },
          {
            prop: 'check_count',
            label: this.$t('order.awbExp.col271'),
            show: false
          },
          {
            prop: 'expensive',
            label: this.$t('order.awbExp.col28'),
            show: false
          },
          {
            prop: 'status',
            label: this.$t('order.awbExp.col29'),
            show: false
          },
          {
            prop: 'remark',
            label: this.$t('order.awbExp.col291'),
            show: false
          },
          {
            prop: 'clearance',
            label: this.$t('order.awbExp.col30'),
            show: false
          },
          {
            prop: 'finance_confirm',
            label: this.$t('order.awbExp.col31'),
            show: false
          },
          {
            prop: 'user_id',
            label: this.$t('order.awbExp.col32'),
            show: false
          },
          {
            prop: 'created_at',
            label: this.$t('order.awbExp.col33'),
            show: false
          },
          {
            prop: 'freight_depot',
            label: this.$t('order.awbExp.freight_depot'),
            show: false
          },
          {
            prop: 'total_value',
            label: this.$t('order.awb.totalValue'),
            show: false
          },
          {
            prop: 'ist',
            label: this.$t('order.awb.ist_number'),
            show: false
          },
          {
            prop: 't1Arr',
            label: this.$t('order.awb.t1_number'),
            show: false
          },
          {
            prop: 'consignee_country',
            label: this.$t('order.awb.consignee_country'),
            show: false
          },
          {
            prop: 'max_stock_in_time',
            label: this.$t('order.awb.box_in_max_time'),
            show: false
          },
          {
            prop: 'max_stock_out_time',
            label: this.$t('order.awb.box_out_max_time'),
            show: false
          }
        ],
        baseColumns: [
          {
            width: 45,
            type: 'selection',
            columnKey: 'selection',
            align: 'center'
          },
          {
            prop: 'bol_no',
            label: this.$t('awb.noa.awbno'),
            showOverflowTooltip: true,
            minWidth: 230,
            show: false,
            slot: 'bol_no'
          },
          {
            prop: 'bol_batch',
            label: this.$t('awb.awb.batch'),
            showOverflowTooltip: true,
            minWidth: 100,
            show: false
          },
          {
            prop: 'flight_no',
            label: this.$t('awb.awb.col10'),
            showOverflowTooltip: true,
            minWidth: 100,
            show: false
          },
          {
            prop: 'lgg',
            label: this.$t('awb.awb.port'),
            showOverflowTooltip: true,
            minWidth: 80,
            show: false
          },
          {
            prop: 'box_count',
            label: this.$t('awb.awb.col3'),
            showOverflowTooltip: true,
            minWidth: 80,
            show: false,
            slot: 'check_box_count'
          },
          {
            prop: 'count',
            label: this.$t('batch.out.packagenum'),
            showOverflowTooltip: true,
            minWidth: 80,
            show: false,
            slot: 'check_count'
          },
          {
            prop: 'prediction_count',
            label: this.$t('awb.awb.col4'),
            showOverflowTooltip: true,
            minWidth: 80,
            show: false,
            slot: 'prediction_count'
          },
          {
            prop: 'stock',
            label: this.$t('awb.awb.col5'),
            showOverflowTooltip: true,
            minWidth: 80,
            show: false,
            slot: 'stock'
          },
          {
            prop: 'address_count',
            label: this.$t('awb.awb.col2'),
            showOverflowTooltip: true,
            minWidth: 80,
            show: false
          },
          {
            prop: 't1',
            label: this.$t('awb.awb.t1'),
            showOverflowTooltip: true,
            minWidth: 80,
            show: false
          },
          {
            prop: 'agent',
            label: this.$t('awb.awb.agent'),
            showOverflowTooltip: true,
            minWidth: 80,
            show: false
          },
          {
            prop: 'b2b',
            label: this.$t('awb.awb.col1'),
            showOverflowTooltip: true,
            minWidth: 80,
            show: false
          },
          {
            prop: 'gross_mass_kg',
            label: this.$t('awb.awb.col6') + '(kg)',
            showOverflowTooltip: true,
            minWidth: 120,
            show: false
          },
          {
            prop: 'expensive',
            label: this.$t('awb.awb.col7'),
            showOverflowTooltip: true,
            minWidth: 110,
            show: false
          },
          // {
          //   prop: 'exchange_flag',
          //   label: this.$t('awb.awb.col8'),
          //   showOverflowTooltip: true,
          //   minWidth: 80,
          //   show: false
          // },
          // {
          //   prop: 'total_value',
          //   label: this.$t('order.awb.totalValue'),
          //   showOverflowTooltip: true,
          //   minWidth: 80,
          //   show: false
          // },
          {
            prop: 'gross_weight',
            label: this.$t('order.awb.grossWeight') + '(kg)',
            showOverflowTooltip: true,
            minWidth: 150,
            show: false
          },
          {
            prop: 'status',
            label: this.$t('basics.status'),
            showOverflowTooltip: true,
            minWidth: 110,
            formatter: (row, column, cellValue) => {
              // return cellValue;
              return this.$t(`awb.awb.status.s${cellValue}`);
            },
            show: false
          },
          {
            prop: 'clearance',
            label: this.$t('awb.awb.clearStatus'),
            showOverflowTooltip: true,
            minWidth: 110,
            show: false,
            formatter: (row, column, cellValue) => {
              // return cellValue;
              return this.$t(`basics.statusWords.s${cellValue}`);
            }
          },
          {
            prop: 'finance_confirm',
            label: this.$t('car.confirmFinance'),
            showOverflowTooltip: true,
            minWidth: 80,
            show: false,
            formatter: (row, column, cellValue) => {
              // return cellValue;
              return { 1: this.$t(`basics.no`), 2: this.$t(`basics.yes`) }[
                cellValue
              ];
            }
          },
          {
            prop: 'user_id',
            label: this.$t('awb.awb.col9'),
            showOverflowTooltip: true,
            minWidth: 110,
            show: false
          },
          {
            prop: 'created_at',
            label: this.$t('basics.createTime'),
            showOverflowTooltip: true,
            minWidth: 110,
            show: false
          },
          {
            prop: 'plan_end_date',
            label: this.$t('awb.awb.col18'),
            showOverflowTooltip: true,
            minWidth: 110,
            show: false
          },
          {
            prop: 'time_line.time3',
            label: this.$t('awb.awb.col21'),
            showOverflowTooltip: true,
            minWidth: 110,
            show: false
          },
          {
            prop: 'time_line.time5',
            label: this.$t('order.awbExp.col18'),
            showOverflowTooltip: true,
            minWidth: 110,
            show: false,
            formatter: (row, column, cellValue) => {
              let noa_time = '';
              if (cellValue) {
                cellValue.map((v) => {
                  noa_time += v + '\n';
                });
              }
              return noa_time;
            }
          },
          {
            prop: 'mail_sending_status',
            label: this.$t('awb.noa.sendMail'),
            showOverflowTooltip: true,
            minWidth: 80,
            show: false,
            formatter: (row, column, cellValue) => {
              // return cellValue;
              return { 1: this.$t(`basics.no`), 2: this.$t(`basics.yes`) }[
                cellValue
              ];
            }
          },
          {
            columnKey: 'action',
            label: this.$t('basics.action'),
            width: 220,
            align: 'center',
            resizable: false,
            slot: 'action',
            hideInSetting: true,
            fixed: 'right'
          }
        ],
        baseUserColumns: [
          {
            columnKey: 'index',
            type: 'index',
            width: 45,
            role: 'sonOwner', //什么角色下不显示
            showOverflowTooltip: true,
            fixed: 'left'
          },
          {
            prop: 'bol_no',
            label: this.$t('awb.noa.awbno'),
            showOverflowTooltip: true,
            minWidth: 200,
            show: false,
            slot: 'bol_no'
          },
          {
            prop: 'bol_batch',
            label: this.$t('awb.awb.batch'),
            showOverflowTooltip: true,
            minWidth: 100,
            show: false
          },
          {
            prop: 'flight_no',
            label: this.$t('awb.awb.col10'),
            showOverflowTooltip: true,
            minWidth: 100,
            show: false
          },
          {
            prop: 'lgg',
            label: this.$t('awb.awb.port'),
            showOverflowTooltip: true,
            minWidth: 80,
            show: false
          },
          {
            prop: 'box_count',
            label: this.$t('awb.awb.col3'),
            showOverflowTooltip: true,
            minWidth: 80,
            show: false,
            slot: 'check_box_count'
          },
          {
            prop: 'count',
            label: this.$t('batch.out.packagenum'),
            showOverflowTooltip: true,
            minWidth: 80,
            show: false,
            slot: 'check_count'
          },
          {
            prop: 'prediction_count',
            label: this.$t('awb.awb.col4'),
            showOverflowTooltip: true,
            minWidth: 80,
            show: false,
            slot: 'prediction_count'
          },
          {
            prop: 'stock',
            label: this.$t('awb.awb.col5'),
            showOverflowTooltip: true,
            minWidth: 80,
            show: false,
            slot: 'stock'
          },
          {
            prop: 'address_count',
            label: this.$t('awb.awb.col2'),
            showOverflowTooltip: true,
            minWidth: 80,
            show: false
          },
          {
            prop: 'b2b',
            label: this.$t('awb.awb.col1'),
            showOverflowTooltip: true,
            minWidth: 80,
            show: false
          },
          {
            prop: 'gross_mass_kg',
            label: this.$t('awb.awb.col6') + '(kg)',
            showOverflowTooltip: true,
            minWidth: 80,
            show: false
          },
          {
            prop: 'expensive',
            label: this.$t('awb.awb.col7'),
            showOverflowTooltip: true,
            minWidth: 80,
            show: false
          },
          // {
          //   prop: 'exchange_flag',
          //   label: this.$t('awb.awb.col8'),
          //   showOverflowTooltip: true,
          //   minWidth: 80,
          //   show: false
          // },
          // {
          //   prop: 'total_value',
          //   label: this.$t('order.awb.totalValue'),
          //   showOverflowTooltip: true,
          //   minWidth: 80,
          //   show: false
          // },
          {
            prop: 'gross_weight',
            label: this.$t('order.awb.grossWeight') + '(kg)',
            showOverflowTooltip: true,
            minWidth: 150,
            show: false
          },
          {
            prop: 'status',
            label: this.$t('basics.status'),
            showOverflowTooltip: true,
            minWidth: 110,
            formatter: (row, column, cellValue) => {
              // return cellValue;
              return this.$t(`awb.awb.status.s${cellValue}`);
            },
            show: false
          },
          {
            prop: 'clearance',
            label: this.$t('awb.awb.clearStatus'),
            showOverflowTooltip: true,
            minWidth: 110,
            show: false,
            formatter: (row, column, cellValue) => {
              // return cellValue;
              return this.$t(`basics.statusWords.s${cellValue}`);
            }
          },
          // {
          //   prop: 'finance_confirm',
          //   label: this.$t('car.confirmFinance'),
          //   showOverflowTooltip: true,
          //   minWidth: 80,
          //   show: false,
          //   formatter: (row, column, cellValue) => {
          //     // return cellValue;
          //     return { 1: this.$t(`basics.no`), 2: this.$t(`basics.yes`) }[
          //       cellValue
          //     ];
          //   }
          // },
          {
            prop: 'created_at',
            label: this.$t('basics.createTime'),
            showOverflowTooltip: true,
            minWidth: 110,
            show: false
          },
          {
            prop: 'plan_end_date',
            label: this.$t('awb.awb.col18'),
            showOverflowTooltip: true,
            minWidth: 110,
            show: false
          },
          {
            prop: 'time_line.time3',
            label: this.$t('awb.awb.col21'),
            showOverflowTooltip: true,
            minWidth: 110,
            show: false
          },
          {
            prop: 'time_line.time5',
            label: this.$t('order.awbExp.col18'),
            showOverflowTooltip: true,
            minWidth: 110,
            show: false,
            formatter: (row, column, cellValue) => {
              let noa_time = '';
              if (cellValue) {
                cellValue.map((v) => {
                  noa_time += v + '\n';
                });
              }
              return noa_time;
            }
          },
          {
            prop: 'time_line.time3',
            label: this.$t('awb.awb.col21'),
            showOverflowTooltip: true,
            minWidth: 110,
            show: false
          },
          {
            columnKey: 'action',
            label: this.$t('basics.action'),
            width: 220,
            align: 'center',
            resizable: false,
            slot: 'action',
            hideInSetting: true,
            fixed: 'right'
          }
        ],
        baseUrl: this.$store.getters.user.info.web_url,
        // 表格选中数据
        selection: [],
        multipleSelection: [],
        // 当前编辑数据
        current: null,
        arraycurrent: null,
        // 是否显示编辑弹窗
        showEditPack: false,
        showDetailAwb: false,
        showEdit: false,
        displayshowpdf: false,
        showTime: false,
        showTimeEdit: false,
        showEditAwb: false,
        showFinance: false,
        showUploadPdf: false,
        displayshowlogs: false,
        displayshowboxes: false,
        showClear: false,
        showSettingClear: false,
        showSettingStartClear: false,
        showSettingPostClear: false,
        // 是否显示导入弹窗
        showImport: false,
        showFlight: false,
        // 是否显修改密码
        userId: null,
        showChangePassword: false,
        userInfo: this.$store.getters.user.info,
        //导出表头
        exportTitle: [],
        //导出数据项
        exportDataIndex: [],
        where: {},
        sendMailShow: false,
        sendMailData: [],
        dl_pdf_load: false,
        show_declaration: false,
        declaration_data: []
      };
    },
    methods: {
      handleSelectionExportChange(e) {
        console.log(e);
        this.selectExportList = e;
      },
      exportXls() {
        const that = this;
        this.exportTitle = this.selectExportList
          .map((i) => i.label)
          .filter((i) => i);
        this.exportDataIndex = this.selectExportList
          .map((i) => i.prop)
          .filter((i) => i);

        const array = [];
        array.push(this.exportTitle);
        console.log(array);
        getAdminBolExport({
          ...this.where
        }).then((res) => {
          console.log(res);
          console.log(this.exportDataIndex);
          res.forEach((d) => {
            d.status = this.$t(`awb.awb.status.s${d.status}`);
            d.clearance = this.$t(`basics.statusWords.s${d.clearance}`);
            d.finance_confirm = {
              1: this.$t(`basics.no`),
              2: this.$t(`basics.yes`)
            }[d.finance_confirm];
            let data = [];
            that.exportDataIndex.map((i) => {
              if (i == 'time_line.time3') {
                data.push(d['time_line']['time3']);
              } else if (i == 'time_line.time5') {
                data.push(this.initTime5(d['time_line']['time5']));
              } else {
                data.push(d[i]);
              }
            });

            array.push(data);
          });
          console.log(array);
          const sheetName = 'Sheet1';
          const workbook = {
            SheetNames: [sheetName],
            Sheets: {}
          };
          workbook.Sheets[sheetName] = utils.aoa_to_sheet(array);
          writeFile(workbook, 'AWB.xlsx');
        });
      },
      /* 表格数据源 */
      datasource({ page, limit, where, order }) {
        return lists({
          ...where,
          ...order,
          page: page,
          num: limit
        });
      },
      initTime5(time5) {
        let noa_time = '';
        if (time5) {
          time5.map((v) => {
            noa_time += v + ' ';
          });
        }
        return noa_time;
      },
      showExport() {
        this.arraycurrent = this.multipleSelection;
        this.showEditPack = true;
      },
      handleSelectionChange(val) {
        this.multipleSelection = val;
        // console.log(this.multipleSelection)
      },
      async columnsChange(cols) {
        let menu = this.$store.getters?.user?.menu;
        let menuIndex = menu.findIndex(({ type }) => type === 'awb');
        menu[menuIndex] = {
          type: 'awb',
          content: cols.map((i) => i.prop).filter((i) => i)
        };
        await store.dispatch('user/setMenu', [menu[menuIndex]]);
        await setTableCols(menu[menuIndex]);
      },
      /* 刷新表格 */
      getColumns() {
        let menu =
          this.$store.getters?.user?.menu.find(({ type }) => type === 'awb')
            ?.content ?? false;
        if (hasPermission('awb:nouserSearch')) {
          return this.baseColumns.map((v) => {
            return {
              ...v,
              show:
                !menu || v.show === undefined
                  ? true
                  : !!menu.find((i) => i === v.prop)
            };
          });
        } else {
          return this.baseUserColumns.map((v) => {
            return {
              ...v,
              show:
                !menu || v.show === undefined
                  ? true
                  : !!menu.find((i) => i === v.prop)
            };
          });
        }
      },
      /* 刷新表格 */
      outboundFbaSearch(where) {
        if (where) {
          this.where = where;
        }
        this.$refs.table.reload({ page: 1, where });
      },
      /* 刷新表格 */
      reload() {
        this.$refs.table.reload();
      },
      /* 打开编辑弹窗 */
      timeline(row) {
        this.current = row;
        this.showTime = true;
      } /* 打开编辑弹窗 */,
      sendMail(row) {
        this.current = row;
        this.sendMailShow = true;
      },
      detailawb(row) {
        //查看
        this.current = {
          ...row,
          hasRole: !this.$hasRole('sonOwner')
        };
        this.showDetailAwb = true;
      },
      getFinance(row) {
        //财务
        this.current = row;
        this.showFinance = true;
      },
      uploadPdf(row) {
        //财务
        this.current = row;
        this.showUploadPdf = true;
      },
      editawb(row) {
        //编辑
        this.current = row;
        this.showEditAwb = true;
      },
      timelineEdit(row) {
        row.time_line.id = row.id;
        this.current = row.time_line;
        this.showTimeEdit = true;
      },
      showPdf(row) {
        // row.bol_pdf_path = row.bol_pdf_path.replace("apisrv-prd.obs.ap-southeast-1.myhuaweicloud.com", "obsfile.yunkesys.com");
        // console.log(row.bol_pdf_path);
        this.current = row;
        this.displayshowpdf = true;
      },
      showlogs(row) {
        this.current = row;
        this.displayshowlogs = true;
      },
      showboxes(row, status) {
        this.current = {
          id: row.id,
          status: status
        };
        this.displayshowboxes = true;
      },
      showClearUpdate(row) {
        this.current = row;
        this.showClear = true;
      },
      showFlightUpdate() {
        this.showFlight = true;
      },
      cleared(row) {
        this.current = row;
        this.showSettingClear = true;
      },
      startCleared(row) {
        this.current = row;
        this.showSettingStartClear = true;
      },
      /* 打开导入弹窗 */
      openImport() {
        console.log(this.$refs.table.currentPage);
        this.showImport = true;
      },
      jumpCheck(row) {
        this.$router.push('/order/package-check?bol_no=' + row.bol_no);
      },
      jumpBoxCheck(row) {
        this.$router.push(
          '/order/package-check?bol_no=' + row.bol_no + '&box_check=2'
        );
      },
      checkUserAwb(row) {
        const loading = this.$loading({ lock: true });
        userConfirm(row)
          .then(() => {
            loading.close();
            this.$message.success(this.$t('basics.success'));
            this.reload();
          })
          .catch((e) => {
            loading.close();
            this.$message.error(e);
          });
      },
      /* 删除 */
      deleteAwb(row) {
        const loading = this.$loading({ lock: true });
        cancel(row.id)
          .then(() => {
            loading.close();
            this.$message.success(this.$t('basics.success'));
            this.reload();
          })
          .catch((e) => {
            loading.close();
            this.$message.error(e);
          });
      },
      /* 开始清关 */
      notStartedCli(row) {
        // /bols/notStarted/
        const loading = this.$loading({ lock: true });
        notStarted(row.id)
          .then(() => {
            loading.close();
            this.$message.success(this.$t('basics.success'));
            this.reload();
          })
          .catch((e) => {
            loading.close();
            this.$message.error(e);
          });
      },
      checkAwb(row) {
        const loading = this.$loading({ lock: true });
        financeConfirm(row)
          .then(() => {
            loading.close();
            this.$message.success(this.$t('basics.success'));
            this.reload();
          })
          .catch((err) => {
            loading.close();
            this.$message.error(err);
          });
      },
      downloadExcel(url, name = '') {
        let a = document.createElement('a'), //创建a标签
          e = document.createEvent('MouseEvents'); //创建鼠标事件对象
        e.initEvent('click', false, false); //初始化事件对象
        a.href = url; //设置下载地址
        a.target = '_blank';
        a.download = name; //设置下载文件名
        a.dispatchEvent(e); //给指定的元素，执行事件click事件
      },
      downloadManifestFun(id) {
        downloadManifest(id)
          .then(({ result }) => {
            this.downloadExcel(result.objectUrl, 'manifest.zip');
          })
          .catch((e) => {
            this.$message.error(e);
          });
      },
      openDeclaration(row) {
        this.show_declaration = true;
        this.declaration_data = row;
      },
      closeDeclaration() {
        this.show_declaration = false;
        this.declaration_data = [];
        this.reload();
      },
      postClear(row) {
        this.current = row;
        this.showSettingPostClear = true;
      },
      postClearPost(row) {
        const loading = this.$loading({ lock: true });
        postClear(row.id)
          .then(() => {
            loading.close();
            this.$message.success(this.$t('basics.success'));
            this.reload();
          })
          .catch((e) => {
            loading.close();
            this.$message.error(e);
          });
      },
      submitDeclaration(row) {
        getDeclaration({ ...row })
          .then((result) => {
            // console.log(result);
            let resp = result.result;
            if (resp.code == 200) {
              if (resp.data.errors.length > 0) {
                let error_array = [];
                for (let x of resp.data.errors) {
                  error_array.push(x.reference + ' ' + x.text);
                }
                this.$message({
                  message: error_array.join('; '),
                  type: 'error',
                  duration: '5000'
                });
              } else {
                this.$message.success(this.$t('basics.success'));
              }
            } else {
              this.$message({
                message: resp.msg,
                type: 'error',
                duration: '5000'
              });
            }
            this.closeDeclaration();
          })
          .catch((error) => {
            // console.log(error);
            this.$message({
              message: error,
              type: 'error',
              duration: '5000'
            });
            this.closeDeclaration();
          });
      },
      onDropClick(command, item) {
        if (command == 'gettimeline') {
          //时间线
          this.timeline(item);
        } else if (command == 'timeline') {
          //编辑时间线
          this.timelineEdit(item);
        } else if (command == 'getlog') {
          //操作日志
          this.showlogs(item);
        } else if (command == 'dlFest') {
          //下载manifest
          this.downloadManifestFun(item.id);
        } else if (command == 'dlPDF') {
          //pdf
          this.downloadExcel(item.bol_pdf_path, item.bol_pdf_name);
        } else if (command == 'sendMail') {
          //sendMail
          this.sendMail(item);
        } else if (command == 'swPDF') {
          //显示凭证
          this.showPdf(item);
        } else if (command == 'declaration') {
          // this.openDeclaration(item);
          // this.declaration_data = item;
          this.submitDeclaration(item);
        }
      },
      getPdfZip() {
        let params = [],
          select = this.multipleSelection;
        if (select.length > 0) {
          for (let x of select) if (x.bol_pdf_path.length > 0) params.push(x);
          if (params.length > 0) {
            this.dl_pdf_load = true;
            downloadPdfZip({ data: params })
              .then((success) => {
                console.log(success);
                this.downloadExcel(success.result.objectUrl, 'bol_pdf.zip');
              })
              .catch((error) => {
                console.log(error);
              })
              .finally(() => {
                this.dl_pdf_load = false;
              });
          } else {
            this.$message(this.$t('order.awb.selectPathNull'));
          }
        } else {
          this.$message(this.$t('order.package.selectNull'));
        }
      }
    }
  };
</script>
