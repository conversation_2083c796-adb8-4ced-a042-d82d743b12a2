<template>
  <div class="ele-body">
    <el-card shadow="never">
      <!-- 搜索表单 -->
      <outbound-fba-search @search="reload" />
      <!-- 数据表格 -->
      <ele-pro-table
        ref="table"
        :initLoad="true"
        :columns="columns"
        :datasource="listDate"
        :selection.sync="selection"
        :currentPage="currentPage"
      >
        <template slot="toolbar">
          <el-button @click="exportXls" icon="el-icon-download">{{
            $t('basics.export')
          }}</el-button>
        </template>
        <!-- 操作列 -->
        <template slot="action" slot-scope="{ row }">
          <el-link
            @click="openEdit(row)"
            v-permission="'noa:edit'"
            type="primary"
            icon="el-icon-edit"
            :underline="false"
          >
            {{ $t('awb.noa.time') }}
          </el-link>
          <el-link
            @click="openEditTimeline(row)"
            v-permission="'awb:timeline'"
            type="primary"
            icon="el-icon-edit"
            :underline="false"
          >
            {{ $t('awb.awb.col21') }}
          </el-link>
        </template>
      </ele-pro-table>
    </el-card>
    <address-edit :visible.sync="showEdit" :data="current" @done="editReload" />
    <edit-time :visible.sync="showTimeEdit" :data="current" @done="editReload" />
  </div>
</template>

<script>
  import { missData } from '@/api/order/awb';
  import AddressEdit from './components/edit';
  import EditTime from './components/timeEdit';
  import OutboundFbaSearch from './components/outbound-fba-search';

  import { utils, writeFile } from 'xlsx';

  export default {
    name: 'SystemUser',
    components: {
      OutboundFbaSearch,
      EditTime,
      AddressEdit
    },
    data() {
      return {
        // 表格列配置
        columns: [
          {
            columnKey: 'index',
            type: 'index',
            width: 65,
            showOverflowTooltip: true,
            fixed: 'left'
          },
          {
            prop: 'bol_no',
            label: this.$t('awb.noa.awbno'),
            showOverflowTooltip: true,
            minWidth: 180
          },
          {
            prop: 'lgg',
            label: this.$t('awb.awb.port'),
            showOverflowTooltip: true,
            minWidth: 110
          },
          {
            prop: 'plan_end_date',
            label: this.$t('awb.awb.col18'),
            showOverflowTooltip: true,
            minWidth: 110
          },
          {
            prop: 'time3',
            label: this.$t('awb.awb.col21'),
            showOverflowTooltip: true,
            minWidth: 110
          },
          {
            prop: 'time5',
            label: this.$t('awb.noa.time'),
            showOverflowTooltip: true,
            minWidth: 110
          },
          {
            prop: 'created_at',
            label: this.$t('basics.createTime'),
            showOverflowTooltip: true,
            minWidth: 110
          },
          {
            columnKey: 'action',
            label: this.$t('basics.action'),
            width: 220,
            align: 'center',
            resizable: false,
            slot: 'action',
            hideInSetting: true,
            fixed: 'right'
          }
        ],
        // 表格选中数据
        selection: [],
        // 当前编辑数据
        current: null,
        // 是否显示编辑弹窗
        showEdit: false,
        showDetail: false,
        showTimeEdit: false,
        showFinanceEdit: false,
        // 是否显示导入弹窗
        showImport: false,
        showBatchImport: false,
        // 是否显修改密码
        userId: null,
        showChangePassword: false,
        listDate: [],
        oldWhwere: [],
        currentPage: 1
      };
    },
    created() {
      this.oldWhwere = [];
      this.reload(this.oldWhwere);
      //或者登陆不显示货主名字其他登录显示名称
      // if (
      //   this.$store.state.user.info.role_type === '2' ||
      //   this.$store.state.user.info.role_type === '3'
      // ) {
      //   const index = this.columns.findIndex(function (key) {
      //     return key.prop === 'username';
      //   });
      //   this.columns.splice(index, index + 1);
      // }
    },
    activated() {

    },
    methods: {
      /* 刷新表格 */
      reload(where) {
        const loading = this.$loading({ lock: true });
        this.oldWhwere = where;
        missData(where).then((res) => {
          this.$refs.table.reload({ page: 1 });
          this.listDate = res;
          loading.close();
        });
      },
      editReload() {
        this.reload(this.oldWhwere);
      },
      /* 打开编辑弹窗 */
      openEdit(row) {
        this.current = row;
        this.showEdit = true;
      },
      openEditTimeline(row) {
        this.current = row;
        this.showTimeEdit = true;
      },
      /* 打开编辑弹窗 */
      openBatchImport() {
        this.showBatchImport = true;
      },
      openFinanceEdit(row) {
        this.current = row;
        this.showFinanceEdit = true;
      },
      openDetail(row) {
        this.current = row;
        this.showDetail = true;
      },
      exportXls() {
        const array = [];
        array.push([
          this.$t('awb.noa.awbno'),
          this.$t('awb.awb.port'),
          this.$t('awb.awb.col18'),
          this.$t('awb.awb.col21'),
          this.$t('awb.noa.time'),
          this.$t('basics.createTime')
        ]);
        this.listDate.forEach((d) => {
          array.push([
            d.bol_no,
            d.lgg,
            d.plan_end_date,
            d.time3,
            d.time5,
            d.created_at
          ]);
        });
        const sheetName = 'Sheet1';
        const workbook = {
          SheetNames: [sheetName],
          Sheets: {}
        };
        workbook.Sheets[sheetName] = utils.aoa_to_sheet(array);
        writeFile(workbook, 'awb.xlsx');
      },
      /* 打开导入弹窗 */
      openImport() {
        this.showImport = true;
      }
    }
  };
</script>
