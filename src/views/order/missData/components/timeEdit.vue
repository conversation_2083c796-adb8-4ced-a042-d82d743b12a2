<!-- 编辑弹窗 -->
<template>
  <ele-modal
    width="860px"
    :visible="visible"
    :close-on-click-modal="false"
    custom-class="ele-dialog-form"
    :title="$t('basics.edit')"
    @update:visible="updateVisible"
  >
    <el-form ref="form" :model="form" :rules="rules" label-width="180px">
      <el-row :gutter="15">
        <!--
        <el-col :sm="12">
          <el-form-item :label="$t('awb.awb.times.time2')+':'" prop="time2">
            <el-date-picker
              v-model="form.time2"
              type="datetime"
              value-format="yyyy-MM-dd HH:mm:ss"
              :placeholder="$t('basics.placeholder.time1')"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>-->
        <el-col :sm="12">
          <el-form-item :label="$t('awb.awb.col21')+':'" prop="time3">
            <el-date-picker
              v-model="form.time3"
              type="datetime"
              value-format="yyyy-MM-dd HH:mm:ss"
              :placeholder="$t('basics.placeholder.time1')"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div slot="footer">
      <el-button @click="updateVisible(false)">
        {{ $t('basics.cancel') }}
      </el-button>
      <el-button type="primary" :loading="loading" @click="save">
        {{ $t('basics.save') }}
      </el-button>
    </div>
  </ele-modal>
</template>

<script>
  import { timelineEdit } from '@/api/order/awb'

  const DEFAULT_FORM = {
    id: null,
    time2: '',
    time3: ''
  };

  export default {
    name: 'EditTime',
    components: { },
    props: {
      // 弹窗是否打开
      visible: Boolean,
      // 修改回显的数据
      data: Object
    },
    data() {
      return {
        // 表单数据
        form: { ...DEFAULT_FORM },
        // 表单验证规则
        rules: {},
        // 提交状态
        loading: false,
        // 是否是修改
        isUpdate: false
      };
    },
    methods: {
      /* 保存编辑 */
      save() {
        this.$refs['form'].validate((valid) => {
          if (!valid) {
            return false;
          }
          this.loading = true;
          let data = [];
          data.push({
            type: 2,
            time: this.form['time2']
          });
          data.push({
            type: 3,
            time: this.form['time3']
          });
          timelineEdit(this.form.id, data)
            .then((msg) => {
              this.loading = false;
              this.$message.success(this.$t('basics.success'));
              this.updateVisible(false);
              this.$emit('done');
            })
            .catch((e) => {
              this.loading = false;
              this.$message.error(e);
            });
        });
      },
      /* 更新visible */
      updateVisible(value) {
        this.$emit('update:visible', value);
      },
      /* 角色选择回调*/
      roleSelectCallback(value) {
        this.form.role_type = value.type;
      }
    },
    watch: {
      async visible(visible) {
        if (visible) {
          let data = this.data;
          if (this.data) {
            this.form.id = data.id;
            this.form.time3 = data.time3;
            this.form.time2 = data.time2;
            this.isUpdate = true;
          } else {
            this.isUpdate = false;
          }
        } else {
          this.$refs['form'].clearValidate();
          this.form = { ...DEFAULT_FORM };
        }
      }
    }
  };
</script>
