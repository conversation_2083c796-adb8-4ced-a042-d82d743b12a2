<template>
  <el-card shadow="never" body-style="padding: 22px 22px 7px 22px;">
    <!-- 搜索表单 -->
    <el-form
      label-width="120px"
      class="ele-form-search"
      @keyup.enter.native="search"
      @submit.native.prevent
    >
      <el-row :gutter="15">
        <el-col :lg="6" :md="12">
          <el-form-item :label="this.$t('awb.noa.awbno') + ':'" prop="bol_no">
            <el-input v-model="where.bol_no" />
          </el-form-item>
        </el-col>
        <el-col :lg="6" :md="12">
          <el-form-item :label="this.$t('awb.awb.port') + ':'" prop="lgg">
            <port-select v-model="where.lgg" />
          </el-form-item>
        </el-col>
        <el-col :lg="8" :md="12">
          <el-form-item :label="`${$t('awb.awb.col21')}:`" prop="time3">
            <el-date-picker
              v-model="where.time3"
              type="datetimerange"
              value-format="yyyy-MM-dd HH:mm:ss"
              :range-separator="this.$t('basics.to')"
              class="ele-fluid"
              :start-placeholder="this.$t('basics.beginTime')"
              :end-placeholder="this.$t('basics.endTime')"
            >
            </el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :lg="4" :md="12">
          <div class="ele-form-actions">
            <el-button
              type="primary"
              icon="el-icon-search"
              class="ele-btn-icon"
              @click="search"
            >
              {{ $t('basics.query') }}
            </el-button>
            <el-button @click="reset">{{ $t('basics.reset') }}</el-button>
          </div>
        </el-col>
      </el-row>
    </el-form>
  </el-card>
</template>

<script>
  import PortSelect from '@/components/PortSelect';
  const DEFAULT_WHERE = {
    bol_no: '',
    lgg: '',
    time3: ''
  };

  export default {
    name: 'outbound-fba-search',
    components: {
      PortSelect
    },
    data() {
      return {
        // 表单数据
        where: { ...DEFAULT_WHERE }
      };
    },
    methods: {
      /* 搜索 */
      search() {
        //解决搜索 也搜索下级的数据
        this.$emit('search', this.where);
      },
      /* 重置搜索 */
      reset() {
        this.where = { ...DEFAULT_WHERE };
        this.search();
      }
    }
  };
</script>
