<template>
  <div class="ele-body">
    <el-card shadow="never">
      <!-- 搜索表单 -->
      <search @search="reload" />
      <!-- 数据表格 -->
      <ele-pro-table
        ref="table"
        :initLoad="false"
        :columns="columns()"
        :datasource="datasource"
        :selection.sync="selection"
      >
        <template slot="bol_no" slot-scope="{ row }">
          <div
            @click="detailawb(row)"
            style="cursor: pointer; color: var(--color-primary)"
          >
            {{ row.bol_no }}
          </div>
        </template>
        <template slot="prediction_count" slot-scope="{ row }">
          <div
            @click="showboxes(row, 1)"
            style="cursor: pointer; color: var(--color-primary)"
          >
            {{ row.differenceBox }}
          </div>
        </template>

        <template slot="stock" slot-scope="{ row }">
          <div
            @click="showboxes(row, 3)"
            style="cursor: pointer; color: var(--color-primary)"
          >
            {{ row.stockBox }}
          </div>
        </template>
        <!-- 表头工具栏 -->
        <template slot="toolbar">
          <el-button
            size="small"
            icon="el-icon-download"
            class="ele-btn-icon"
            @click="exportXls"
            v-permission="'order:billOfLadingAddress'"
          >
            {{ $t('basics.export') }}
          </el-button>
        </template>

        <template slot="check_count" slot-scope="{ row }">
          {{ row.parcel_check_count }}
          <span
            v-if="row.check_count > 0"
            class="item"
            effect="dark"
            :title="$t('order.awb.check_tips') + row.check_count"
            placement="top-start"
            style="cursor: pointer; color: var(--color-primary)"
          >
            <i @click="jumpCheck(row)" class="el-icon-_warning"></i>
          </span>
        </template>

        <template slot="check_box_count" slot-scope="{ row }">
          {{ row.predictionBox }}
          <span
            v-if="row.check_box_count > 0"
            class="item"
            effect="dark"
            :title="$t('order.awb.check_box_tips') + row.check_box_count"
            placement="top-start"
            style="cursor: pointer; color: var(--color-primary)"
          >
            <i @click="jumpBoxCheck(row)" class="el-icon-_warning"></i>
          </span>
        </template>

        <template slot="storages" slot-scope="{ row }">
          <p v-for="item in row.storages.split(',')">{{ item }}</p>
        </template>
      </ele-pro-table>
    </el-card>
    <detail-awb :visible.sync="showDetailAwb" :data="current" />
    <show-boxes :visible.sync="displayshowboxes" :data="current" />
  </div>
</template>

<script>
  import {
    addressBoxQuantity,
    addressBoxQuantityExportXls
  } from '@/api/order/billOfLadingAddress';
  import search from './components/search';
  import DetailAwb from './components/detail';
  import { utils, writeFile } from 'xlsx';
  import ShowBoxes from '@/views/order/awb/components/box';

  export default {
    name: 'billOfLadingAddress',
    components: {
      DetailAwb,
      ShowBoxes,
      search
    },
    data() {
      return {
        columnsData: [
          {
            columnKey: 'index',
            type: 'index',
            width: 45,
            showOverflowTooltip: true,
            fixed: 'left'
          },
          //提单号
          {
            prop: 'bol_no',
            label: this.$t('awb.noa.awbno'),
            showOverflowTooltip: true,
            minWidth: 230,
            slot: 'bol_no'
          },
          //批次
          {
            prop: 'bol_batch',
            label: this.$t('awb.awb.batch'),
            showOverflowTooltip: true,
            minWidth: 100
          },
          //派送地址
          {
            prop: 'address_code',
            label: this.$t('car.addressOut'), //地址名称
            showOverflowTooltip: true,
            minWidth: 200
          },
          {
            // 航班号
            prop: 'flight_no',
            label: this.$t('order.awbExp.col10'),
            show: false
          },
          //航班港口
          {
            prop: 'lgg',
            label: this.$t('awb.awb.port'),
            showOverflowTooltip: true,
            minWidth: 80
          },
          // 业务类型
          {
            prop: 'b2b',
            label: this.$t('awb.awb.col1'),
            showOverflowTooltip: true,
            minWidth: 80
          },
          {
            // 包裹数
            prop: 'parcel_check_count',
            label: this.$t('batch.out.packagenum'),
            showOverflowTooltip: true,
            minWidth: 80,
            slot: 'check_count'
          },
          {
            prop: 'check_count',
            label: this.$t('order.awb.check_tips_title'),
            show: false
          },
          {
            // 预报箱数
            prop: 'predictionBox',
            label: this.$t('awb.awb.forecastNumber'),
            showOverflowTooltip: true,
            minWidth: 80,
            slot: 'check_box_count'
          },
          {
            prop: 'check_box_count',
            label: this.$t('order.awb.check_box_tips_title'),
            show: false
          },
          //预报箱数
          /*{
            prop: 'predictionBox',
            label: this.$t('awb.awb.forecastNumber'), //预报数
            showOverflowTooltip: true,
            minWidth: 110
          },*/
          //入仓箱数
          {
            prop: 'warehousingBox',
            label: this.$t('awb.awb.receiptQuantity'), //入库数
            showOverflowTooltip: true,
            minWidth: 110
          },
          //出仓箱数
          {
            prop: 'exWarehouseBox',
            label: this.$t('awb.awb.deliveryQuantity'), //出库数
            showOverflowTooltip: true,
            minWidth: 110
          },
          //差异箱数
          {
            prop: 'differenceBox',
            label: this.$t('awb.awb.col4'),
            showOverflowTooltip: true,
            minWidth: 110,
            slot: 'prediction_count'
          },
          //库存箱数
          {
            prop: 'stockBox',
            label: this.$t('awb.awb.col5'),
            showOverflowTooltip: true,
            minWidth: 110,
            slot: 'stock'
          },
          //清关状态
          {
            prop: 'clearance',
            label: this.$t('awb.awb.clearStatus'),
            showOverflowTooltip: true,
            minWidth: 100,
            formatter: (row, column, cellValue) => {
              // return cellValue;
              return this.$t(`basics.statusWords.s${cellValue}`);
            }
          },
          //所属用户
          {
            prop: 'user_name',
            label: this.$t('awb.awb.col9'),
            showOverflowTooltip: true,
            minWidth: 110,
            noRole: 'sonOwner' //什么角色下不显示
          },
          //预计落地时间
          {
            prop: 'plan_end_date',
            label: this.$t('awb.awb.col18'),
            showOverflowTooltip: true,
            minWidth: 110
          },
          //实际落地时间
          {
            prop: 'time3',
            label: this.$t('awb.awb.col21'),
            showOverflowTooltip: true,
            minWidth: 110
          },
          // {
          //   // 入仓时间
          //   prop: 'stock_in_time',
          //   label: this.$t('awb.box.stock_in_time'),
          //   minWidth: 110,
          //   showOverflowTooltip: true,
          //   show: false
          // },
          {
            // 库位
            prop: 'storages',
            label: this.$t('storages.storage_name'),
            minWidth: 200,
            showOverflowTooltip: true,
            slot: 'storages',
            noRole: 'sonOwner' //什么角色下不显示
            // show: false
          },
          {
            // 入仓时间 扫描最后一箱时间
            prop: 'box_in_last_time',
            label: this.$t('order.billOfLadingAddress.box_in_last_time'),
            noRole: 'sonOwner', //什么角色下不显示
            show: false
          },
          {
            // 出仓时间 扫描最后一箱时间
            prop: 'box_out_last_time',
            label: this.$t('order.billOfLadingAddress.box_out_last_time'),
            noRole: 'sonOwner', //什么角色下不显示
            show: false
          }
        ],
        // 表格选中数据
        selection: [],
        where: [],
        // 当前编辑数据
        current: null,
        currentRow: null,
        // 是否显示编辑弹窗
        showEdit: false,
        showEditPack: false,
        // 是否显示导入弹窗
        showImport: false,
        // 是否显修改密码
        userId: null,
        showChangePassword: false,
        displayshowboxes: false,
        multipleSelection: [],
        showDetail: false,
        showDetailAwb: false,
        loading: false
      };
    },

    watch: {
      async selection() {
        console.log(this.selection);
      }
    },
    activated() {
      this.reload();
    },
    methods: {
      columns(isExportXls = false) {
        return this.columnsData.filter((i) => {
          return (
            (i.noRole ? !this.$hasRole(i.noRole) : true) &&
            (i.role ? this.$hasRole(i.role) : true) &&
            (isExportXls ? !!i.label && i.slot !== 'action' : true)
          );
        });
      },
      detailawb(row) {
        //查看
        this.current = {
          ...row,
          hasRole: !this.$hasRole('sonOwner')
        };
        this.showDetailAwb = true;
      },
      /* 表格数据源 */
      datasource({ page, limit, where, order }) {
        return addressBoxQuantity({
          ...where,
          ...order,
          page: page,
          num: limit
        });
      },
      /* 刷新表格 */
      reload(where) {
        this.where = where;
        this.$refs.table.reload({ page: 1, where: where });
      },
      showboxes(row, status) {
        this.current = {
          id: row.bol_id,
          address_code: row.address_code,
          status: status
        };
        this.displayshowboxes = true;
      },
      exportXls() {
        const columns = this.columns(true);
        const array = [
          columns.map((i) => {
            // label
            return i.label;
          })
        ];
        //请求数据
        this.loading = this.$loading({ lock: true });
        addressBoxQuantityExportXls({
          ...this.where,
          isPaging: 1
        })
          .then((list) => {
            console.log(list);
            if (list.length === 0) {
              //暂无导出数据
              return this.$message.error(this.$t('basics.empty_text'));
            }
            const sheetName = 'Sheet1';
            const workbook = {
              SheetNames: [sheetName],
              Sheets: {}
            };
            workbook.Sheets[sheetName] = utils.aoa_to_sheet([
              ...array,
              ...list.map((i) => {
                return columns.map((v) => {
                  return v.formatter
                    ? v.formatter({}, {}, i[v.prop])
                    : i[v.prop];
                });
              })
            ]);
            writeFile(workbook, 'AWB.xlsx');
            this.loading.close();
          })
          .catch((err) => {
            this.loading.close();
            console.log(err);
          });
      },
      jumpCheck(row) {
        this.$router.push(
          '/order/package-check?bol_no=' +
            row.bol_no +
            '&address_code=' +
            row.address_code
        );
      },
      jumpBoxCheck(row) {
        this.$router.push(
          '/order/package-check?bol_no=' +
            row.bol_no +
            '&box_check=2&address_code=' +
            row.address_code
        );
      }
    }
  };
</script>
