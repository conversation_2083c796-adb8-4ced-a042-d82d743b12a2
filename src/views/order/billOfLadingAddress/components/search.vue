<template>
  <el-card shadow="never" body-style="padding: 22px 22px 7px 22px;">
    <!-- 搜索表单 -->
    <el-form
      label-width="185px"
      class="ele-form-search"
      @keyup.enter.native="search"
      @submit.native.prevent
    >
      <el-row :gutter="15">
        <el-col :lg="8" :md="12">
          <el-form-item :label="this.$t('awb.noa.awbno') + ':'" prop="bol_no">
            <el-input
              v-model="where.bol_no"
              :placeholder="this.$t('basics.pleaseInput')"
            />
          </el-form-item>
        </el-col>
        <!--  地址搜索   -->
        <el-col :lg="8" :md="12">
          <el-form-item :label="`${$t('car.addressOut')}:`">
            <address-mulit-select
              :searchType="1"
              v-model="where.address_code"
            />
            <!-- <address-select :searchType="1" v-model="where.address_code" /> -->
          </el-form-item>
        </el-col>
        <!--  清关状态   -->
        <el-col :lg="8" :md="12">
          <el-form-item
            :label="this.$t('awb.awb.clearStatus') + ':'"
            prop="clearance"
          >
            <el-select
              v-model="where.clearance"
              :placeholder="this.$t('basics.pleaseChoose')"
              clearable
              class="ele-fluid"
            >
              <el-option :label="this.$t('basics.statusWords.s1')" :value="1" />
              <el-option :label="this.$t('basics.statusWords.s2')" :value="2" />
              <el-option :label="this.$t('basics.statusWords.s3')" :value="3" />
            </el-select>
          </el-form-item>
        </el-col>
        <!--        所属用户-->
        <el-col :lg="8" :md="12" v-permission="'awb:nouserSearch'">
          <el-form-item :label="`${$t('awb.awb.col9')}:`" prop="bol_no">
            <user-select v-model="where.user_id" />
          </el-form-item>
        </el-col>
        <!--是否有库存-->
        <el-col :lg="8" :md="12">
          <el-form-item
            :label="this.$t('awb.awb.is_stock') + ':'"
            prop="isStock"
          >
            <el-select
              v-model="where.is_stock"
              :placeholder="this.$t('basics.pleaseChoose')"
              clearable
              class="ele-fluid"
            >
              <el-option :label="this.$t('basics.no')" :value="1" />
              <el-option :label="this.$t('basics.yes')" :value="2" />
            </el-select>
          </el-form-item>
        </el-col>
        <!--        入仓状态-->
        <el-col :lg="8" :md="12">
          <el-form-item
            :label="$t('awb.awb.in_storage') + ':'"
            prop="inStorages"
          >
            <el-select
              v-model="where.in_storage"
              :placeholder="this.$t('basics.pleaseChoose')"
              clearable
              class="ele-fluid"
            >
              <el-option :label="$t('awb.awb.in_storages.s1')" :value="1" />
              <el-option :label="$t('awb.awb.in_storages.s2')" :value="2" />
              <el-option :label="$t('awb.awb.in_storages.s3')" :value="3" />
            </el-select>
          </el-form-item>
        </el-col>
        <!--        业务类型-->
        <el-col :lg="8" :md="12">
          <el-form-item
            :label="this.$t('awb.awb.col1') + ':'"
            prop="b2b"
            v-permission="'awb:nouserSearch'"
          >
            <bussiness-select v-model="where.b2b" />
          </el-form-item>
        </el-col>
        <!--航班港口-->
        <el-col :lg="8" :md="12" v-permission="'awb:nouserSearch'">
          <el-form-item :label="this.$t('awb.awb.port') + ':'" prop="lgg">
            <port-select v-model="where.lgg" />
          </el-form-item>
        </el-col>
        <!--预计落地时间-->
        <el-col :lg="8" :md="12">
          <el-form-item :label="$t('awb.awb.col18') + ':'" prop="plan_end_date">
            <el-date-picker
              v-model="where.plan_end_date"
              type="datetimerange"
              value-format="yyyy-MM-dd HH:mm:ss"
              :range-separator="$t('basics.to')"
              class="ele-fluid"
              :start-placeholder="$t('basics.beginTime')"
              :end-placeholder="$t('basics.endTime')"
            >
            </el-date-picker>
          </el-form-item>
        </el-col>
        <!--实际落地时间-->
        <el-col :lg="8" :md="12">
          <el-form-item :label="$t('awb.awb.col21') + ':'" prop="time3">
            <el-date-picker
              v-model="where.time3"
              type="datetimerange"
              value-format="yyyy-MM-dd HH:mm:ss"
              :range-separator="$t('basics.to')"
              class="ele-fluid"
              :start-placeholder="$t('basics.beginTime')"
              :end-placeholder="$t('basics.endTime')"
            >
            </el-date-picker>
          </el-form-item>
        </el-col>
        <!--        -->
        <el-col :lg="8" :md="12">
          <div class="ele-form-actions">
            <el-button
              type="primary"
              icon="el-icon-search"
              class="ele-btn-icon"
              @click="search"
            >
              {{ $t('basics.query') }}
            </el-button>
            <el-button @click="reset">{{ $t('basics.reset') }}</el-button>
          </div>
        </el-col>
      </el-row>
    </el-form>
  </el-card>
</template>

<script>
  // import AddressSelect from '@/components/AddressSelect';
  import AddressMulitSelect from '@/components/AddressMulitSelect';
  import BussinessSelect from '@/components/BussinessSelect';
  import UserSelect from '@/views/order/awb/components/user-select';
  import PortSelect from '@/components/PortSelect';
  const DEFAULT_WHERE = {
    bol_no: '',
    box_no: '',
    track_no: '',
    address_code: [],
    access_warehouse: '',
    check: null,
    user_address_code: null,
    take_delivery: ''
  };

  export default {
    name: 'outbound-fba-search',
    components: {
      AddressMulitSelect,
      // AddressSelect,
      BussinessSelect,
      UserSelect,
      PortSelect
    },
    data() {
      return {
        // 表单数据
        where: { ...DEFAULT_WHERE },
        // 搜索表单是否展开
        searchExpand: false,
        goodsCategoryList: [],
        category_id: ''
      };
    },
    methods: {
      /* 搜索 */
      search() {
        //解决搜索 也搜索下级的数据
        this.$emit('search', this.where);
      },
      /* 重置搜索 */
      reset() {
        this.where = { ...DEFAULT_WHERE };
        this.search();
      },
      /* 搜索展开/收起 */
      toggleExpand() {
        this.searchExpand = !this.searchExpand;
        this.$emit('expand-change', this.searchExpand);
      },
      /* 搜索展开/收起 */
      goodsCategoryFilterListClick(e) {
        this.goodsCategoryList = e.data;
      }
    }
  };
</script>
