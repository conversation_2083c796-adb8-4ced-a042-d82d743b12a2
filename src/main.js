/** 主入口js */
import Vue from 'vue';
import App from './App.vue';
import store from './store';
import router from './router';
import permission from './utils/permission';
import './styles/index.scss';
import EleAdmin from 'ele-admin';
import VueClipboard from 'vue-clipboard2';
import i18n from './i18n';
import { MAP_KEY, LICENSE_CODE, TOOLBAR } from '@/config/setting';
import { handleListName } from '@/utils/util';

Vue.config.productionTip = false;
Vue.prototype.handleListName = handleListName;

Vue.use(EleAdmin, {
  toolkit: TOOLBAR,
  response: {
    dataName: 'list'
  },
  mapKey: MAP_KEY,
  license: LICENSE_CODE,
  i18n: (key, value) => i18n.t(key, value)
});

Vue.use(permission);

Vue.use(VueClipboard);

// Vue.config.lang = 'zh-cn'
// Vue.locale('zh-cn', zhLocale)
// Vue.locale('en', enLocale)
new Vue({
  router,
  store,
  i18n,
  render: (h) => h(App)
}).$mount('#app');
