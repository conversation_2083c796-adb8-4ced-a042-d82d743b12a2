import axios from '@/utils/request';
import { parseNormalData, parsingPagingData } from '@/utils/util';

/**
 * 分页查询用户
 * @param params 查询条件
 */
export async function reservoirList(params) {
  return parsingPagingData(
    await axios.post('/Reservoir/reservoirList', params)
  );
}

/**
 * 分页查询用户
 * @param params 查询条件
 */
export async function reservoirAreaList(params) {
  return parsingPagingData(
    await axios.post('/Reservoir/reservoirAreaList', params)
  );
}
/**
 * 创建库位库区
 */
export async function create(data) {
  return parseNormalData(await axios.post('/Reservoir/create', data));
}
