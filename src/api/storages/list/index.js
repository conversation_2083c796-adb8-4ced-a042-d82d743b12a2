import axios from "@/utils/request";
import { parsingPagingData, parsingPagingDataNoPage } from "@/utils/util";

export async function list(params) {
  return parsingPagingData(await axios.post("/storages/list", params));
}

export async function inventory(params) {
  return parsingPagingData(await axios.post("/storages/inventory", params));
}

export async function create(params) {
  return await axios.post("/storages/create", params);
}

export async function update(params) {
  return await axios.post("/storages/update", params);
}

export async function deleteStorages(params) {
  return await axios.post("/storages/delete", params);
}
