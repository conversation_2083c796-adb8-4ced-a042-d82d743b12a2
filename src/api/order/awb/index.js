import axios from '@/utils/request';
import {
  parseNormalData,
  parsingPagingData,
  parsingPagingDataNoPage
} from '@/utils/util';

/**
 * 分页查询用户
 * @param params 查询条件
 */
export async function lists(params) {
  return parsingPagingData(await axios.get('/bols', { params: params }));
}
export async function noPagelists(params) {
  return parsingPagingDataNoPage(await axios.get('/bols', { params: params }));
}
export async function timeline(params) {
  let temp = await axios.get('/bols/getBolTime/' + params.id);
  // return temp.result.bol_arr;
  return temp.result;
}
export async function timelineEdit(id, params) {
  return await axios.post('/bols/updateBolTime/' + id, params);
}
export async function uploadPdf(id, params) {
  return await axios.post('/bols/uploadPdf/' + id, params);
}
export async function getclear(params) {
  return await axios.post('/bols/updateClearance/' + params.id, params);
}
export async function uploadClearance(params) {
  return await axios.post('/bols/uploadClearance', params);
}
export async function uploadFlight(params) {
  return await axios.post('/bols/uploadFlight', params);
}
export async function financeConfirm(params) {
  return await axios.get('/bols/financeConfirm/' + params.id);
}
export async function logs(params) {
  return parsingPagingData(
    await axios.get('/bols/getBolLog/' + params.id, { params: params })
  );
}
export async function boxes(params) {
  return parsingPagingData(
    await axios.get('/bols/getBolBoxs/' + params.id, { params: params })
  );
}
export async function edit(params) {
  return await axios.put('/bols/' + params.id, params);
}
export async function userConfirm(params) {
  return await axios.get('/bols/userConfirm/' + params.id);
}

export async function info(params) {
  return await axios.get('/bols/' + params.id);
}
export async function financeSetting(params) {
  return await axios.post('/bols/financeSetting/' + params.id, params);
}
export async function financeExport(params) {
  return await axios.post('/bols/export', params);
}
export async function financeExportCost(params) {
  return await axios.post('/bols/exportCost', params);
}
/**
 * 出库单修改
 */
export async function update(data) {
  return parseNormalData(await axios.post('/Outboundfba/update', data));
}

/**
 * 出库单修改
 */
export async function create(data) {
  return await axios.post('/bols', data);
}

/**
 * 取消
 */
export async function cancel(id) {
  return await axios.delete('/bols/' + id);
}
/**
 * 开始清关
 */
export async function notStarted(params) {
  return await axios.post(`/bols/notStarted/` + params.id, params);
}
/**
 * 下载downloadManifest
 */
export async function downloadManifest(id) {
  return await axios.post(`/bols/downloadManifest/${id}`);
}
export async function downloadPdfZip(params) {
  return await axios.post(`/bols/downloadPdfZip`, params);
}
export async function getBolExport(params) {
  return parsingPagingDataNoPage(
    await axios.get('/getBolExport', { params: params })
  );
}
export async function getAdminBolExport(params) {
  return parsingPagingDataNoPage(
    await axios.get('/getAdminBolExport', { params: params })
  );
}
/**
 * 发送邮件
 */
export async function sendMail(params) {
  return await axios.post(`/bols/sendMail/` + params.id, params);
}

export async function getDeclaration(params) {
  return await axios.post(`/bols/declaration/` + params.id, params);
}

export async function getVisitHourList(params) {
  let temp = await axios.post('/bols/bolViewTime', params);
  return temp.result;
}

export async function getAwbDatList(params) {
  return parsingPagingData(await axios.post('/bols/bolViewDate', params));
}

/**
 * 接口清关提交
 */
export async function postClear(params) {
  return await axios.post(`/bols/postClear/` + params.id, params);
}

export async function missData(params) {
  let res = await axios.post('/bols/missData', params);
  if (res.result) {
    return res.result;
  }
  return [];
}

export async function abnormalData(params) {
  let res = await axios.post('/bols/abnormalData', params);
  if (res.result) {
    return res.result;
  }
  return [];
}
