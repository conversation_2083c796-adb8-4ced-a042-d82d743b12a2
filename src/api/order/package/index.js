import axios from '@/utils/request';
import { parseNormalData, parsingPagingData } from '@/utils/util';

/**
 * 分页查询用户
 * @param params 查询条件
 */
export async function lists(params) {
  return parsingPagingData(await axios.get('/parcels', { params: params }));
  // let res = await axios.get('/parcels', { params: params });
  // return res.result;
}

/**
 * 分页查询用户
 * @param params 查询条件
 */
export async function lists2(params) {
  let res = await axios.get('/parcels', { params: params });
  return {
    list: res.result,
    next: res.next_page
  };
}

export async function settingPack(params) {
  return await axios.post('/parcels/updateOption', params);
}

/**
 * 入库单详情
 */
export async function info(params) {
  return await axios.get('/parcels/' + params.id);
}

/**
 * 出库单修改
 */
export async function update(data) {
  return parseNormalData(await axios.post('/Outboundfba/update', data));
}

/**
 * 出库单修改
 */
export async function create(data) {
  return parseNormalData(await axios.post('/Outboundfba/Create', data));
}

/**
 * 取消出库单
 */
export async function cancel(id) {
  return parseNormalData(await axios.post('/Outboundfba/Create', { id: id }));
}

export async function editPackage(params) {
  return await axios.post('/parcels/editPackage', params);
}

/**
 * 批量导出提单号、包裹号、包裹编号信息
 */
export async function exportPackage(params) {
  return await axios.post('/parcels/exportPackage', params);
}

export async function multiParcelOut(params) {
  return await axios.post('/parcels/multiParcelOut', params);
}

export async function changeCmr(params) {
  return await axios.post('/parcels/updatePendingCmr', params);
}
