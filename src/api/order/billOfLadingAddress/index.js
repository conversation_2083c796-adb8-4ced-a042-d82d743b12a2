import axios from '@/utils/request';
import { parsingPagingData, parsingPagingDataNoPage } from '@/utils/util';

export async function addressBoxQuantity(params) {
  return parsingPagingData(
    await axios.get('/boxes/addressBoxQuantity', { params: params })
  );
}

export async function addressBoxQuantityExportXls(params) {
  return parsingPagingDataNoPage(
    await axios.get('/boxes/addressBoxQuantity', { params: params })
  );
}
