import axios from '@/utils/request';
import { parsingPagingData } from '@/utils/util';

export async function getValidityHourList(params) {
  let temp = await axios.post('/bols/validityHourList', params);
  return temp;
}
export async function validityLineList(params) {
  let temp = await axios.post('/bols/validityLineList', params);
  return temp;
}
/**
 * 分页查询
 * @param params 查询条件
 */
export async function validityHourParcelList(params) {
  return parsingPagingData(
    await axios.post('/bols/validityHourParcelList', params)
  );
}
/**
 * 查询全部并导出
 * @param params 查询条件
 */
export async function validityHourParcelListExcel(params) {
  return await axios.post('/bols/validityHourParcelListExcel', params);
}

/**
 * 列表
 * @param params 查询条件
 */
export async function lists(params) {
  return parsingPagingData(
    await axios.get('/parcelsExcel', { params: params })
  );
}

/**
 * 取消
 */
export async function cancel(id) {
  return await axios.delete('/parcelsExcel/' + id);
}
/**
 * 获取下载链接
 */
export async function info(id) {
  return await axios.get('/parcelsExcel/' + id);
}
/**
 *提单维度时效派送地址列表
 */
export async function validityAddressLists(params) {
  return parsingPagingData(await axios.post('/bols/validityAddress', params));
}
/**
 * 设置超时原因
 */
export async function editReason(data) {
  return await axios.post('/bols/editReason', data);
}
/**
 * 查看提单派送地址超时原因
 */
export async function awbReasonInfo(data) {
  return await axios.post('/bols/infoReason', data);
}
/**
 * 提单维度时效看板
 */
export async function validityBol(params) {
  let temp = await axios.post('/bols/validityBol', params);
  return temp;
}
/**
 * 分页查询
 * @param params 查询条件
 */
export async function validityBolList(params) {
  return parsingPagingData(await axios.post('/bols/validityBolList', params));
}
/**
 * 查询导出
 * @param params 查询条件
 */
export async function validityBolListExp(params) {
  return await axios.post('/bols/validityBolList', params);
}
/**
 * 提单维度趋势图
 * */
export async function getValidityBolHour(params) {
  let temp = await axios.post('/bols/validityBolHour', params);
  return temp;
}
