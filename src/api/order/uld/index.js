import axios from '@/utils/request';
import { parsingPagingData, parsingPagingDataNoPage } from '@/utils/util';

/**
 * 分页查询航空板
 * @param params 查询条件
 */
export async function lists(params) {
  return parsingPagingData(await axios.get('/airPlankBol', { params: params }));
}

/**
 * 查询全部航空板
 * @param params 查询条件
 */
export async function expLists(params) {
  return parsingPagingDataNoPage(
    await axios.get('/airPlankBolExp', { params: params })
  );
}

/**
 * 设置备注
 */
export async function modifyRemarks(params) {
  return parsingPagingDataNoPage(
    await axios.post('/airPlankBol/modifyRemarks', params)
  );
}
