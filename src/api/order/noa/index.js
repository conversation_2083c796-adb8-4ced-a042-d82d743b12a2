import axios from '@/utils/request';
import { parsingPagingData } from '@/utils/util';

/**
 * 分页查询用户
 * @param params 查询条件
 */
export async function lists(params) {
  return parsingPagingData(await axios.get('/noa', { params: params }));
}
export async function del(params) {
  return await axios.delete('/noa/' + params.id);
}
export async function edit(params) {
  return await axios.put('/noa/' + params.id, params);
}
export async function create(params) {
  return await axios.post('/noa', params);
}
export async function determine(params) {
  return await axios.post('/noa/determine', params);
}

/**
 * 批量导入
 * */
export async function noaImport(params) {
  return await axios.post('/noa/import', params);
}
