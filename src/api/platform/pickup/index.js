import axios from '@/utils/request';
import { parseNormalData, parsingPagingData } from '@/utils/util';

/**
 * 列表
 * @param params 查询条件
 */
export async function lists(params) {
  return parsingPagingData(await axios.get('/pickup', { params: params }));
}

/**
 * 修改
 */
export async function update(data) {
  return await axios.put('/pickup/' + data.id, data);
}

/**
 * 新建
 */
export async function create(data) {
  return await axios.post('/pickup', data);
}

/**
 * 取消
 */
export async function cancel(id) {
  return await axios.delete('/pickup/' + id);
}

/**
 * 确认离开
 */
export async function confirm(params) {
  return await axios.post('/pickup/confirm', params);
}

/**
 * 复制
 */
export async function copy(params) {
  return await axios.post('/pickup/copy', params);
}

/**
 * 全部列表
 * @param params 查询条件
 */
export async function getAll(params) {
  return await axios.post('/pickup/getAll', params);
}
