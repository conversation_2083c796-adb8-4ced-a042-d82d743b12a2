import axios from '@/utils/request';

/**
 * 上传文件
 * @param file 文件
 */
export async function uploadFile(file) {
  const formData = new FormData();
  formData.append('file', file);
  const res = await axios.post('/upload_file', formData);
  return res.result;
}

export async function uploadFileObs(file) {
  const formData = new FormData();
  formData.append('file', file);
  const res = await axios.post('/uploadFileObs', formData);
  return res.result;
}
/**
 * 上传文件
 */
export async function bolFileUpload(file, type) {
  const formData = new FormData();
  formData.append('file', file);
  formData.append('type', type);
  return await axios.post('/bolFileUpload', formData);
}

// export async function importUsers(file) {
//   const formData = new FormData();
//   formData.append('file', file);
//   const res = await axios.post('/system/user/import', formData);
//   if (res.data.code === 0) {
//     return res.data.message;
//   }
//   return Promise.reject(new Error(res.data.message));
// }

/**
 * 分页查询文件上传记录
 * @param params 查询条件
 */
export async function pageFiles(params) {
  const res = await axios.get('/file/page', {
    params
  });
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}
