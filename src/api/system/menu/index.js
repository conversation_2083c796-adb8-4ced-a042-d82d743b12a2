import axios from '@/utils/request';
import { parseNormalData, parsingPagingData,parsingPagingDataNoPage } from '@/utils/util';
/**
 * 查询菜单列表
 * @param params 查询条件
 */
export async function listMenus(params) {

  // const res = await axios.get('/menues')
  // console.log(res)
  let data = {
    num:10000,
    page:1,
  }
  return parsingPagingDataNoPage(await axios.get('/menues',{params:data}))
  // return Promise.reject(new Error(res.data.message));
}

/**
 * 添加菜单
 * @param data 菜单信息
 */
export async function addMenu(data) {
  const res = await axios.post('/menues', data);
  if (res.status == 'success') {
    return res;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 修改菜单
 * @param data 菜单信息
 */
export async function updateMenu(data) {
  const res = await axios.put('/menues/'+data.menu_id, data);
  if (res.status == 'success') {
    return res;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 删除菜单
 * @param data
 */
export async function removeMenu(data) {
  const res = await axios.delete('/menues/'+data.id);
  if (res.status == 'success') {
    return res;
  }
  return Promise.reject(new Error(res.data.message));
}
