import axios from '@/utils/request';
import { parsingPagingData } from '@/utils/util';

/**
 * 分页查询角色
 * @param params 查询条件
 */
export async function pageRoles(data) {
  return parsingPagingData(await axios.get('/roles', {params:data}));
}

/**
 * 查询角色列表
 * @param params 查询条件
 */
export async function listRoles(params) {
  const res = await axios.post('/system/role', {
    params
  });
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 添加角色
 * @param data 角色信息
 */
export async function addRole(data) {
  const res = await axios.post('/roles', data);
  if (res.status == 'success') {
    return res.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 修改角色
 * @param data 角色信息
 */
export async function updateRole(data) {
  const res = await axios.put('/roles/'+data.id, data);
  if (res.status == 'success') {
    return res.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 删除角色
 * @param data arr
 */
export async function removeRole(data) {
  const res = await axios.delete('/roles/'+data.id, data);
  if (res.status == 'success') {
    return res.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 获取角色分配的菜单
 * @param data 角色id
 */
export async function listRoleMenus(data) {
  const res = await axios.get('/roles/role-menu/' + data.id);
  console.log(res.result)
  return res.result
  return Promise.reject(new Error(res.data.message));
}

/**
 * 修改角色菜单
 * @param data 菜单id集合
 */
export async function updateRoleMenus(data) {
  const res = await axios.post('/roles/role-menu/'+data.id, data.permission_ids);
  return res
  return Promise.reject(new Error(res.data.message));
}
