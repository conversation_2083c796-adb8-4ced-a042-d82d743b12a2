import axios from '@/utils/request';
import {
  parseNormalData,
  parsingPagingData,
  parsingPagingDataNoPage
} from '@/utils/util';

/**
 * 分页查询用户
 * @param params 查询条件
 */
export async function pageUsers(data) {
  return parsingPagingData(await axios.get('/user', { params: data }));
}
export async function getUserMail(data) {
  return parsingPagingData(
    await axios.get('/user/getUserMail', { params: data })
  );
}
export async function nopageUsers(data) {
  return parsingPagingDataNoPage(await axios.get('/user', { params: data }));
}

/**
 * 查询用户列表
 * @param params 查询条件
 */
export async function listUsers(data) {
  const res = await axios.get('/user', { params: data });
  return res.result;
  return Promise.reject(new Error(res.data.message));
}

/**
 * 根据id查询用户
 * @param id 用户id
 */
export async function getUser(id) {
  const res = await axios.get('/system/user/' + id);
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 添加用户
 * @param data 用户信息
 */
export async function addUser(data) {
  const res = await axios.post('/user', data);
  return res;
}

/**
 * 修改用户
 * @param data 用户信息
 */
export async function updateUser(data) {
  const res = await axios.put('/user/' + data.id, data);
  return res;
}

/**
 * 删除用户
 * @param id 用户id
 */
export async function removeUser(id) {
  const res = await axios.delete('/user/' + id);
  return res;
  return Promise.reject(new Error(res.data.message));
}

/**
 * 批量删除用户
 * @param data 用户id集合
 */
export async function removeUsers(data) {
  const res = await axios.delete('/system/user/batch', {
    data
  });
  if (res.data.code === 0) {
    return res.data.message;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 修改用户状态
 */
export async function updateUserStatus(data) {
  return await axios.post('/user/userStatus', data);
}

/**
 * 重置用户密码
 * @param data '{"id",password}'
 * @returns {Promise<string>}
 */
export async function updateUserPassword(data) {
  return parseNormalData(await axios.post('/users/resetPassword', data));
}

/**
 * 导入用户
 * @param file excel文件
 */
export async function importUsers(file) {
  const formData = new FormData();
  formData.append('file', file);
  const res = await axios.post('/system/user/import', formData);
  if (res.data.code === 0) {
    return res.data.message;
  }
  return Promise.reject(new Error(res.data.message));
}

export async function getUserFinances(params) {
  return await axios.get('/user/userFinances/' + params.id);
}
export async function setUserFinances(params) {
  return await axios.post('/user/userFinances/' + params.user_id, params);
}

/**
 * 读取消息
 * */
export async function changeReadStatus(id) {
  return await axios.get(`/user/changeReadStatus?id=${id}`);
}

/**
 * 全部消息数
 * */
export async function getUserMailNoReading() {
  return await axios.get(`/user/getUserMailNoReading`);
}
