import axios from '@/utils/request';
import { parseNormalData } from '@/utils/util';

/**
 * 国家二字码列表
 */
export async function countryCode() {
  return await axios.get('/country')
}
export async function repassword(data) {
  return await axios.post('/user/modifyPassword',data)
}
/**
 * 货主
 */
export async function ownerList() {
  let data = {
    page:1,
    num:1000
  }
  return await axios.get('/user',{params:data})
}

/**
 * 获取阿里云上传
 */
export async function getAliYunUpdateRpc() {
  return parseNormalData(
    await axios.post('/Common/getAliYunUpdateRpc'),
    false,
    function (res) {
      return res.data.data;
    }
  );
}
