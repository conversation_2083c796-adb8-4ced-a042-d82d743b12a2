import axios from '@/utils/request';
import { parsingPagingData } from '@/utils/util';

/**
 * 列表
 * @param params 查询条件
 */
export async function lists(params) {
  return parsingPagingData(
    await axios.get('/highHsCode', { params: params })
  );
}

/**
 * 删除
 * @param id 查询条件
 */
export async function destroy(id) {
  return await axios.delete(`highHsCode/${id}`);
}

/**
 * create
 */
export async function create(data) {
  return await axios.post('/highHsCode', data);
}

/**
 * update
 */
export async function update(data) {
  return await axios.put(`/highHsCode/${data.id}`, data);
}
