import axios from '@/utils/request';
import { parseNormalData, parsingPagingData } from '@/utils/util';

/**
 * 子货主列表
 * @param params 查询条件
 */
export async function list(params) {
  return parsingPagingData(await axios.post('/Subowner/lists', params));
}

/**
 * 添加用户
 * @param data 用户信息
 */
export async function create(data) {
  const res = await axios.post('/Subowner/create', data);
  if (res.data.code === '200') {
    return res.data.message;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 删除用户
 * @param userId
 */
export async function SubOwnerDelete(userId) {
  return parseNormalData(
    await axios.post('/Subowner/delete', {
      id: userId
    })
  );
}

/**
 * 重置用户密码
 * @param data '{"id",password}'
 * @returns {Promise<string>}
 */
export async function updateUserPassword(data) {
  return parseNormalData(await axios.post('/users/resetPassword', data));
}
