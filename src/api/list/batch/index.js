import axios from '@/utils/request';
import { parseNormalData, parsingPagingData } from '@/utils/util';

/**
 * 获取列表数据
 */
export async function list(params) {
  return parsingPagingData(await axios.get('/batches',{ params:params }))
  return Promise.reject(new Error(res.data.message));
}
export async function appoint(id,params) {
  return await axios.post('/batches/appoint/'+id,params)
}
export async function info(params) {
  return await axios.get('/batches/'+ params.id )
}
