import axios from '@/utils/request';
import { parseNormalData, parsingPagingData } from '@/utils/util';

/**
 * 获取列表数据
 */
export async function list(params) {
  return parsingPagingData(await axios.get('/planning',{ params:params }))
  return Promise.reject(new Error(res.data.message));
}
export async function status(params) {
  return await axios.post('/planning/update',params)
}
export async function create(params) {
  return await axios.post('/planning',params)
}
export async function edit(params) {
  return await axios.put('/planning/'+params.id,params)
}
export async function cancel(params) {
  return await axios.delete('/planning/'+params.id)
}
export async function info(params) {
  return await axios.get('/planning/'+ params.id )
}
