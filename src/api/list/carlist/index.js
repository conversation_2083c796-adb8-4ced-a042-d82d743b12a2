import axios from '@/utils/request';
import { parsingPagingData, parsingPagingDataNoPage } from '@/utils/util';

/**
 * 获取列表数据
 */
export async function list(params) {
  return parsingPagingData(await axios.get('/cmrs', { params: params }));
}
export async function airlist(params) {
  return parsingPagingData(await axios.get('/getAirNo', { params: params }));
}

export async function update(params) {
  return await axios.put('/cmrs/' + params.id, params);
}
export async function deleteCmr(params) {
  return await axios.delete('/cmrs/' + params.id);
}
export async function settingPay(params) {
  return await axios.post('/cmrs/allocationFree', params);
}
export async function cancelPaySetting(params) {
  return await axios.post('/cmrs/cancelFree', params);
}
export async function lockStatus(params) {
  return await axios.get('/cmrs/lockStatus/' + params.id);
}
export async function financeCon(params) {
  return await axios.get(
    '/cmrs/financeConfirm/' + params.id + '?voucher_url=' + params.voucher_url
  );
}
export async function uploadVoucher(params) {
  return await axios.get(
    '/cmrs/uploadVoucher/' + params.id + '?voucher_url=' + params.voucher_url
  );
}
export async function uploadVehicleList(params) {
  return await axios.get(
    '/cmrs/uploadVehicleList/' + params.id + '?cmr_path=' + params.cmr_path
  );
}
export async function queryAirNo(params) {
  return await axios.post('/queryAirNo', params);
}
export async function getBase(params) {
  return await axios.post('/get_file', params);
}

export async function getCmrsAll(params) {
  return parsingPagingDataNoPage(
    await axios.get('/getCmrsAll', { params: params })
  );
}

export async function getCmrsAllCost(params) {
  return parsingPagingDataNoPage(
    await axios.get('/getCmrsAllCost', { params: params })
  );
}

export async function batchUploadCost(params) {
  return await axios.post('/cmrs/batchSettingCost', params);
}

export async function getCmrParcel(params) {
  return parsingPagingData(
    await axios.get('/cmrs/getCmrParcels', { params: params })
  );
}
