import axios from '@/utils/request';
import { parseNormalData, parsingPagingData } from '@/utils/util';

/**
 * 列表
 * @param params 查询条件
 */
export async function lists(params) {
  return parsingPagingData(await axios.get('/ports', {params:params}));
}

/**
 * 修改
 */
export async function update(data) {
  return await axios.put('/ports/'+data.id, data)
}

/**
 * 新建
 */
export async function create(data) {
  return await axios.post('/ports', data)
}

/**
 * 取消
 */
export async function cancel(id) {
  return await axios.delete('/ports/'+id)
}
