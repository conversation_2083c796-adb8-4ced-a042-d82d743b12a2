import axios from '@/utils/request';
import { parsingPagingData } from '@/utils/util';

/**
 * 列表
 * @param params 查询条件
 */
export async function lists(params) {
  return parsingPagingData(
    await axios.get('/AddressUser/list', { params: params })
  );
}

/**
 * 删除
 * @param params 查询条件
 */
export async function destroy(params) {
  return await axios.get('/AddressUser/delete', { params: params });
}

/**
 * 新建
 */
export async function create(data) {
  return await axios.post('/AddressUser/create', data);
}

/**
 * 新建
 */
export async function update(data) {
  return await axios.post('/AddressUser/update', data);
}
