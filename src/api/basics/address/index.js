import axios from '@/utils/request';
import { parseNormalData, parsingPagingData } from '@/utils/util';

/**
 * 列表
 * @param params 查询条件
 */
export async function lists(params) {
  return parsingPagingData(await axios.get('/addresses', {params:params}));
}

/**
 * 修改
 */
export async function update(data) {
  return await axios.put('/addresses/'+data.id, data)
}

/**
 * 新建
 */
export async function create(data) {
  return await axios.post('/addresses', data)
}

/**
 * 取消
 */
export async function cancel(id) {
  return await axios.delete('/addresses/'+id)
}
export async function financeSetting(data) {
  return await axios.post('/address/financeSetting/'+data.id,data)
}

/**
 * 修改地址状态
 */
export async function updateAddressStatus(data) {
  return await axios.post('/address/changeStatus', data);
}
