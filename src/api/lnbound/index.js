import axios from '@/utils/request';
import { parseNormalData, parsingPagingData } from '@/utils/util';

/**
 * 分页查询用户
 * @param params 查询条件
 */
export async function lists(params) {
  return parsingPagingData(await axios.post('/Inbound/lists', params));
}

/**
 * 入库单详情
 */
export async function info(id) {
  return parseNormalData(
    await axios.post('/Inbound/Info', {
      id: id
    }),
    false,
    (res) => {
      return res.data.data;
    }
  );
}

/**
 * 入库单修改
 */
export async function update(data) {
  return parseNormalData(await axios.post('/Inbound/update', data));
}

/**
 * 入库单修改
 */
export async function create(data) {
  return parseNormalData(await axios.post('/Inbound/Create', data));
}

/**
 * 取消出库单
 */
export async function cancel(id) {
  return parseNormalData(await axios.post('/Inbound/cancel', { id: id }));
}
