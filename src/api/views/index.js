import axios from '@/utils/request';
import { parsingPagingDataNoPage } from '@/utils/util';

/**
 * 获取版本信息
 */
export async function getVersions() {
  return parsingPagingDataNoPage(await axios.get('/versions'));
}

/**
 * 修改版本信息
 * @param params 查询条件
 */
export async function setVersions(params) {
  return parsingPagingDataNoPage(await axios.post('/versions', params));
}
/**
 * 获取版本信息
 */
export async function getConfig() {
  return parsingPagingDataNoPage(
    await axios.get('/config', { params: { key: 'mail' } })
  );
}

/**
 * 修改版本信息
 * @param params 查询条件
 */
export async function setConfig(params) {
  return parsingPagingDataNoPage(await axios.post('/config', params));
}
/**
 * 获取版本信息
 */
export async function getProcess() {
  return parsingPagingDataNoPage(
    await axios.get('/config', { params: { key: 'process' } })
  );
}
