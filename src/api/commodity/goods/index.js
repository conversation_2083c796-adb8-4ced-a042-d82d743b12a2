import axios from '@/utils/request';
import { parseNormalData, parsingPagingData } from '@/utils/util';

/**
 * 分页查询
 * @param params 查询条件
 */
export async function pageList(params) {
  return parsingPagingData(await axios.post('/Goods/lists', params));
}

/**
 * 创建
 */
export async function create(data) {
  return parseNormalData(await axios.post('/Goods/Create', data));
}
/**
 * 修改
 */
export async function update(data) {
  return parseNormalData(await axios.post('/Goods/Update', data));
}
/**
 * 删除
 */
export async function Delete(data) {
  return parseNormalData(await axios.post('/Goods/Delete', data));
}

/**
 * 删除
 */
export async function batchCreate(data) {
  return parseNormalData(await axios.post('/Goods/batchCreate', data));
}

/**
 * 删除
 */
export async function verifyInAndOut(data) {
  return parseNormalData(
    await axios.post('/Goods/verifyInAndOut', data),
    false,
    (res) => {
      return res.data.data;
    }
  );
}
