import axios from '@/utils/request';
import { parseNormalData } from '@/utils/util';

/**
 * 分页查询
 * @param params 查询条件
 */
export async function pageGoodeCategory(params) {
  return parseNormalData(
    await axios.post('/Goodscategory/lists', params),
    false,
    function (res) {
      return res.data.data;
    }
  );
}

/**
 * 创建
 */
export async function create(data) {
  return parseNormalData(await axios.post('/Goodscategory/Create', data));
}
/**
 * 修改
 */
export async function update(data) {
  return parseNormalData(await axios.post('/Goodscategory/Update', data));
}
/**
 * 删除
 */
export async function Delete(data) {
  return parseNormalData(await axios.post('/Goodscategory/delete', data));
}
