import axios from '@/utils/request';
import { parseNormalData, parsingPagingData } from '@/utils/util';

/**
 * 分页查询
 * @param params 查询条件
 */
export async function pageBrand(params) {
  return parsingPagingData(await axios.post('/Brand/lists', params));
}

/**
 * 创建品牌
 */
export async function create(data) {
  return parseNormalData(await axios.post('/Brand/Create', data));
}
/**
 * 修改品牌
 */
export async function update(data) {
  return parseNormalData(await axios.post('/Brand/Update', data));
}
/**
 * 删除品牌
 */
export async function Delete(data) {
  return parseNormalData(await axios.post('/Brand/Delete', data));
}
