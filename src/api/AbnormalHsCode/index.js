import axios from '@/utils/request';
import { parsingPagingData } from '@/utils/util';

/**
 * 列表
 * @param params 查询条件
 */
export async function lists(params) {
  return parsingPagingData(
    await axios.get('/abnormalHsCode', { params: params })
  );
}

/**
 * 删除
 * @param id 查询条件
 */
export async function destroy(id) {
  return await axios.delete(`abnormalHsCode/${id}`);
}

/**
 * create
 */
export async function create(data) {
  return await axios.post('/abnormalHsCode', data);
}

/**
 * update
 */
export async function update(data) {
  return await axios.put(`/abnormalHsCode/${data.id}`, data);
}
