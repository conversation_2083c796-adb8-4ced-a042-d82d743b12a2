/**
 * 登录状态管理
 */
import { formatMenus, toTreeData, formatTreeData } from 'ele-admin';
import { USER_MENUS } from '@/config/setting';
import { getUserInfo } from '@/api/layout';

export default {
  namespaced: true,
  state: {
    // 当前登录用户信息
    info: null,
    // 当前登录用户的菜单
    menus: null,
    // 当前登录用户的权限
    authorities: [],
    // 当前登录用户的角色
    roles: [],
    menu: [] //当前页面侧边列表
  },
  mutations: {
    // 设置登录用户的信息
    setUserInfo(state, info) {
      state.info = info;
    },
    // 设置登录用户的菜单
    setMenus(state, menus) {
      state.menus = menus;
    },
    // 设置登录用户的权限
    setAuthorities(state, authorities) {
      state.authorities = authorities;
    },
    // 设置登录用户的角色
    setRoles(state, roles) {
      state.roles = roles;
    },
    // 设置登录用户的角色
    setMenu(state, menu) {
      state.menu = menu;
    }
  },
  actions: {
    /**
     * 请求用户信息、权限、角色、菜单
     */
    async fetchUserInfo({ commit }) {
      const result = await getUserInfo();
      // 用户信息
      commit('setUserInfo', result);
      commit('setMenu', result.menu);
      // 用户权限
      const authorities =
        result.permissions
          ?.filter((d) => d.menu_type === 2)
          ?.map((d) => d.authority) ?? [];
      commit('setAuthorities', authorities);
      //重新处理格式
      const menusNew = result.permissions.map(function (item) {
        return {
          id: item.id,
          parent_id: item.parent_id,
          type: item.menu_type,
          title: item.title,
          icon: item.icon,
          path: item.website_path,
          hide: item.hide !== 1,
          component: item.component
        };
      });
      // 用户角色
      let role_type_name = {
        1: 'admin',
        2: 'owner',
        3: 'sonOwner',
        4: 'finance',
        5: 'pda'
      };
      console.log(role_type_name[result.role_id]);
      commit('setRoles', [role_type_name[result.role_id]]);
      // 用户菜单, 过滤掉按钮类型并转为children形式
      const { menus, homePath } = formatMenus(
        USER_MENUS ??
          toTreeData({
            data: menusNew?.filter((d) => d.type === 1),
            idField: 'id',
            parentIdField: 'parent_id'
          })
      );
      // console.log(menus)
      commit('setMenus', menus);
      return { menus, homePath };
    },
    /**
     * 更新用户信息
     */
    setMenu({ commit }, value) {
      commit('setMenu', value);
    },
    /**
     * 更新菜单的badge
     */
    setMenuBadge({ commit, state }, { path, value, color }) {
      const menus = formatTreeData(state.menus, (m) => {
        if (path === m.path) {
          return Object.assign({}, m, {
            meta: Object.assign({}, m.meta, {
              badge: value,
              badgeColor: color
            })
          });
        }
        return m;
      });
      commit('setMenus', menus);
    }
  }
};
