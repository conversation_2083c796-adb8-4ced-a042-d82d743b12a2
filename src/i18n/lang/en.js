/**
 * 简体中文
 */
export default {
  // 菜单路由
  route: {
    system: {
      _name: 'System',
      user: {
        _name: 'Accounts'
      },
      role: { _name: 'Roles' },
      menu: { _name: 'Menus' },
      versions: { _name: 'Update' },
      message: {
        _name: 'Notifaction'
      }
    },
    platform: {
      _name: 'Dock Management',
      platform: { _name: 'Dock List' },
      pickup: { _name: 'Driver Registration Records' }
    },
    basics: {
      _name: 'Base Info',
      port: { _name: 'Airport' },
      address: { _name: 'Addresses' },
      loadmode: { _name: 'Loadmode' },
      agent: { _name: 'Agent' },
      bussiness: { _name: 'Bussiness' },
      transfers: { _name: 'Transfers' },
      userAddress: { _name: 'User Address' },
      seal: { _name: 'SEAL Number' },
      clearanceError: { _name: 'Clearance Error Reason' }
    },
    validity: {
      _name: 'Visual Board'
    },
    order: {
      _name: 'AWB Manage',
      awb: { _name: 'AWB Lists' },
      package: { _name: 'Package Lists' },
      box: { _name: 'Box Lists' },
      airload: { _name: 'ULD' },
      uld: { _name: 'ULD' },
      noa: { _name: 'NOA Lists' },
      packageCheck: { _name: 'Inspect Packages ' },
      billOfLadingAddress: { _name: 'Delivery address ' },
      packageXls: { _name: 'Package Export' },
      abnormalData: { _name: 'Abnormal Data' },
      missData: { _name: 'Miss Data' }
    },
    batch: {
      _name: 'Batch',
      car: { _name: 'Distribution Plan' },
      out: { _name: 'Batch' }
    },
    carlist: {
      _name: 'CMR'
    },
    storages: {
      _name: 'Storages Manage',
      list: { _name: 'Storages List' },
      inventory: { _name: 'Storages Inventory' }
    },
    exception: {
      _name: 'Error',
      403: { _name: '403' },
      404: { _name: '404' },
      500: { _name: '500' }
    },
    user: {
      _name: 'UserCenter',
      profile: { _name: 'Profile' },
      message: { _name: 'Messages' }
    },
    AbnormalHsCode: {
      _name: 'Invalid Hscode'
    },
    HighHsCode: {
      _name: 'High Value Hscode'
    },
    config: {
      _name: 'Email Setting'
    },
    dashboard: {
      analysis: {
        _name: 'AWB Report'
      },
      validity: { _name: 'Package Timeliness' },
      parcelsExcel: { _name: 'Download Center' }
    }
  },
  // 外层布局
  layout: {
    home: 'HomePage',
    header: {
      profile: 'UserCenter',
      password: 'Rest Password',
      logout: 'Sign Out'
    },
    footer: {
      website: 'Website',
      document: 'Documents',
      authorization: 'Copyrights',
      copyright: 'Copyright  2018-2024 Yunkesys'
    },
    logout: {
      title: 'Tip',
      message: 'Confirm logout?'
    },
    // 设置抽屉
    setting: {
      title: 'Theme Setting',
      sideStyles: {
        dark: 'Dark Sidebar',
        light: 'Light Sidebar'
      },
      headStyles: {
        light: 'Light Header',
        dark: 'Dark Header',
        primary: 'Primary Header'
      },
      layoutStyles: {
        side: 'Side Menu Layout',
        top: 'Top Menu Layout',
        mix: 'Mix Menu Layout'
      },
      colors: {
        default: 'Daybreak Blue',
        dust: 'Dust Blue',
        sunset: 'Sunset Orange',
        volcano: 'Volcano',
        purple: 'Golden Purple',
        cyan: 'Cyan',
        green: 'Polar Green',
        geekblue: 'Geek Blue'
      },
      darkMode: 'Dark Mode',
      layoutStyle: 'Navigation Mode',
      sideMenuStyle: 'Sidebar Double Menu',
      bodyFull: 'Body Fullscreen',
      other: 'Other Setting',
      fixedHeader: 'Fixed Header',
      fixedSidebar: 'Fixed Sidebar',
      fixedBody: 'Fixed Body',
      logoAutoSize: 'Logo Adaptation',
      colorfulIcon: 'Colorful Icon',
      sideUniqueOpen: 'Menu Unique Open',
      weakMode: 'Weak Mode',
      showFooter: 'Show Footer',
      showTabs: 'Show Tabs',
      tabStyle: 'Tab Style',
      tabStyles: {
        default: 'Default',
        dot: 'Dot',
        card: 'Card'
      },
      reset: 'Reset',
      tips: 'It will remember your configuration the next time you open it.'
    }
  },
  // 登录界面
  login: {
    title: 'Sign In',
    username: 'fill with username',
    password: 'fill with password',
    code: 'fill with captcha',
    remember: 'remember me',
    forget: 'forgot password',
    login: 'Sign In',
    loading: 'loading...',
    LoginSuccess: 'Succeed'
  },
  /**
   * 基础
   * */
  basics: {
    empty_text: 'No data',
    create: 'Create',
    delete: 'Delete',
    import: 'Import',
    export: 'Export',
    cancel: 'Cancel',
    save: 'Save',
    submit: 'Submit',
    detail: 'Detail',
    selectFile: 'Select file',
    update: 'Upload',
    edit: 'Edit',
    query: 'Search',
    reset: 'Reset',
    success: 'Succeed',
    action: 'Actions',
    action_person: 'Operator',
    confirm: 'Confirm',
    time: 'Time',
    createTime: 'Created Date',
    updateTime: 'Updated Date',
    pleaseChoose: 'Please select',
    moreAddress: 'Multiple addresses are used; Separate',
    pleaseInput: 'please enter',
    financeSetting: 'Finance Setting',
    yes: 'Yes',
    no: 'No',
    more: 'More',
    unknown: 'Not cleared',
    inspection: 'Costoms Control',
    release: 'Customs release',
    pendingClearance: 'Pending',
    status: 'Status',
    confirmDel: 'Confirm delete',
    err: {
      tips1: 'Incomplete AWB No Information',
      tips2: 'ULD exists',
      tips3: 'ULD is required'
    },
    placeholder: {
      time: '',
      time1: ''
    },
    statusWords: {
      s1: 'Not start',
      s2: 'CC in the process',
      s3: 'Finished'
    },
    beginTime: 'Begin Time',
    endTime: 'End Time',
    to: 'To',
    arrowUp: 'Expose All',
    arrowDown: 'Expand All',
    prevText: 'Prev',
    nextText: 'Next'
  },
  //系统管理
  system: {
    user: {
      username: 'Account',
      name: 'Account',
      role: 'Role',
      role_type: 'Role Type',
      nickname: 'Nickname',
      status: 'Status',
      editPass: 'Rest Password',
      financeSetting: 'Finance Settings',
      mail: 'Email',
      cmrShow: 'CMR visible type',
      cmrShows: {
        all: 'All visible',
        showDispatch: 'Delivery visible',
        showSelf: 'Pickup visible',
        no: 'All invisible'
      },
      pdaShow: 'PDA visible',
      pdaShows: {
        all: 'All',
        onlyCreate: 'Only create'
      },
      isOut: 'Define Outbound',
      isAddBatch: 'Add Batch',
      isRevoke: 'withdraw',
      password: 'Password',
      cpassword: 'Confirm Password',
      tips: {
        t1: 'Password must be 6-18 non blank characters',
        t2: 'Please enter the confirm password',
        t3: 'The passwords entered twice are inconsistent'
      },
      userCreate: {
        title: 'Create'
      },
      userEdit: {
        title: 'Edit'
      },
      userChangePassword: {
        title: 'Rest Password',
        password: 'New password',
        passwordPlaceholder: '',
        passwordConfirm: 'Confirm password',
        passwordConfirmPlaceholder: '',
        rules: {
          password: 'Password must be 6-18 non blank characters',
          password_confirm: 'Please enter the confirm password',
          no_password_confirm: 'The passwords entered twice are inconsistent'
        }
      },
      //财务暂时不处理
      financeEdit: {
        type: '计费模式_en',
        typePlaceholder: '请选择计费模式_en',
        typeList: {
          1: '全包_en',
          2: '详细_en'
        },
        all: '全包计费系数_en',
        airplane_1: '机场放货费系数_en',
        airplane_2: '机场操作费_en',
        t1: 'T1文件费_en',
        car: '提货费/还板费系数_en'
      },
      pdaInOutStorage: 'In/Out Storage'
    },
    role: {
      roleName: 'Role Name',
      roleNamePlaceholder: 'Role Name',
      roleCode: 'Role Meta',
      roleCodePlaceholder: 'Role Meta',
      assignPermission: 'Permissions'
    },
    versions: {
      versionCode: 'Version code',
      desc: 'Description',
      apk: 'apk installer',
      wgt: 'wgt installer'
    },
    message: {
      readOrNot: 'read or not',
      read: 'read',
      not: 'not'
    }
  },
  //时效信息
  validity: {
    downloadZip: 'Download',
    parcelExcelMessage:
      'Export task submitted successfully. Please check the Download Center later.',
    pleaseTypeChoose: 'Select a type',
    pleaseAreaChoose: 'Select a time range',
    exportXlsError: 'Select export filters',
    validityParcels: 'Package Timeliness',
    validityParcelsPercentage: 'Percentage',
    validityParcelsTrend: 'Trend',
    validityParcelsUnit: '(Piece)',
    validityParcelsPercentageUnit: '(%)',
    validityParcelsTrendUnit: '(hrs)',
    validityAtaTimeEmptyError: 'Landing Time Error',
    ninetyInTime: '90% package:',
    ninetyOutTime: '90% package:',
    validityPeriodTit: 'Section',
    validityLine: {
      s0: 'Timeliness',
      s1: 'Timeout Phase',
      s2: 'Timeout Reason'
    },
    validityWeeks: {
      s1: 'First week',
      s2: 'Second week',
      s3: 'Third week',
      s4: 'Four Week',
      s5: 'Fifth week',
      s6: 'Sixth week',
      s7: 'Seventh week'
    },
    validityTypes: {
      s1: 'Warehouse Processing Time',
      s2: 'Outbound Processing Time'
    },
    validityAreas: {
      s0: 'less than 0hrs',
      s1: '0-4hrs',
      s2: '4-8hrs',
      s3: '8-12hrs',
      s4: '12-18hrs',
      s5: '18-24hrs',
      s6: 'over 24hrs'
    },
    status: {
      s1: 'Processing',
      s2: 'Finished'
    },
    validityStatus: {
      s0: 'Abnormal',
      s1: 'Standard',
      s2: 'Overtime'
    },
    validityPeriodBegin: '',
    validityPeriod: {
      s1: ' week',
      s2: ' month',
      s3: ' season',
      s4: ' year'
    },
    validityBols: {
      cmr_out_hour: 'Outbound Timeliness',
      plan_hour: 'Pre-advice Window Timeliness',
      noa_pmc_hour: 'NOA Window Timeliness(PMC)',
      noa_loose_hour: 'NOA Window Timeliness(LOOSE)',
      cmr_pmc_hour: 'Pickup Scheduling Timeliness(PMC)',
      cmr_loose_hour: 'Pickup Scheduling Timeliness(LOOSE)',
      cmr_in_hour: 'Freight Station Loading Timeliness',
      edt_in_hour: 'Pre-departure Operational Timeliness',
      box_hour: 'Outbound Scheduling Timeliness',
      customs_hour: 'Customs Clearance Timeliness',
      station_hour: 'Warehouse Operation Timeliness',
      created_at: 'Create Time',
      bol_flight: 'Landing Time',
      bol_pmc_noa: 'NOA Time(PMC)',
      bol_loose_noa: 'NOA Time(LOOSE)',
      bol_customs: 'Customs Clearance Time',
      bol_cmr_in: 'Time of arrival at Warehouse',
      bol_cmr_out: 'Time of leaving  Warehouse',
      bol_cmr_station_in: 'Time of arrival at Handler',
      bol_station_in_pmc: 'Time of arrival at Handler(PMC)',
      bol_station_in_loose: 'Time of arrival at Handler(LOOSE)',
      bol_box_in: 'Box Inbound Time',
      bol_box_out: 'Box Outbound Time',
      bol_cmr_station_out: 'Time of leaving Handler',
      bol_cmr_station_out_edt: 'Time of leaving Handler(EDT)',
      box_in_last_time: 'Final Box Inbound Time',
      box_out_last_time: 'Final Box Outbound Time',
      station_in: 'Time of arrival at warehouse'
    },
    reason_name: 'Reason Classification',
    reason_mark: 'Remarks',
    reason_setting: 'Set Reason',
    reason_str: 'Reason'
  },
  //基础信息
  basicsMenu: {
    address: {
      name: 'Address',
      type: 'Type',
      code: 'Code',
      country: 'Country',
      city: 'City',
      zipcode: 'Zipcode',
      contacts: 'Contacts',
      mail: 'Email',
      tel: 'Tel',
      address: 'Detail',
      openTime: 'Opening Hours',
      financeSetting: 'Finance Settings',
      delivery: 'Delivery Address',
      station: 'Handler Address',
      tape_color: 'Tape Color'
    },
    loadmode: {
      loadmode: 'LoadMode',
      type: 'LoadType',
      status: {
        s1: 'ULD',
        s2: 'Other'
      }
    },
    agent: {
      name: 'Agent'
    },
    bussiness: {
      name: 'Business Name'
    },
    port: {
      name: 'Port'
    },
    transfers: {
      name: 'Transportation Company'
    }
  },
  awb: {
    abnormal: {
      disTime1: 'From landing to NOA',
      disTime2: 'Loading Time',
      disTime3: 'From landing to warehouse entry',
      disTime4: 'EDT Unloading Time',
      disInBox: 'Inbound Count Discrepancy',
      disOutBox: 'Outbound Count Discrepancy'
    },
    analysis: {
      hour: ' Hour',
      time_slot: 'Duration',
      lastTimeTitle: 'Last ',
      date: 'Date',
      bol_count: 'Awb Count',
      box_count: 'Box Count',
      weight: 'Weight (t)',
      awbTitle: 'AWB'
    },
    noa: {
      time: 'NOA Time',
      awbno: 'AWB No',
      boxCount: 'BoxNum',
      weight: 'Weight',
      loadmode: 'LoadMode',
      pmc: 'PMC',
      address: 'Handler Address',
      sendMail: 'Send Email',
      is_determine: 'Inbounded',
      determine: 'StockIn'
    },

    package: {
      parcel_status_err: 'Package status does not allow outbound',
      changeCmr_err: 'Not a single outbound package, cannot change CMR',
      changeCmr: 'Change CMR',
      no: 'PackageNo',
      check: 'Clearance Status',
      check_operate: 'Costoms Control',
      double_check: 'Double Check',
      notice_custom: 'Notify Customs',
      customs_inspection: 'Customs Inspection',
      customs_inspection_data: 'Customs Inspection Date',
      take_delivery: 'Customs Checking',
      mission: 'Mission',
      declaration: 'Declaration Type',
      permit_through: 'Customs Release',
      permit_through_date: 'Customs Release Date',
      notice_custom_date: 'Notice Time',
      physical_controlled_date: 'Physical Controlled Date',
      take_delivery_date: 'Pick Up Time',
      control_mail_date: 'Control Mail Date',
      access_warehouse: 'Stock Status',
      pending_out_time: 'Pending Time',
      access_warehouse_status: {
        1: 'UnInbounded',
        2: 'Inbounded',
        3: 'OutBounded'
      },
      status: {
        s1: 'Default',
        s2: 'Missing',
        s3: 'Scaned',
        s4: 'OutBounded'
      },
      btn1: 'Set Status',
      btn2: 'Double Check (Multi)',
      btn3: 'Check Information',
      edit: 'Edit',
      clearStatus: 'Clearance',
      mrn: 'MRN',
      bol_batch: 'Bol Batch',
      address_code: 'Address Code'
    },
    box: {
      box: 'BoxSn',
      stock_in_time: 'Inbound Date',
      stock_shelt_time: 'Shelt Date',
      stock_out_time: 'Outbound Date',
      status: {
        s1: 'Not Picked up',
        s2: 'Reserved',
        s3: 'Inbounded',
        s4: 'OutBounded'
      },
      tips: {
        t1: 'Confirm change the address?'
      },
      clearStatus: 'Clearance',
      editAdd: 'Edit Address',
      is_one_parcel: 'Single Package',
      bol_no: 'AWB No'
    },
    awb: {
      batch: 'Batches',
      t1: 'T1 Agent',
      agent: 'Clearance Agent',
      port: 'Airport',
      col1: 'Business Type',
      col2: 'Address Num',
      col3: 'Estimated Box Num',
      col4: 'Quantity Difference',
      col5: 'Inventory Box Num',
      col6: 'Chargeable Weight',
      col7: 'HV Package Num',
      col8: 'Label Exchange',
      col9: 'User',
      col10: 'FlightNo',
      col11: 'Duty Fee',
      col12: 'Customs Inspection Fee',
      col13: 'Customs Clearance Fee',
      col14: 'Storage Fee',
      col15: 'Release Note Fee',
      col16: 'COVID-19 Surcharge',
      col17: 'Estimated Takeof Time',
      col18: 'Estimated Landing Time',
      col19: 'Remark',
      col20: 'Departure Time',
      col21: 'Landing Time',
      status: {
        s1: 'Not Picked Up', //s1: 'Not Picked up',s2: 'Partial Inbounded',s3: 'All Inbounded',s4: 'Partial OutBounded',s5: 'All OutBounded'
        s2: 'Partial Inbounded',
        s3: 'All Inbounded',
        s4: 'All OutBounded',
        s5: 'Finished',
        s6: 'Partial OutBounded'
      },
      tips: {
        t1: 'Drop files here or ',
        t2: 'Click to upload',
        t3: 'Only xlsx files,',
        t30: 'Only xls、xlsx files,',
        t4: 'Download Template',
        t5: 'Please Upload Manifest',
        t6: 'Please Upload Manifest',
        t7: 'Drop file here or ',
        t8: 'Confirm examine?',
        t9: 'Confirm examine?',
        t10: 'Confirm delete the AWB No',
        t11: 'FlightNo is required',
        t12: 'Chargeable Weight is required',
        t13: 'Estimated Takeof Time is required',
        t14: 'Estimated Landing Time is required',
        tips_btn: 'OK',
        stock_in_tips1: 'Tip: ',
        stock_in_tips2: 'The manifest has been scanned into the warehouse.'
      },
      title1: 'Import Check Information',
      title2: 'Import Manifest',
      title3: 'Import Flight Info',
      export: 'Finance Export',
      export_type: '财务导出类型',
      export_types: {
        export1: '导出费用',
        export2: '导出成本'
      },
      times: {
        time1: 'Manifest Approved',
        time2: 'Flight Departure Time',
        time3: 'Flight Landing Time',
        time4: 'Clearance Finished Time',
        time5: 'NOA Accepts Time',
        time6: 'Inbound Time',
        time7: 'Outbound Time',
        time8: '配送完成',
        time9: 'Inbound',
        time10: 'OutBound',
        time11: 'Arrivaled'
      },
      action_types: {
        1: 'Manifest Upload',
        2: 'Manifest Information Supplement',
        3: '上传提货',
        4: 'Clearance Finished',
        5: '取货约车',
        6: 'Stockin',
        7: '上传车单信息',
        8: 'Edit CMR Information',
        9: '发货约车',
        10: 'Stock Out',
        11: '上传配送车单信息',
        12: '财务确认车单',
        13: '财务确认计费重量',
        14: 'Edit Timeline',
        15: 'Send IST Email',
        16: 'Check AWB',
        17: 'Stock To Storage'
      },
      clearTime: 'Clearance Time',
      clearStartTime: 'Start Time',
      clearStatus: 'Clearance',
      is_stock: 'Inventory',
      in_storage: 'Inbound Status',
      in_storages: {
        s1: 'Not Picked Up',
        s2: 'Partial Inbounded',
        s3: 'All Inbounded'
      },
      menu: {
        m1: 'Timeline',
        m2: 'Edit Timeline',
        m3: 'Action Logs',
        m4: 'Download Manifest',
        m5: 'Download POD',
        m55: 'POD Detail',
        m6: 'Confirm',
        m7: 'Delete',
        m8: 'Clearance Success',
        m9: 'Examine',
        m10: 'Start Clearance'
      },
      see: 'Info',
      forecastNumber: 'Estimated Box Num',
      receiptQuantity: 'Inbounded Box Num',
      deliveryQuantity: 'OutBounded Box Num'
    },
    air: {
      no: 'Not Returned',
      yes: 'Returned'
    }
  },
  car: {
    btn1: 'Set Delivery Cost',
    btn2: 'Set Delivery Cost',
    btn3: 'Cancel Set',
    timeWords: {
      air1: 'Time of arrival at Warehouse',
      air2: 'Time of leaving  Warehouse',
      air3: 'Time of arrival at Handler',
      air4: 'Time of leaving Handler',
      in1: 'Time of arrival at Handler',
      in2: 'Time of leaving Handler',
      in3: 'Time of arrival at Warehouse',
      in4: 'Time of leaving  Warehouse',
      out1: 'Time of arrival at Warehouse',
      out2: 'Time of leaving  Warehouse',
      out3: 'Arrivaled Time',
      out4: 'Leaving Time'
    },
    tips: {
      err1: 'CMR is required',
      tips1: 'Can not ReEdit after deletion',
      tips2: 'Delete CMR?',
      tips3: 'Can not recovered after deletion',
      tips4: 'Returned ULD ',
      tips5: 'Invalid AWB No code'
    },
    cmrType: 'Delivery Type',
    cmrTypes: {
      dispatch: 'Delivery',
      self: 'Pickup'
    },
    type: 'CMR Type',
    in: 'Inbound',
    out: 'OutBound',
    air: 'Return ULD',
    no: 'Unconfirmed',
    yes: 'Confirmed',
    transfers: 'Transportation',
    finance: 'Finance',
    address: 'Address',
    addressIn: 'Handler Address',
    addressOut: 'Delivery Address',
    addressReturn: 'Return Address',
    download: 'Download CMR',
    viewVehicleList: 'CMR Detail',
    uploadVehicleList: 'Upload CMR',
    add: 'Add ULD',
    add1: 'Add AWB',
    add2: 'Add Other',
    confirmFinance: 'Finance Confirm',
    airno: 'ULD Code',
    traynum: 'Tray Num',
    actual_weight: 'Actual Weight',
    invoiceNo: 'Invoice Number',
    pincode_ref: 'Pincode Ref',
    deliveryFee: 'Delivery Fee',
    additionalCosts: 'Extra Fee',
    deliveryAddress: 'Handler Address',
    deliveryAddress2: 'Delivery Address',
    totalExpenses: 'Amount',
    amortizedExpenses: 'Cost Sharing',
    userName: 'Account',
    trayNum: 'Tray Num',
    airType: 'LoadMode',
    grossweight: 'Gross Weight',
    grossweight2: 'Gross Weight',
    boxNum: 'BoxNum',
    tips2: 'Please select the CMR type',
    tips3: 'POD file is required',
    tips4: 'CMR file is required',
    exportFlatExpenses: 'Export Cost Sharing',
    batchUploadCost: 'Batch upload of costs',
    viewvoucher: 'POD Detail',
    uploadvoucher: 'Upload POD',
    uploadvouchermsg: 'Drop/Drag',
    uploadvoucherlistmsg: 'Drop/Drag',
    truck_order: 'Truck Ref',
    print: 'Print',
    print_cmr: 'Print CMR',
    print_pdf: 'Print PDF',
    lock_btn: 'Lock',
    un_lock_btn: 'Unlock',
    amount: 'Amount',
    unit: 'Unit'
  },
  batch: {
    car: {
      create1: 'New Plan',
      create2: 'New Plan(Customs AWB No)',
      name: 'Delivery Plan',
      reference: 'Delivery Plan Code',
      truck_order: 'Truck Ref',
      plate_number: 'CarNumber',
      estimated_loading_time: 'Estimated Load Date',
      loadTime: 'Load Date',
      statusWord: 'Load Status',
      remark: 'Remark',
      awb_stock_info: 'Stock Details',
      status_remark: 'Load Remark',
      btn1: 'Load Complete',
      user_name: 'Creator',
      out_name: 'Outbound',
      out_time: 'Outbound Date',
      status: {
        s1: 'UnLoaded',
        s2: 'Loaded'
      }
    },
    out: {
      name: 'Batch No',
      status: {
        s1: 'Default',
        s2: 'OutBounded'
      },
      boxnum: 'Box Count',
      packagenum: 'Package Count',
      checknum: 'Checked Count'
    }
  },
  message: {
    content: 'Message',
    time: 'Date',
    info: 'Detail',
    cancel: 'Cancel',
    action: 'Actions',
    boxtype: 'Type',
    label1: 'All Inbounded',
    label2: 'All OutBounded',
    label3: 'Import Check Information',
    label4: 'Customs Checking',
    label5: 'Customs Release',
    label6: 'Customs Inspection',
    label7: 'Edit Address',
    label8: 'Import Check Information'
  },
  userAddress: {
    user_addresses: 'User AddrCode'
  },
  orderBoxBatchEdit: 'The box is outbounded, cant change address',
  carList: {
    aviationBoard: {
      removeAll: 'Remove All',
      addAll: 'Add All',
      add: 'Add',
      remove: 'Remove',
      selectedSubTitle: 'Selected PMCs',
      selectedEmptyText: 'Unselected PMCs'
    },
    order_num: 'Other Order Number'
  },
  order: {
    awb: {
      totalValue: 'Total Value',
      grossWeight: 'Gross Weight',
      lowValueManifest: 'Manifest(H7)',
      highValueManifest: 'Manifest(High Value)',
      bol_pdf_path: 'AWB POD',
      highValueManifestMin: 'Please upload Manifest',
      nameErr: 'Duplicate manifest file name',
      check_tips: 'Checked:',
      check_tips_title: 'Checked',
      check_box_tips: 'Single Box Inspection:',
      check_box_tips_title: 'Single Box Inspection',
      sendMail: {
        AddresseeA: 'Recipient Email',
        addresses: 'Handler',
        istNumber: 'IST number',
        estimatedArrivalDate: 'Estimated arrival date'
      },
      batchDownloadPDF: 'Batch Download PDF',
      selectPathNull: 'select File Path is Null',
      declarationLink: 'Submit Clearance',
      is_emergency: 'Is Emergency',
      emergency_code: 'Emergency Code',
      declarationInfo: 'Confirm Submit Clearance',
      ist_number: 'Ist number',
      t1_number: 'Warehouse Inbound T1 Number',
      consignee_country: 'Consignee Country',
      box_in_max_time: 'The Last Warehouse inbound Time',
      box_out_max_time: 'The Last Warehouse Outbound Time'
    },
    package: {
      customsClearanceResults: 'CC Results',
      historicalExamination: 'Historical Inspection',
      failedToClearCustoms: 'CC Blocked',
      failedToClearCustomsData: 'Time of CC Blocked',
      failedToClearCustomsTakeDelivery: 'CC Blocked (taken by customs)',
      failedToClearCustomsTakeDeliveryWarehouseRetention:
        'CC Blocked (left in warehouse)',
      scanStatus: 'Clearance Parcel Scan Status',
      boxChecked: 'Single box inspection',
      uncleared: 'Not Cleared',
      packageReference: 'package Reference',
      packageReferenceOld: 'raw Package Reference',
      packageReferenceNew: 'edit Package Reference',
      selectNull: 'select is Null',
      selectCheckError: 'select Data Check is Error'
    },
    uid: {
      comments: 'Remark',
      setComments: 'Remark'
    },
    noa: {
      importName: 'Import NOA Info'
    },
    abnormalHsCode: {
      nomenclature: 'Invalid Hscode',
      excluded_for_ni_on_code: 'excludedForNiOnCode',
      remarks: 'Remarks'
    },
    awbExp: {
      col1: 'Batch number',
      col2: 'Shipping time',
      col3: 'MAWB number',
      col4: 'Cartons expected',
      col5: 'Total pcs',
      col6: 'G.W.(kg)',
      col7: 'Chargeable weight',
      col8: 'T1 Agent',
      col9: 'Custom Agent',
      col10: 'Flight',
      col11: 'Destination',
      col111: 'Delivery Address',
      col12: 'Business Type',
      col13: 'ETD',
      col14: 'ETA',
      col15: 'ATD',
      col16: 'ATA（Local time）',
      col17: 'Customs clearance time',
      col18: 'NOA/ATB',
      col19: 'Transfer time from airport',
      col20: 'Arrival time at warehouse',
      col21: 'Cartons received',
      col22: 'Discrepancy',
      col23: 'Inventory',
      col24: 'Pallets',
      col25: 'Handover time',
      col26: 'Cartons Handover',
      col27: 'The last unloading at destination',
      col271: 'Checked parcels',
      col28: 'Number of HV parcels',
      col29: 'Status',
      col291: 'Remark',
      col30: 'Customs clearance status',
      col31: 'Financial confirmation',
      col32: 'Client',
      col33: 'Create time',
      freight_depot: 'Freight Depot'
    },
    billOfLadingAddress: {
      box_in_last_time: 'Last time the box was stored',
      box_out_last_time: 'Last time the box left the warehouse'
    }
  },
  config: {
    depart_time: 'Depart Time(minute)',
    host: 'Email',
    username: 'Email Account',
    password: 'Email Password',
    reply_to: 'Reply Email',
    from: 'Sender',
    from_name: 'Sender account',
    addressee: 'Import/delete AWB recipients',
    istMailRecipientsL: 'IST Mail Recipients',
    otherIstMailRecipientsL: 'Other IST Recipients',
    fraRecipientMailbox: 'Flight Port" as FRA Recipients'
  },
  platform: {
    platform: {
      name: 'Dock Name'
    },
    pickup: {
      platform_nomal: '--',
      create_date: 'Date',
      trailer_plate: 'Trailer Plate',
      type: 'Type',
      types: {
        s1: 'Van',
        s2: 'Truck'
      },
      sort: 'Sort',
      cmr: 'CMR',
      name: 'Driver Name',
      car_number: 'Truck Plate',
      gsname: 'Company',
      tel: 'Tel Number',
      platform_id: 'Dock Name',
      mate_at: 'Arrival Time',
      leave_at: 'Departure Time',
      countdown: 'Countdown',
      address_1: 'Address',
      address_2: 'Add Address',
      address_3: 'Add Address(Other)',
      tips: 'Delete?',
      confirm: 'Confirm Departure',
      copy: 'Copy'
    }
  },
  seal: {
    seal_no: 'SEAL Number',
    use_at: 'Operating Time',
    begin_num: 'Begin Number',
    end_num: 'End Number',
    pad_zero: 'Zero Length',
    statusWords: {
      s1: 'Not Operated',
      s2: 'Operated'
    }
  },
  clearanceError: {
    error_value: 'Clearance Error',
    error_value_en: 'Clearance Error EN',
    selectedEmpty: 'Unselected'
  },
  storages: {
    storage_name: 'Storage Name',
    add_storage: 'Add Storage',
    edit_storage: 'Edit Storage',
    del_storage: 'Delete Storage',
    del_storage_tips: 'Confirm Delete?',
    launch_time: 'Shelt Date'
  }
};
