/**
 * 简体中文
 */
export default {
  // 菜单路由
  route: {
    system: {
      _name: '系统管理',
      user: {
        _name: '账号管理'
      },
      role: { _name: '角色管理' },
      menu: { _name: '菜单管理' },
      versions: { _name: '版本更新' },
      message: {
        _name: '系统消息'
      }
    },
    platform: {
      _name: '月台管理',
      platform: { _name: '月台列表' },
      pickup: { _name: '司机登记记录' }
    },
    basics: {
      _name: '基础信息',
      port: { _name: '航班港口' },
      address: { _name: '地址管理' },
      loadmode: { _name: '装载模式' },
      agent: { _name: '代理列表' },
      bussiness: { _name: '业务类型' },
      transfers: { _name: '运输公司' },
      userAddress: { _name: '用户地址管理' },
      seal: { _name: 'SEAL号码' },
      clearanceError: { _name: '清关失败原因' }
    },
    validity: {
      _name: '时效看板'
    },
    order: {
      _name: '提单管理',
      awb: { _name: '提单列表' },
      package: { _name: '包裹列表' },
      box: { _name: '箱号列表' },
      airload: { _name: '航空板列表' },
      uld: { _name: '航空板列表' },
      noa: { _name: 'NOA列表' },
      packageCheck: { _name: '查验包裹列表' },
      billOfLadingAddress: { _name: '提单派送地址' },
      packageXls: { _name: '包裹导出' },
      abnormalData: { _name: '异常数据' },
      missData: { _name: '缺失数据' }
    },
    batch: {
      _name: '出仓批次',
      car: { _name: '派送计划' },
      out: { _name: '出仓批次' }
    },
    carlist: {
      _name: '车单管理'
    },
    storages: {
      _name: '库存管理',
      list: { _name: '库位列表' },
      inventory: { _name: '库存列表' }
    },
    exception: {
      _name: '异常页面',
      403: { _name: '403' },
      404: { _name: '404' },
      500: { _name: '500' }
    },
    user: {
      _name: '个人中心',
      profile: { _name: '个人资料' },
      message: { _name: '我的消息' }
    },
    AbnormalHsCode: {
      _name: '禁用Hscode'
    },
    HighHsCode: {
      _name: '高价值Hscode'
    },
    config: {
      _name: '邮件配置'
    },
    dashboard: {
      analysis: {
        _name: '提单报表'
      },
      validity: { _name: '包裹时效' },
      parcelsExcel: { _name: '下载中心' }
    }
  },
  // 外层布局
  layout: {
    home: '主页',
    header: {
      profile: '个人中心',
      password: '修改密码',
      logout: '退出登录'
    },
    footer: {
      website: '官网',
      document: '文档',
      authorization: '授权',
      copyright: 'Copyright  2018-2024 Yunkesys'
    },
    logout: {
      title: '提示',
      message: '确定要退出登录吗?'
    },
    // 设置抽屉
    setting: {
      title: '整体风格设置',
      sideStyles: {
        dark: '暗色侧边栏',
        light: '亮色侧边栏'
      },
      headStyles: {
        light: '亮色顶栏',
        dark: '暗色顶栏',
        primary: '主色顶栏'
      },
      layoutStyles: {
        side: '左侧菜单布局',
        top: '顶部菜单布局',
        mix: '混合菜单布局'
      },
      colors: {
        default: '拂晓蓝',
        dust: '薄暮',
        sunset: '日暮',
        volcano: '火山',
        purple: '酱紫',
        cyan: '明青',
        green: '极光绿',
        geekblue: '极客蓝'
      },
      darkMode: '开启暗黑模式',
      layoutStyle: '导航模式',
      sideMenuStyle: '侧栏双排菜单',
      bodyFull: '内容区域铺满',
      other: '其它配置',
      fixedHeader: '固定顶栏区域',
      fixedSidebar: '固定侧栏区域',
      fixedBody: '固定主体区域',
      logoAutoSize: 'Logo宽度自动',
      colorfulIcon: '侧栏彩色图标',
      sideUniqueOpen: '侧栏排他展开',
      weakMode: '开启色弱模式',
      showFooter: '开启全局页脚',
      showTabs: '开启多页签栏',
      tabStyle: '页签显示风格',
      tabStyles: {
        default: '默认',
        dot: '圆点',
        card: '卡片'
      },
      reset: '重置',
      tips: '该功能可实时预览各种布局效果, 修改后会缓存在本地, 下次打开会记忆主题配置.'
    }
  },
  // 登录界面
  login: {
    title: '用户登录',
    username: '请输入登录账号',
    password: '请输入登录密码',
    code: '请输入验证码',
    remember: '记住密码',
    forget: '忘记密码',
    login: '登录',
    loading: '登录中',
    LoginSuccess: '登录成功'
  },
  /**
   * 基础
   * */
  basics: {
    empty_text: '暂无数据',
    create: '新建',
    delete: '删除',
    import: '导入',
    export: '导出',
    cancel: '取消',
    save: '保存',
    submit: '提交',
    detail: '详情',
    selectFile: '选取文件',
    update: '上传',
    edit: '编辑',
    query: '查询',
    reset: '重置',
    success: '操作成功',
    action: '操作',
    action_person: '操作人',
    confirm: '确认',
    time: '时间',
    createTime: '创建时间',
    updateTime: '更新时间',
    pleaseChoose: '请选择',
    moreAddress: '多个地址用;隔开',
    pleaseInput: '请输入',
    financeSetting: '财务配置',
    yes: '是',
    no: '否',
    more: '更多',
    unknown: '未清关',
    inspection: '查验',
    release: '放行',
    pendingClearance: 'Pending',
    status: '状态',
    confirmDel: '确定要删除？',
    err: {
      tips1: '请填写完整提单信息',
      tips2: '不能填写重复的航空版号',
      tips3: '请选择航空板号'
    },
    placeholder: {
      time: '请选择日期',
      time1: '请选择时间'
    },
    statusWords: {
      s1: '未开始',
      s2: '清关中',
      s3: '已完成'
    },
    beginTime: '开始时间',
    endTime: '结束时间',
    to: '至',
    arrowUp: '收起',
    arrowDown: '展开',
    prevText: '上一页',
    nextText: '下一页'
  },
  //系统管理
  system: {
    user: {
      username: '用户账号',
      name: '用户账号',
      role: '角色',
      role_type: '角色类型',
      nickname: '昵称',
      status: '状态',
      editPass: '修改密码',
      financeSetting: '财务设置',
      mail: '邮箱',
      cmrShow: 'CMR可见类型',
      cmrShows: {
        all: '全部可见',
        showDispatch: '派送可见',
        showSelf: '自提可见',
        no: '全部不可见'
      },
      pdaShow: 'PDA可见范围',
      pdaShows: {
        all: '全部',
        onlyCreate: '仅创建'
      },
      isOut: '自定义出库',
      isAddBatch: '添加新批次',
      isRevoke: '出库撤回',
      password: '登录密码',
      cpassword: '确认密码',
      tips: {
        t1: '密码必须为6-18位非空白字符',
        t2: '请输入确认密码',
        t3: '两次输入的密码不一致'
      },
      userCreate: {
        title: '创建用户'
      },
      userEdit: {
        title: '修改用户'
      },
      userChangePassword: {
        title: '修改密码',
        password: '新密码',
        passwordPlaceholder: '请输入新密码',
        passwordConfirm: '确认密码',
        passwordConfirmPlaceholder: '请再次输入新密码',
        rules: {
          password: '请输入新密码',
          password_confirm: '请再次输入新密码',
          no_password_confirm: '两次输入密码不一致'
        }
      },
      //财务暂时不处理
      financeEdit: {
        type: '计费模式',
        typePlaceholder: '请选择计费模式',
        typeList: {
          1: '全包',
          2: '详细'
        },
        all: '全包计费系数',
        airplane_1: '机场放货费系数',
        airplane_2: '机场操作费',
        t1: 'T1文件费',
        car: '提货费/还板费系数'
      },
      pdaInOutStorage: '入库/移库'
    },
    role: {
      roleName: '角色名称',
      roleNamePlaceholder: '请输入角色名称',
      roleCode: '角色标识',
      roleCodePlaceholder: '请输入角色标识',
      assignPermission: '分配权限'
    },
    versions: {
      versionCode: '版本号',
      desc: '描述',
      apk: 'apk安装包',
      wgt: 'wgt安装包'
    },
    message: {
      readOrNot: '是否已读',
      read: '已读',
      not: '未读'
    }
  },
  //时效信息
  validity: {
    downloadZip: '下载',
    parcelExcelMessage: '导出任务已提交，稍后可在下载中心查看。',
    pleaseTypeChoose: '请选择类型',
    pleaseAreaChoose: '请选择区间',
    exportXlsError: '请选择导出条件',
    validityParcels: '包裹时效',
    validityParcelsPercentage: '百分比',
    validityParcelsTrend: '趋势图',
    validityParcelsUnit: '(个)',
    validityParcelsPercentageUnit: '(%)',
    validityParcelsTrendUnit: '(hrs)',
    validityAtaTimeEmptyError: '请选择实际落地时间',
    ninetyInTime: '90%包裹为:',
    ninetyOutTime: '90%包裹为:',
    validityPeriodTit: '区间',
    validityLine: {
      s0: '库操完成时效',
      s1: '超时环节',
      s2: '超时原因'
    },
    validityLineList: {
      s1: '提单派送地址维度信息统计',
      s2: '库操时效列表'
    },
    validityWeeks: {
      s1: '第一周',
      s2: '第二周',
      s3: '第三周',
      s4: '第四周',
      s5: '第五周',
      s6: '第六周',
      s7: '第七周'
    },
    validityTypes: {
      s1: '库操完成时效',
      s2: '出库时效'
    },
    validityAreas: {
      s0: '小于0hrs',
      s1: '0-4hrs',
      s2: '4-8hrs',
      s3: '8-12hrs',
      s4: '12-18hrs',
      s5: '18-24hrs',
      s6: '24hrs以上'
    },
    status: {
      s1: '未完成',
      s2: '已完成'
    },
    validityStatus: {
      s0: '异常',
      s1: '达标',
      s2: '超时'
    },
    validityPeriodBegin: '第',
    validityPeriod: {
      s1: '周',
      s2: '月',
      s3: '季',
      s4: '年'
    },
    validityBols: {
      cmr_out_hour: '出库时效',
      plan_hour: '预报段时效',
      noa_pmc_hour: 'NOA段时效(PMC)',
      noa_loose_hour: 'NOA段时效(LOOSE)',
      cmr_pmc_hour: '提货调度段时效(PMC)',
      cmr_loose_hour: '提货调度段时效(LOOSE)',
      cmr_in_hour: '货站装车段时效',
      edt_in_hour: '货站到EDT段时效',
      box_hour: '出库调度段时效',
      customs_hour: '清关段时效',
      station_hour: '库操段时效',
      created_at: '创建时间',
      bol_flight: '实际落地时间',
      bol_pmc_noa: 'NOA时间(PMC)',
      bol_loose_noa: 'NOA时间(LOOSE)',
      bol_customs: '清关完成',
      bol_cmr_in: '到达仓库时间',
      bol_cmr_out: '离开仓库时间',
      bol_cmr_station_in: '到达货站时间',
      bol_station_in_pmc: '到达货站时间(PMC)',
      bol_station_in_loose: '到达货站时间(LOOSE)',
      bol_box_in: '箱子入库时间',
      bol_box_out: '箱子出库时间',
      bol_cmr_station_out: '离开货站时间',
      bol_cmr_station_out_edt: '离开货站时间(EDT)',
      box_in_last_time: '最后扫描入库时间',
      box_out_last_time: '最后扫描出库时间',
      station_in: '到达仓库时间'
    },
    reason_name: '超时原因分类',
    reason_mark: '超时原因备注',
    reason_setting: '设置超时原因',
    reason_str: '超时原因'
  },
  //基础信息
  basicsMenu: {
    address: {
      name: '地址名称',
      type: '地址类型',
      code: '地址代码',
      country: '国家',
      city: '城市',
      zipcode: '邮编',
      contacts: '联系人',
      mail: '邮箱',
      tel: '电话',
      address: '地址',
      openTime: '开门时间',
      financeSetting: '财务设置',
      delivery: '配送地址',
      station: '货站地址',
      tape_color: '胶带颜色'
    },
    loadmode: {
      loadmode: '装载模式',
      type: '装载类型',
      status: {
        s1: '航空版',
        s2: '其他'
      }
    },
    agent: {
      name: '代理名称'
    },
    bussiness: {
      name: '业务名称'
    },
    port: {
      name: '港口名称'
    },
    transfers: {
      name: '运输公司'
    }
  },
  awb: {
    abnormal: {
      disTime1: '落地到NOA',
      disTime2: '货站装车时长',
      disTime3: '落地到入仓',
      disTime4: 'EDT卸车时长',
      disInBox: '入仓件数异常',
      disOutBox: '出仓件数异常'
    },
    analysis: {
      hour: '小时',
      time_slot: '时间段',
      lastTimeTitle: '最近',
      date: '日期',
      bol_count: '提单数',
      box_count: '箱数',
      weight: '吨数',
      awbTitle: '提单数据'
    },
    noa: {
      time: 'NOA时间',
      awbno: '提单号',
      boxCount: '箱数',
      weight: '重量',
      loadmode: '装载模式',
      pmc: '位置/PMC',
      address: '货站地址',
      sendMail: '发送邮件',
      is_determine: '是否入仓',
      determine: '入仓'
    },
    package: {
      changeCmr_err: '不是单独出库的包裹，不能更换CMR',
      changeCmr: '修改CMR',
      parcel_status_err: '包裹状态不允许出库',
      out: '出库',
      no: '包裹号',
      check: '清关状态',
      check_operate: '查验',
      double_check: '二次检查',
      notice_custom: '通知海关',
      customs_inspection: '海关查验',
      customs_inspection_data: '海关查验时间',
      take_delivery: '海关取走',
      mission: '任务编号',
      declaration: '申报类型',
      permit_through: '放行',
      permit_through_date: '放行时间',
      notice_custom_date: '通知海关时间',
      physical_controlled_date: '海关检查日期',
      take_delivery_date: '海关取走时间',
      control_mail_date: '通知查验日期',
      access_warehouse: '出入仓状态',
      pending_out_time: 'Pending时间',
      access_warehouse_status: {
        1: '未入仓',
        2: '已入仓',
        3: '已出仓'
      },
      status: {
        s1: '未知',
        s2: '缺失',
        s3: '已扫描',
        s4: '已出仓'
      },
      btn1: '设置包裹查验状态',
      btn2: '二次检查（多选）',
      btn3: '查验信息',
      edit: '编辑',
      clearStatus: '清关状态',
      mrn: 'MRN',
      bol_batch: '批次号',
      address_code: '地址CODE'
    },
    box: {
      box: '箱号',
      stock_in_time: '入仓时间',
      stock_shelt_time: '上架时间',
      stock_out_time: '出仓时间',
      status: {
        s1: '等待提货',
        s2: '已约车',
        s3: '已到仓',
        s4: '已出仓'
      },
      tips: {
        t1: '修改地址后，相应的包裹地址将自动修改，是否确定？'
      },
      clearStatus: '清关状态',
      editAdd: '修改地址',
      is_one_parcel: '单包裹',
      bol_no: 'AWB'
    },
    awb: {
      batch: '批次',
      t1: 'T1代理',
      agent: '清关代理',
      port: '航班港口',
      col1: '业务类型',
      col2: '地址数量',
      col3: '预报箱数',
      col4: '差异箱数',
      col5: '库存箱数',
      col6: '计费重量',
      col7: '高价值包裹数',
      col8: '换标数',
      col9: '所属用户',
      col10: '航班号',
      col11: '税费',
      col12: '查验费',
      col13: '清关费',
      col14: '仓储费',
      col15: '放货费',
      col16: '新冠附加费',
      col17: '预计起飞时间',
      col18: '预计落地时间',
      col19: '备注',
      col20: '实际起飞时间',
      col21: '实际落地时间',
      status: {
        s1: '等待提货',
        s2: '部分到仓',
        s3: '全部到仓',
        s4: '全部出仓',
        s5: '已完成',
        s6: '部分出仓'
      },
      tips: {
        t1: '将文件拖到此处, 或 ',
        t2: '点击上传',
        t3: '只能上传xlsx文件,',
        t30: '只能上传xls、xlsx文件,',
        t4: '下载模板',
        t5: '请上传Manifest',
        t6: '请上传提单',
        t7: '将提单拖到此处, 或',
        t8: '是否确认审核提单？',
        t9: '确认提单之后不可编辑，是否确定？',
        t10: '确定要删除提单？',
        t11: '请输入航班号',
        t12: '请输入计费重量',
        t13: '请选择预计起飞时间',
        t14: '请选择预计落地时间',
        tips_btn: '确定',
        stock_in_tips1: '注意：',
        stock_in_tips2: '提单号已经扫描入仓',
        pending_in_tips: '包裹是Pending状态'
      },
      title1: '导入查验信息',
      title2: '导入提单',
      title3: '导入航班信息',
      export: '财务导出',
      export_type: '财务导出类型',
      export_types: {
        export1: '导出费用',
        export2: '导出成本'
      },
      times: {
        time1: '通过审核',
        time2: '航班起飞',
        time3: '航班落地',
        time4: '清关完成',
        time5: '收到NOA',
        time6: '到达仓库',
        time7: '货物出仓',
        time8: '配送完成',
        time9: '入仓',
        time10: '出仓',
        time11: '到达目的地'
      },
      action_types: {
        1: '上传提单',
        2: '补充提单信息',
        3: '上传提货',
        4: '清关完成',
        5: '取货约车',
        6: '仓库收货',
        7: '上传车单信息',
        8: '更新车单信息',
        9: '发货约车',
        10: '仓库出仓',
        11: '上传配送车单信息',
        12: '财务确认车单',
        13: '财务确认计费重量',
        14: '编辑时间线',
        15: '发送IST邮件',
        16: '审核提单',
        17: '库位上架'
      },
      clearTime: '清关时间',
      clearStartTime: '清关开始时间',
      clearStatus: '清关状态',
      is_stock: '是否有库存',
      in_storage: '入仓状态',
      in_storages: {
        s1: '等待提货',
        s2: '部分到仓',
        s3: '全部到仓'
      },
      menu: {
        m1: '时间线',
        m2: '编辑时间线',
        m3: '操作日志',
        m4: '下载Manifest',
        m5: '下载凭证',
        m55: '查看凭证',
        m6: '确认提单',
        m7: '删除提单',
        m8: '清关完成',
        m9: '审核',
        m10: '开始清关'
      },
      see: '查看',
      forecastNumber: '预报箱数',
      receiptQuantity: '入仓箱数',
      deliveryQuantity: '出仓箱数'
    },
    air: {
      no: '未还',
      yes: '已还'
    }
  },
  car: {
    btn1: '设置配送成本（多选）',
    btn2: '设置配送成本',
    btn3: '取消设置（多选）',
    timeWords: {
      air1: '到达仓库时间',
      air2: '离开仓库时间',
      air3: '到达货站时间',
      air4: '离开货站时间',
      in1: '到达货站时间',
      in2: '离开货站时间',
      in3: '到达仓库时间',
      in4: '离开仓库时间',
      out1: '到达仓库时间',
      out2: '离开仓库时间',
      out3: '到达目的地时间',
      out4: '离开目的地时间'
    },
    tips: {
      err1: '请选择车单',
      tips1: '确定要确认该车单信息，确认之后不可编辑？',
      tips2: '确定要删除该车单信息，确认之后不可恢复？',
      tips3: '删除后保存将不可恢复',
      tips4: '已还板',
      tips5: '不要提交两条相同的提单'
    },
    cmrType: '派送类型',
    cmrTypes: {
      dispatch: '派送',
      self: '自提'
    },
    type: '车单类型',
    in: '入仓',
    out: '出仓',
    air: '还板',
    no: '未确认',
    yes: '已确认',
    transfers: '运输公司',
    finance: '财务',
    address: '地址名称',
    addressIn: '提货地址',
    addressOut: '派送地址',
    addressReturn: '还板地址',
    download: '下载车单',
    viewVehicleList: '查看车单',
    uploadVehicleList: '上传车单',
    add: '增加板号信息',
    add1: '增加提单',
    add2: '增加其他',
    confirmFinance: '财务确认',
    airno: '航空板号',
    traynum: '托盘数',
    actual_weight: '实际重量',
    invoiceNo: '发票号',
    pincode_ref: '成本编码',
    deliveryFee: '派送费用',
    additionalCosts: '额外费用',
    deliveryAddress: '提货地址',
    deliveryAddress2: '派送地址',
    totalExpenses: '总费用',
    amortizedExpenses: '平摊费用',
    userName: '用户名称',
    trayNum: '托盘数量',
    airType: '装载模式',
    grossweight: '毛重',
    grossweight2: '毛重',
    boxNum: '箱数',
    tips2: '请选择车单类型并查询',
    tips3: '请上传凭证',
    tips4: '请上传车单',
    tips5: '导出文件必须至少有两个搜索条件',
    exportFlatExpenses: '导出平摊费用',
    batchUploadCost: '批量上传成本',
    viewvoucher: '查看凭证',
    uploadvoucher: '上传凭证',
    uploadvouchermsg: '将PDF/图片拖到此处，或 点击上传',
    uploadvoucherlistmsg: '将图片拖到此处，或 点击上传',
    truck_order: '卡车订单号',
    print: '打印',
    print_cmr: '打印车单',
    print_pdf: '打印PDF',
    lock_btn: '锁定',
    un_lock_btn: '解锁',
    amount: '数量',
    unit: '单位'
  },
  batch: {
    car: {
      create1: '新建(自动提单)',
      create2: '新建(自选提单)',
      name: '派送计划',
      reference: '派送码',
      plate_number: '车牌号',
      truck_order: '卡车订单号',
      estimated_loading_time: '预计装载日期',
      loadTime: '装载日期',
      statusWord: '装载状态',
      remark: '备注',
      awb_stock_info: '当前地址对应的库存情况',
      status_remark: '装载备注',
      btn1: '完成装载',
      user_name: '创建人',
      out_name: '出库人',
      out_time: '出库时间',
      status: {
        s1: '未装载',
        s2: '已装载'
      }
    },
    out: {
      name: '批次号',
      status: {
        s1: '等待出仓',
        s2: '已完成'
      },
      boxnum: '箱数',
      packagenum: '包裹数',
      checknum: '查验数'
    }
  },
  message: {
    content: '内容',
    time: '时间',
    info: '详情',
    cancel: '取消',
    action: '操作',
    boxtype: '类型',
    label1: '全部入仓',
    label2: '全部出仓',
    label3: '导入查验信息',
    label4: '海关取走',
    label5: '放行',
    label6: '海关查验',
    label7: '修改地址',
    label8: '导入查验信息'
  },
  userAddress: {
    user_addresses: '用户地址代码'
  },
  orderBoxBatchEdit: '箱子已出仓不能更换地址',
  carList: {
    parcel_out_btn: '出库包裹',
    aviationBoard: {
      removeAll: '全部移除',
      addAll: '全部添加',
      add: '添加',
      remove: '移除',
      selectedSubTitle: '已选航空板号',
      selectedEmptyText: '未选择航空板号'
    },
    order_num: '其他单号'
  },
  order: {
    awb: {
      totalValue: '总价值',
      grossWeight: '毛重',
      lowValueManifest: 'Manifest(低价值)',
      highValueManifest: 'Manifest(高价值)',
      bol_pdf_path: '提单凭证',
      highValueManifestMin: '请上传Manifest',
      nameErr: 'Manifest名称重复',
      check_tips: '有查验包裹：',
      check_tips_title: '查验包裹',
      check_box_tips: '单箱查验：',
      check_box_tips_title: '单箱查验',
      sendMail: {
        AddresseeA: '收件人邮箱',
        addresses: '货站',
        istNumber: 'IST号码',
        estimatedArrivalDate: '预计到仓日期'
      },
      batchDownloadPDF: '批量下载凭证',
      selectPathNull: '所选提单凭证路径为空',
      declarationLink: '提交清关信息',
      is_emergency: '是否紧急模式',
      emergency_code: '紧急模式code',
      declarationInfo: '确认提交清关信息',
      ist_number: '提单IST号码',
      t1_number: '入仓T1号码',
      consignee_country: '收件人目的地国家',
      box_in_max_time: '最后入仓时间',
      box_out_max_time: '最后出仓时间'
    },
    package: {
      customsClearanceResults: '清关结果',
      historicalExamination: '历史查验',
      failedToClearCustoms: '清关失败',
      failedToClearCustomsData: '清关失败时间',
      failedToClearCustomsTakeDelivery: '清关失败(海关取走)',
      failedToClearCustomsTakeDeliveryWarehouseRetention: '清关失败(留仓)',
      scanStatus: '清关包裹扫描状态',
      boxChecked: '单箱查验',
      uncleared: '未清关',
      packageReference: '包裹编号',
      packageReferenceOld: '原包裹编号',
      packageReferenceNew: '修改包裹编号',
      selectNull: '选择为空',
      selectCheckError: '选择数据状态错误'
    },
    uid: {
      comments: '备注',
      setComments: '修改'
    },
    noa: {
      importName: '导入NOA信息'
    },
    abnormalHsCode: {
      nomenclature: '禁用HSCODE',
      excluded_for_ni_on_code: 'excludedForNiOnCode',
      remarks: '备注'
    },
    awbExp: {
      col1: '批次',
      col2: '发货时间',
      col3: '主单号（MAWB)',
      col4: '预计到达箱数',
      col5: '包裹件数',
      col6: '毛重kg',
      col7: '计费重量kg',
      col8: 'T1 代理',
      col9: '清关代理',
      col10: '航班号',
      col11: '航班港口',
      col111: '派送地址',
      col12: '业务类型',
      col13: '预计起飞时间',
      col14: '预计到达时间',
      col15: '实际起飞时间',
      col16: '实际落地时间',
      col17: '清关完成时间',
      col18: 'NOA时间',
      col19: '口岸转出时间（卡车到货站提货的时间）',
      col20: '到达仓库时间',
      col21: '实到箱数',
      col22: '差异箱数',
      col23: '库存箱数',
      col24: '托盘数',
      col25: '出仓时间',
      col26: '出仓箱数',
      col27: '目的仓最后接收货物时间',
      col271: '查验包裹数量',
      col28: '高价值包裹数',
      col29: '状态',
      col291: '备注',
      col30: '清关状态',
      col31: '财务确认',
      col32: '所属用户',
      col33: '创建时间',
      freight_depot: '货站'
    },
    billOfLadingAddress: {
      box_in_last_time: '最后入库时间',
      box_out_last_time: '最后出库时间'
    }
  },
  config: {
    depart_time: '离开计时(分)',
    host: '邮件地址',
    username: '邮件用户名',
    password: '邮件密码',
    reply_to: '回复通知邮箱',
    from: '发件人',
    from_name: '发件人用户名',
    addressee: '导入/删除提单收件人',
    istMailRecipientsL: 'IST邮件收件人',
    otherIstMailRecipientsL: '其他IST收件人',
    fraRecipientMailbox: '航班港口”为FRA收件人邮箱'
  },
  platform: {
    platform: {
      name: '月台名称'
    },
    pickup: {
      platform_nomal: '无',
      create_date: '日期',
      name: '司机姓名',
      car_number: '卡车牌照',
      trailer_plate: '拖车底板',
      type: '卡车类型',
      types: {
        s1: '小型卡车',
        s2: '大型卡车'
      },
      sort: '排序',
      cmr: 'CMR',
      gsname: '公司名称',
      tel: '电话号码',
      platform_id: '月台名称',
      mate_at: '分配时间',
      leave_at: '离开时间',
      countdown: '倒计时',
      address_1: '地址',
      address_2: '添加地址',
      address_3: '添加地址（其他）',
      tips: '确定要删除该记录，确认后不可恢复？',
      confirm: '确认离开',
      copy: '复制'
    }
  },
  seal: {
    seal_no: 'SEAL号码',
    use_at: '使用时间',
    begin_num: '开始号码',
    end_num: '结束号码',
    pad_zero: '补零长度',
    statusWords: {
      s1: '未使用',
      s2: '已使用'
    }
  },
  clearanceError: {
    error_value: '清关失败原因',
    error_value_en: '英文清关失败原因',
    selectedEmpty: '未选择数据'
  },
  storages: {
    storage_name: '库位名称',
    add_storage: '添加库位',
    edit_storage: '修改库位',
    del_storage: '删除库位',
    del_storage_tips: '确定要删除该记录, 删除后不可恢复?',
    launch_time: '上架时间'
  }
};
