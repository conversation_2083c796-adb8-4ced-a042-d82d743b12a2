<!-- 修改密码弹窗 -->
<template>
  <ele-modal
    width="480px"
    :title="$t('system.user.userChangePassword.title')"
    :visible="visible"
    :append-to-body="true"
    :close-on-click-modal="false"
    @update:visible="updateVisible"
    @closed="onClose"
  >
    <el-form
      ref="form"
      :model="form"
      :rules="rules"
      label-width="140px"
      @keyup.enter.native="save"
    >
      <!-- <el-form-item label="旧密码:" prop="oldPassword">
        <el-input
          show-password
          v-model="form.password_current"
          placeholder="请输入旧密码"
        />
      </el-form-item> -->
      <el-form-item :label="`${$t('system.user.userChangePassword.password')}:`" prop="password">
        <el-input
          show-password
          onkeyup="this.value=this.value.replace(/(^\s*)|(\s*$)/g,'')"
          v-model="form.password"
          :placeholder="$t('system.user.userChangePassword.passwordPlaceholder')"
        />
      </el-form-item>
      <el-form-item :label="`${$t('system.user.userChangePassword.passwordConfirm')}:`" prop="password_confirm">
        <el-input
          show-password
          onkeyup="this.value=this.value.replace(/(^\s*)|(\s*$)/g,'')"
          v-model="form.password_confirm"
          :placeholder="
            $t('system.user.userChangePassword.passwordConfirmPlaceholder')
          "
        />
      </el-form-item>
    </el-form>
    <div slot="footer">
      <el-button @click="updateVisible(false)">{{$t('basics.cancel')}}</el-button>
      <el-button type="primary" @click="save">{{$t('basics.save')}}</el-button>
    </div>
  </ele-modal>
</template>

<script>
  import { repassword } from '@/api/common';
  const i18k = 'system.user.userChangePassword';
  export default {
    name: 'PasswordModal',
    props: {
      visible: Boolean
    },
    data() {
      return {
        // 按钮loading
        loading: false,
        // 表单数据
        form: {
          password_current: '',
          password: '',
          password_confirm: ''
        },
        // 表单验证
        rules: {
          password_current: [
            {
              required: true,
              message: this.$t(`${i18k}.rules.password`),
              trigger: 'blur'
            }
          ],
          password: [
            {
              required: true,
              message: this.$t(`${i18k}.rules.password`),
              trigger: 'blur'
            }
          ],
          password_confirm: [
            {
              required: true,
              trigger: 'blur',
              validator: (_rule, value, callback) => {
                if (!value) {
                    return callback(
                        new Error(this.$t(`${i18k}.rules.password_confirm`) ?? '')
                    );
                }
                if (value !== this.form.password) {
                    return callback(
                        new Error(
                            this.$t(`${i18k}.rules.no_password_confirm`) ?? ''
                        )
                    );
                }
                callback();
              }
            }
          ]
        }
      };
    },
    methods: {
      /* 修改visible */
      updateVisible(value) {
        this.$emit('update:visible', value);
      },
      /* 保存修改 */
      save() {
        this.$refs['form'].validate((valid) => {
          if (valid) {
            this.loading = true;
            repassword(this.form)
              .then((msg) => {
                this.loading = false;
                this.$message.success(this.$t('basics.success'));
                this.updateVisible(false);
              })
              .catch((e) => {
                this.loading = false;
                this.$message.error(e);
              });
          } else {
            return false;
          }
        });
      },
      /* 关闭回调 */
      onClose() {
        this.form = {
          password_current: '',
          password: '',
          password_confirm: ''
        };
        this.$refs['form'].resetFields();
        this.loading = false;
      }
    }
  };
</script>
