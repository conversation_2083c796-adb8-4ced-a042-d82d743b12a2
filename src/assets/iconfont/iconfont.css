@font-face {
  font-family: "iconfont"; /* Project id 3783686 */
  src: url('iconfont.woff2?t=1669017145313') format('woff2'),
       url('iconfont.woff?t=1669017145313') format('woff'),
       url('iconfont.ttf?t=1669017145313') format('truetype');
}

.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-mudedi:before {
  content: "\e89d";
}

.icon-chuku:before {
  content: "\e602";
}

.icon-ruku:before {
  content: "\e606";
}

.icon-chuhuofanghangshenqingliucheng:before {
  content: "\e64f";
}

.icon-qifei:before {
  content: "\e6b1";
}

.icon-jiangluo:before {
  content: "\e6b2";
}

.icon-shenhetongguo:before {
  content: "\e613";
}

.icon-camera:before {
  content: "\e601";
}

