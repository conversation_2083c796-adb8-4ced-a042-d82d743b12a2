<template>
  <div id="app">
    <router-view />
  </div>
</template>

<script>
  export default {
    name: 'App',
    created() {
      // 恢复主题
      this.$store.dispatch('theme/recoverTheme');
    }
  };
</script>
<style>
  .ele-admin-header .ele-admin-logo > img {
    height: auto !important;
  }

  .el-submenu > div {
    font-weight: bolder !important;
  }
  .el-menu-item .el-tooltip {
    font-weight: bolder !important;
  }

  @font-face {
    font-family: 'iconfont';
    src: url('@/assets/iconfont/iconfont.woff2?t=1669017145313') format('woff2'),
      url('@/assets/iconfont/iconfont.woff?t=1669017145313') format('woff'),
      url('@/assets/iconfont/iconfont.ttf?t=1669017145313') format('truetype');
  }
  .iconfont {
    font-family: 'iconfont' !important;
    font-size: 16px;
    font-style: normal;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }
</style>
