/**
 * axios实例
 */
import axios from 'axios';
import router from '@/router';
import { MessageBox } from 'element-ui';
import { API_BASE_URL, TOKEN_HEADER_NAME, I18N_CACHE_NAME , LANG_HEADER_NAME } from '@/config/setting';
import { getToken, setToken } from './token-util';
import { logout } from './page-tab-util';

const service = axios.create({
  baseURL: API_BASE_URL
});

// 添加请求拦截器
service.interceptors.request.use(
  (config) => {
    // 添加token到header
    const token = getToken();
    if (token && config.headers) {
      config.headers.common[TOKEN_HEADER_NAME] = token;
    }
    // console.log(localStorage.getItem(I18N_CACHE_NAME) || 'zh_CN')
    config.headers.common[LANG_HEADER_NAME] = localStorage.getItem(I18N_CACHE_NAME) || 'zh_CN'
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 添加响应拦截器
service.interceptors.response.use(
  (res) => {
    // 登录过期处理
    if (res.data?.code === '40000') {
      const currentPath = router.currentRoute.path;
      if (currentPath === '/') {
        logout(true);
      } else {
        // MessageBox.alert('登录状态已过期, 请退出重新登录!', '系统提示', {
        //   confirmButtonText: '重新登录',
        //   callback: (action) => {
        //     if (action === 'confirm') {
        //       logout(false, currentPath);
        //     }
        //   },
        //   beforeClose: () => {
        //     MessageBox.close();
        //   }
        // });
      }
      return Promise.reject(new Error(res.data.message));
    }
    // token自动续期
    const token = res.headers[TOKEN_HEADER_NAME.toLowerCase()];
    if (token) {
      setToken(token);
    }
    return res.data;
  },
  (error) => {
    // console.log(error.response)
    if(error.response.status == 401){
	  if((localStorage.getItem(I18N_CACHE_NAME) || 'zh_CN')== 'zh_CN'){
          MessageBox.alert('登录状态已过期, 请退出重新登录!', '系统提示', {
            confirmButtonText: '重新登录',
            callback: (action) => {
              if (action === 'confirm') {
                logout(false, '/');
              }
            },
            beforeClose: () => {
              MessageBox.close();
            }
          });
      }else{
          MessageBox.alert('Login status has expired, please exit and login again!', 'System Error', {
            confirmButtonText: 'Re Login',
            callback: (action) => {
              if (action === 'confirm') {
                logout(false, '/');
              }
            },
            beforeClose: () => {
              MessageBox.close();
            }
          });
      }
    }
    console.log(error)
    // if(error.response.data.err_column){
    //   return Promise.reject(error.response.data.err_msg)
    // }else{
    //   return Promise.reject(error.response.data.err_msg)
    // }
    return Promise.reject(error.response.data.err_msg)
  }
);

export default service;
