/**
 * 常用封装
 */

import message from 'element-ui/packages/message';
import { hasPermission } from '@/utils/permission';
import i18n from '@/i18n';

/**
 * 解析列表数据
 * @param res 接口返回'
 */
export function parsingPagingData(res) {
  return {
    list: res.result,
    count: res.meta.total
  }
  // return {
  //   list: res.result,
  //   count: res.meta.total
  // };

  // return Promise.reject(new Error(res.data.message));
}
/**
 * 解析列表数据不分页
 * @param res 接口返回'
 */
export function parsingPagingDataNoPage(res) {
  return res.result
  // return {
  //   list: res.result,
  //   count: res.meta.total
  // };

  // return Promise.reject(new Error(res.data.message));
}
export function parsingPagingDataNoPageLogs(res) {
  return res.result.log
  // return {
  //   list: res.result,
  //   count: res.meta.total
  // };

  // return Promise.reject(new Error(res.data.message));
}

/**
 * 解析正常数据
 * @param res 接口返回'
 * @param callback 成功回调
 * @param returnKey
 */
export function parseNormalData(
  res,
  callback = false,
  returnKey = (res) => {
    return res.data.err_msg;
  }
) {
  res
  .then((res) => {
    callback ? callback(res.result) : false;
    return returnKey(res);
  })
  .catch((e) => {
    message.error(e.data.err_msg);
    return Promise.reject(new Error(e.data.err_msg));
  });
  
}

/**
 * 获取级别
 * @returns {number}
 * @param treeData
 * @param i
 * @param Level
 * @param idKey
 * @param parentIdKey
 */
export function getLevel(
  treeData,
  i,
  Level = 1,
  idKey = 'id',
  parentIdKey = 'parent_id'
) {
  const parentItem = treeData.find((item) => {
    return item[idKey] === i[parentIdKey];
  });
  if (!parentItem || parentItem['parentIdKey'] === '0') {
    return parentItem && parentItem['parentIdKey'] === '0' ? ++Level : Level;
  }
  return getLevel(treeData, parentItem, ++Level, idKey, parentIdKey);
}

/**
 * 判断如果没权限 删除操作按钮
 */
export function processingOperations(
  self,
  has,
  columnsFun = (key) => {
    return key.prop === 'action';
  }
) {
  if (!hasPermission(has)) {
    const index = self.columns.findIndex(columnsFun);
    self.columns.splice(index, index + 1);
  }
}

/**
 * 处理列表数据多语言
 * */
export function handleListName(
  columnsData,
  key,
  nameKey = ['prop', 'columnKey']
) {
  let i18nList = { ...i18n.t('basics'), ...i18n.t(key) };
  return columnsData.map((i) => {
    if (!Array.isArray(nameKey) && !i[nameKey]) return i;
    if (!Array.isArray(nameKey))
      return {
        ...i,
        label: i[nameKey] ? i18nList[i[nameKey]] : i.label
      };
    key = nameKey.find((v) => {
      // eslint-disable-next-line no-prototype-builtins
      return i.hasOwnProperty(v);
    });
    return {
      ...i,
      label: i18nList[i[key]] ?? i.label
    };
  });
}
